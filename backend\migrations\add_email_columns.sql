-- =====================================================
-- EMAIL VERIFICATION MIGRATION
-- =====================================================
-- This migration adds email verification columns to the users table
-- and creates the email_logs table for tracking email delivery

-- Add email verification columns to users table if they don't exist
DO $$ 
BEGIN
    -- Add email_verification_token column
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns 
                   WHERE table_name = 'users' AND column_name = 'email_verification_token') THEN
        ALTER TABLE users ADD COLUMN email_verification_token TEXT;
        RAISE NOTICE 'Added email_verification_token column to users table';
    ELSE
        RAISE NOTICE 'email_verification_token column already exists';
    END IF;

    -- Add email_verification_expires column
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns 
                   WHERE table_name = 'users' AND column_name = 'email_verification_expires') THEN
        ALTER TABLE users ADD COLUMN email_verification_expires TIMESTAMP WITH TIME ZONE;
        RAISE NOTICE 'Added email_verification_expires column to users table';
    ELSE
        RAISE NOTICE 'email_verification_expires column already exists';
    END IF;

    -- Add is_email_verified column
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns 
                   WHERE table_name = 'users' AND column_name = 'is_email_verified') THEN
        ALTER TABLE users ADD COLUMN is_email_verified BOOLEAN DEFAULT FALSE;
        RAISE NOTICE 'Added is_email_verified column to users table';
    ELSE
        RAISE NOTICE 'is_email_verified column already exists';
    END IF;
END $$;

-- Create email_logs table if it doesn't exist
CREATE TABLE IF NOT EXISTS email_logs (
    id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    user_id UUID REFERENCES users(id) ON DELETE SET NULL,
    recipient VARCHAR(255) NOT NULL,
    subject VARCHAR(500) NOT NULL,
    message_id VARCHAR(255), -- SendGrid message ID
    status VARCHAR(20) DEFAULT 'sent' CHECK (status IN ('sent', 'delivered', 'opened', 'clicked', 'bounced', 'failed')),
    error_message TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create indexes for email_logs table if they don't exist
CREATE INDEX IF NOT EXISTS idx_email_logs_recipient ON email_logs(recipient);
CREATE INDEX IF NOT EXISTS idx_email_logs_created_at ON email_logs(created_at);
CREATE INDEX IF NOT EXISTS idx_email_logs_status ON email_logs(status);

-- Enable RLS on email_logs table
ALTER TABLE email_logs ENABLE ROW LEVEL SECURITY;

-- Create policy for email_logs table
DO $$
BEGIN
    IF NOT EXISTS (SELECT 1 FROM pg_policies WHERE tablename = 'email_logs' AND policyname = 'Enable read access for service role') THEN
        CREATE POLICY "Enable read access for service role" ON email_logs FOR ALL USING (true);
        RAISE NOTICE 'Created RLS policy for email_logs table';
    ELSE
        RAISE NOTICE 'RLS policy for email_logs already exists';
    END IF;
END $$;

-- Create cleanup function for old email logs if it doesn't exist
CREATE OR REPLACE FUNCTION cleanup_old_email_logs()
RETURNS INTEGER AS $$
DECLARE
    deleted_count INTEGER;
BEGIN
    DELETE FROM email_logs WHERE created_at < NOW() - INTERVAL '30 days';
    GET DIAGNOSTICS deleted_count = ROW_COUNT;
    RETURN deleted_count;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Update existing users to have is_email_verified = false if null
UPDATE users SET is_email_verified = FALSE WHERE is_email_verified IS NULL;

-- Add email index to users table if it doesn't exist
CREATE INDEX IF NOT EXISTS idx_users_email ON users(email);
CREATE INDEX IF NOT EXISTS idx_users_is_email_verified ON users(is_email_verified);

SELECT 'Email verification migration completed successfully!' as message;
