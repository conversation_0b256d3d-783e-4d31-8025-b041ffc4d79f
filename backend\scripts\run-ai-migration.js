/**
 * PayVendy AI Brain Database Migration Script
 * 
 * Runs the AI Brain database migration to create necessary tables
 * and functions for AI operations.
 */

const fs = require('fs').promises;
const path = require('path');
const pool = require('../config/database');
const logger = require('../utils/logger');

async function runAIMigration() {
    console.log('🧠 Starting AI Brain database migration...');
    
    try {
        // Read the migration file
        const migrationPath = path.join(__dirname, '../database/migrations/007_ai_brain_tables.sql');
        const migrationSQL = await fs.readFile(migrationPath, 'utf8');
        
        console.log('📄 Migration file loaded successfully');
        
        // Execute the migration
        console.log('⚡ Executing migration...');
        await pool.query(migrationSQL);
        
        console.log('✅ AI Brain migration completed successfully!');
        
        // Verify tables were created
        const tableCheck = await pool.query(`
            SELECT table_name 
            FROM information_schema.tables 
            WHERE table_schema = 'public' 
            AND table_name IN (
                'ai_logs', 
                'behavior_analytics', 
                'user_segments', 
                'reward_queue', 
                'realtime_events', 
                'fraud_alerts', 
                'ai_configuration'
            )
            ORDER BY table_name
        `);
        
        console.log('📊 Created tables:');
        tableCheck.rows.forEach(row => {
            console.log(`  ✓ ${row.table_name}`);
        });
        
        // Verify functions were created
        const functionCheck = await pool.query(`
            SELECT routine_name 
            FROM information_schema.routines 
            WHERE routine_schema = 'public' 
            AND routine_name IN ('apply_reward', 'cleanup_old_events', 'cleanup_old_ai_data')
            ORDER BY routine_name
        `);
        
        console.log('🔧 Created functions:');
        functionCheck.rows.forEach(row => {
            console.log(`  ✓ ${row.routine_name}()`);
        });
        
        // Check AI configuration
        const configCheck = await pool.query('SELECT config_key, config_value FROM ai_configuration ORDER BY config_key');
        
        console.log('⚙️  AI Configuration:');
        configCheck.rows.forEach(row => {
            console.log(`  • ${row.config_key}: ${row.config_value}`);
        });
        
        console.log('\n🎉 AI Brain database setup complete!');
        console.log('💡 You can now start the AI Brain service.');
        
    } catch (error) {
        console.error('❌ Migration failed:', error);
        logger.error('AI Brain migration error:', error);
        process.exit(1);
    } finally {
        await pool.end();
    }
}

// Run if called directly
if (require.main === module) {
    runAIMigration().catch(error => {
        console.error('Fatal error:', error);
        process.exit(1);
    });
}

module.exports = { runAIMigration };