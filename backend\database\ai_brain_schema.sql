-- =====================================================
-- AI BRAIN DATABASE SCHEMA EXTENSION
-- =====================================================
-- Additional tables for AI brain autonomous operations
-- Run this after the main schema.sql

-- AI Logs table for tracking AI decisions and actions
CREATE TABLE IF NOT EXISTS ai_logs (
    id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    user_id UUID REFERENCES users(id) ON DELETE CASCADE,
    action_type VARCHAR(50) NOT NULL, -- 'reward_calculated', 'behavior_analyzed', 'segment_updated', etc.
    description TEXT NOT NULL,
    input_data JSONB DEFAULT '{}'::jsonb,
    output_data JSONB DEFAULT '{}'::jsonb,
    confidence_score DECIMAL(3,2), -- 0.00 to 1.00
    processing_time_ms INTEGER,
    model_version VARCHAR(20),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Reward Queue table for AI-generated rewards
CREATE TABLE IF NOT EXISTS reward_queue (
    id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    user_id UUID REFERENCES users(id) ON DELETE CASCADE,
    reward_type VARCHAR(50) NOT NULL, -- 'cashback', 'bonus', 'discount', 'loyalty_points'
    amount DECIMAL(15,2) NOT NULL CHECK (amount > 0),
    reason TEXT NOT NULL,
    trigger_event VARCHAR(100), -- 'transaction_milestone', 'loyalty_threshold', 'behavior_pattern'
    status VARCHAR(20) DEFAULT 'pending' CHECK (status IN ('pending', 'applied', 'expired', 'cancelled')),
    expires_at TIMESTAMP WITH TIME ZONE,
    applied_at TIMESTAMP WITH TIME ZONE,
    metadata JSONB DEFAULT '{}'::jsonb,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- User Segments table for AI-driven user categorization
CREATE TABLE IF NOT EXISTS user_segments (
    id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    user_id UUID REFERENCES users(id) ON DELETE CASCADE,
    segment_name VARCHAR(50) NOT NULL, -- 'high_value', 'at_risk', 'new_user', 'power_user', etc.
    segment_score DECIMAL(5,2) NOT NULL, -- Score determining fit for this segment
    attributes JSONB DEFAULT '{}'::jsonb, -- Additional segment-specific data
    last_updated TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    expires_at TIMESTAMP WITH TIME ZONE, -- When this segmentation should be re-evaluated
    UNIQUE(user_id, segment_name)
);

-- Behavior Analytics table for storing user behavior patterns
CREATE TABLE IF NOT EXISTS behavior_analytics (
    id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    user_id UUID REFERENCES users(id) ON DELETE CASCADE,
    analysis_type VARCHAR(50) NOT NULL, -- 'spending_pattern', 'usage_frequency', 'risk_assessment'
    period_start TIMESTAMP WITH TIME ZONE NOT NULL,
    period_end TIMESTAMP WITH TIME ZONE NOT NULL,
    metrics JSONB NOT NULL, -- Calculated metrics and patterns
    insights JSONB DEFAULT '{}'::jsonb, -- AI-generated insights
    recommendations JSONB DEFAULT '{}'::jsonb, -- AI recommendations
    confidence_level DECIMAL(3,2), -- 0.00 to 1.00
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Real-time Events table for AI to process events as they happen
CREATE TABLE IF NOT EXISTS realtime_events (
    id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    user_id UUID REFERENCES users(id) ON DELETE CASCADE,
    event_type VARCHAR(50) NOT NULL, -- 'transaction_completed', 'login', 'app_opened', 'balance_low'
    event_data JSONB NOT NULL,
    processed BOOLEAN DEFAULT FALSE,
    processing_started_at TIMESTAMP WITH TIME ZONE,
    processed_at TIMESTAMP WITH TIME ZONE,
    ai_actions JSONB DEFAULT '[]'::jsonb, -- Array of actions taken by AI
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- AI Model Performance table for tracking model accuracy and performance
CREATE TABLE IF NOT EXISTS ai_model_performance (
    id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    model_name VARCHAR(50) NOT NULL,
    model_version VARCHAR(20) NOT NULL,
    metric_name VARCHAR(50) NOT NULL, -- 'accuracy', 'precision', 'recall', 'f1_score'
    metric_value DECIMAL(5,4) NOT NULL,
    test_data_size INTEGER,
    evaluation_date TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    notes TEXT
);

-- Indexes for AI Brain tables
CREATE INDEX IF NOT EXISTS idx_ai_logs_user_id ON ai_logs(user_id);
CREATE INDEX IF NOT EXISTS idx_ai_logs_action_type ON ai_logs(action_type);
CREATE INDEX IF NOT EXISTS idx_ai_logs_created_at ON ai_logs(created_at);

CREATE INDEX IF NOT EXISTS idx_reward_queue_user_id ON reward_queue(user_id);
CREATE INDEX IF NOT EXISTS idx_reward_queue_status ON reward_queue(status);
CREATE INDEX IF NOT EXISTS idx_reward_queue_expires_at ON reward_queue(expires_at);

CREATE INDEX IF NOT EXISTS idx_user_segments_user_id ON user_segments(user_id);
CREATE INDEX IF NOT EXISTS idx_user_segments_segment_name ON user_segments(segment_name);
CREATE INDEX IF NOT EXISTS idx_user_segments_expires_at ON user_segments(expires_at);

CREATE INDEX IF NOT EXISTS idx_behavior_analytics_user_id ON behavior_analytics(user_id);
CREATE INDEX IF NOT EXISTS idx_behavior_analytics_analysis_type ON behavior_analytics(analysis_type);
CREATE INDEX IF NOT EXISTS idx_behavior_analytics_period ON behavior_analytics(period_start, period_end);

CREATE INDEX IF NOT EXISTS idx_realtime_events_user_id ON realtime_events(user_id);
CREATE INDEX IF NOT EXISTS idx_realtime_events_processed ON realtime_events(processed);
CREATE INDEX IF NOT EXISTS idx_realtime_events_event_type ON realtime_events(event_type);
CREATE INDEX IF NOT EXISTS idx_realtime_events_created_at ON realtime_events(created_at);

CREATE INDEX IF NOT EXISTS idx_ai_model_performance_model ON ai_model_performance(model_name, model_version);
CREATE INDEX IF NOT EXISTS idx_ai_model_performance_date ON ai_model_performance(evaluation_date);

-- Triggers for updated_at timestamps on AI tables
CREATE TRIGGER update_reward_queue_updated_at BEFORE UPDATE ON reward_queue
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- RLS policies for AI Brain tables (allow service role access)
ALTER TABLE ai_logs ENABLE ROW LEVEL SECURITY;
ALTER TABLE reward_queue ENABLE ROW LEVEL SECURITY;
ALTER TABLE user_segments ENABLE ROW LEVEL SECURITY;
ALTER TABLE behavior_analytics ENABLE ROW LEVEL SECURITY;
ALTER TABLE realtime_events ENABLE ROW LEVEL SECURITY;
ALTER TABLE ai_model_performance ENABLE ROW LEVEL SECURITY;

-- Policies: Users can view their own AI-related data
CREATE POLICY "Users can view own AI logs" ON ai_logs
    FOR SELECT USING (auth.uid()::text = user_id::text);

CREATE POLICY "Users can view own rewards" ON reward_queue
    FOR SELECT USING (auth.uid()::text = user_id::text);

CREATE POLICY "Users can view own segments" ON user_segments
    FOR SELECT USING (auth.uid()::text = user_id::text);

CREATE POLICY "Users can view own behavior analytics" ON behavior_analytics
    FOR SELECT USING (auth.uid()::text = user_id::text);

-- Functions for AI Brain operations
CREATE OR REPLACE FUNCTION apply_reward(
    user_uuid UUID,
    reward_amount DECIMAL(15,2),
    reward_reason TEXT,
    reward_reference VARCHAR(100)
)
RETURNS BOOLEAN AS $$
DECLARE
    success BOOLEAN;
BEGIN
    -- Apply the reward as a credit to user balance
    SELECT update_user_balance(
        user_uuid,
        reward_amount,
        'credit',
        'AI Reward: ' || reward_reason,
        reward_reference
    ) INTO success;
    
    -- Update reward queue status if successful
    IF success THEN
        UPDATE reward_queue 
        SET status = 'applied', applied_at = NOW(), updated_at = NOW()
        WHERE user_id = user_uuid 
        AND amount = reward_amount 
        AND reason = reward_reason
        AND status = 'pending';
    END IF;
    
    RETURN success;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

CREATE OR REPLACE FUNCTION get_user_behavior_score(user_uuid UUID)
RETURNS DECIMAL(5,2) AS $$
DECLARE
    transaction_score DECIMAL(5,2) := 0;
    frequency_score DECIMAL(5,2) := 0;
    recency_score DECIMAL(5,2) := 0;
    total_score DECIMAL(5,2);
BEGIN
    -- Calculate transaction volume score (last 30 days)
    SELECT LEAST(100, COUNT(*) * 2) INTO transaction_score
    FROM transactions 
    WHERE user_id = user_uuid 
    AND created_at >= NOW() - INTERVAL '30 days'
    AND status = 'completed';
    
    -- Calculate frequency score
    SELECT LEAST(100, COUNT(DISTINCT DATE(created_at)) * 5) INTO frequency_score
    FROM transactions 
    WHERE user_id = user_uuid 
    AND created_at >= NOW() - INTERVAL '30 days'
    AND status = 'completed';
    
    -- Calculate recency score
    SELECT CASE 
        WHEN MAX(created_at) >= NOW() - INTERVAL '7 days' THEN 100
        WHEN MAX(created_at) >= NOW() - INTERVAL '14 days' THEN 75
        WHEN MAX(created_at) >= NOW() - INTERVAL '30 days' THEN 50
        ELSE 25
    END INTO recency_score
    FROM transactions 
    WHERE user_id = user_uuid 
    AND status = 'completed';
    
    -- Weighted average
    total_score := (transaction_score * 0.4) + (frequency_score * 0.4) + (recency_score * 0.2);
    
    RETURN ROUND(total_score, 2);
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Function to cleanup old AI data
CREATE OR REPLACE FUNCTION cleanup_old_ai_data()
RETURNS INTEGER AS $$
DECLARE
    deleted_count INTEGER := 0;
    temp_count INTEGER;
BEGIN
    -- Clean up old AI logs (keep last 90 days)
    DELETE FROM ai_logs WHERE created_at < NOW() - INTERVAL '90 days';
    GET DIAGNOSTICS temp_count = ROW_COUNT;
    deleted_count := deleted_count + temp_count;
    
    -- Clean up expired rewards
    DELETE FROM reward_queue WHERE status = 'pending' AND expires_at < NOW();
    GET DIAGNOSTICS temp_count = ROW_COUNT;
    deleted_count := deleted_count + temp_count;
    
    -- Clean up old processed events (keep last 30 days)
    DELETE FROM realtime_events WHERE processed = TRUE AND created_at < NOW() - INTERVAL '30 days';
    GET DIAGNOSTICS temp_count = ROW_COUNT;
    deleted_count := deleted_count + temp_count;
    
    -- Clean up old behavior analytics (keep last 180 days)
    DELETE FROM behavior_analytics WHERE created_at < NOW() - INTERVAL '180 days';
    GET DIAGNOSTICS temp_count = ROW_COUNT;
    deleted_count := deleted_count + temp_count;
    
    RETURN deleted_count;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;
