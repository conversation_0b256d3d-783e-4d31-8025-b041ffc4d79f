import { Platform } from 'react-native';
import DeviceInfo from 'react-native-device-info';
import { useAppStore, subscribeToAuthChanges, subscribeToNetworkChanges } from '../../shared/utils/store';
import { featureFlags } from './featureFlagService';
import { performanceService } from './performanceService';
import { crashReporting } from './crashReportingService';
import { secureStorage } from '../security/secureStorageService';
import { apiService } from '../api/apiService';
import { logger } from './productionLogger';
import performanceMonitor from './performanceMonitor';

// Type definitions
interface DeviceInfoType {
  deviceId: string;
  appVersion: string;
  buildNumber: string;
  platform: 'ios' | 'android';
  osVersion: string;
  deviceModel: string;
  deviceBrand: string;
  isEmulator: boolean;
  hasNotch: boolean;
  isTablet: boolean;
}

interface InitializationStatus {
  isInitialized: boolean;
  isInitializing: boolean;
}

class AppInitializationService {
  private isInitialized = false;
  private initPromise: Promise<boolean> | null = null;

  async initialize(): Promise<boolean> {
    // Prevent multiple initializations
    if (this.isInitialized) {
      return true;
    }

    if (this.initPromise) {
      return this.initPromise;
    }

    this.initPromise = this.performInitialization();
    return this.initPromise;
  }

  private async performInitialization(): Promise<boolean> {
    const perfId = performanceService.startTiming('app_initialization');
    
    try {
      logger.info('Starting app initialization', null, 'init');

      // 1. Initialize device info
      const deviceInfo = await this.initializeDeviceInfo();
      logger.info('Device info initialized', null, 'init');

      // 2. Initialize crash reporting
      await this.initializeCrashReporting(deviceInfo);
      logger.info('Crash reporting initialized', null, 'init');

      // 3. Initialize performance monitoring
      await this.initializePerformanceMonitoring();
      logger.info('Performance monitoring initialized', null, 'init');

      // 3a. Initialize advanced performance monitoring
      performanceMonitor.initialize();
      logger.info('Advanced performance monitoring initialized', null, 'init');

      // 4. Initialize secure storage
      await this.initializeSecureStorage();
      logger.info('Secure storage initialized', null, 'init');

      // 5. Initialize feature flags
      await this.initializeFeatureFlags(deviceInfo);
      logger.info('Feature flags initialized', null, 'init');

      // 6. Initialize store subscriptions
      this.initializeStoreSubscriptions();
      logger.info('Store subscriptions initialized', null, 'init');

      // 7. Restore authentication state
      await this.restoreAuthenticationState();
      logger.info('Authentication state restored', null, 'init');

      // 8. Initialize network monitoring
      this.initializeNetworkMonitoring();
      logger.info('Network monitoring initialized', null, 'init');

      // 9. Cleanup old data
      await this.cleanupOldData();
      logger.info('Old data cleaned up', null, 'init');

      this.isInitialized = true;
      performanceService.endTiming(perfId);
      
      logger.info('App initialization completed successfully', {
        duration: Date.now() - (performanceService as any).startTime
      }, 'init');
      return true;

    } catch (error) {
      performanceService.endTiming(perfId);
      logger.error('App initialization failed', error, 'init');
      
      // Record initialization failure
      const errorMessage = error instanceof Error ? error.message : 'Unknown error';
      const errorStack = error instanceof Error ? error.stack : undefined;
      
      crashReporting.recordNonFatalError('App initialization failed', {
        error: errorMessage,
        stack: errorStack,
      });

      return false;
    }
  }

  private async initializeDeviceInfo(): Promise<DeviceInfoType> {
    try {
      const deviceInfo: DeviceInfoType = {
        deviceId: await DeviceInfo.getUniqueId(),
        appVersion: DeviceInfo.getVersion(),
        buildNumber: DeviceInfo.getBuildNumber(),
        platform: Platform.OS as 'ios' | 'android',
        osVersion: DeviceInfo.getSystemVersion(),
        deviceModel: DeviceInfo.getModel(),
        deviceBrand: DeviceInfo.getBrand(),
        isEmulator: await DeviceInfo.isEmulator(),
        hasNotch: DeviceInfo.hasNotch(),
        isTablet: DeviceInfo.isTablet(),
      };

      // Store device info in app store for easy access
      const { setCacheItem } = useAppStore.getState();
      setCacheItem('deviceInfo', deviceInfo, 24 * 60 * 60 * 1000); // Cache for 24 hours

      return deviceInfo;
    } catch (error) {
      logger.warn('Failed to get device info', error, 'init');
      return {
        deviceId: 'unknown',
        appVersion: '1.0.0',
        buildNumber: '1',
        platform: Platform.OS as 'ios' | 'android',
        osVersion: 'unknown',
        deviceModel: 'unknown',
        deviceBrand: 'unknown',
        isEmulator: false,
        hasNotch: false,
        isTablet: false,
      };
    }
  }

  private async initializeCrashReporting(deviceInfo: DeviceInfoType): Promise<void> {
    try {
      // Set user context for crash reporting
      const { user } = useAppStore.getState();
      if (user?.id) {
        crashReporting.setUserId(user.id);
      }

      // Add device info breadcrumb
      crashReporting.addBreadcrumb({
        category: 'device',
        message: 'Device info collected',
        level: 'info',
        data: deviceInfo,
      });

      // Add app start breadcrumb
      crashReporting.addBreadcrumb({
        category: 'app',
        message: 'App initialization started',
        level: 'info',
        data: {
          appVersion: deviceInfo.appVersion,
          platform: deviceInfo.platform,
        },
      });

    } catch (error) {
      logger.warn('Failed to initialize crash reporting', error, 'init');
    }
  }

  private async initializePerformanceMonitoring(): Promise<void> {
    try {
      // Enable performance monitoring based on feature flag or environment
      const shouldEnable = __DEV__ || featureFlags.features.betaFeatures();
      performanceService.setEnabled(shouldEnable);

      // Track app startup performance
      performanceService.startTiming('app_startup');

    } catch (error) {
      logger.warn('Failed to initialize performance monitoring', error, 'init');
    }
  }

  private async initializeSecureStorage(): Promise<void> {
    try {
      // Check if biometric authentication is available
      const biometricAvailable = await secureStorage.isBiometricAvailable();
      const biometryType = await secureStorage.getBiometryType();

      // Store biometric info in cache
      const { setCacheItem } = useAppStore.getState();
      setCacheItem('biometricInfo', {
        available: biometricAvailable,
        type: biometryType,
      });

      logger.info('Biometric info collected', { biometricAvailable, biometryType }, 'init');

    } catch (error) {
      logger.warn('Failed to initialize secure storage', error, 'init');
    }
  }

  private async initializeFeatureFlags(deviceInfo: DeviceInfoType): Promise<void> {
    try {
      const { user } = useAppStore.getState();
      
      await featureFlags.initialize({
        userId: user?.id,
        userGroup: user?.preferences?.language || 'default',
        appVersion: deviceInfo.appVersion,
        platform: deviceInfo.platform,
        deviceId: deviceInfo.deviceId,
      });

      // Update store with feature flags
      const { setFeatureFlag } = useAppStore.getState();
      const enabledFlags = featureFlags.getEnabledFlags();
      
      enabledFlags.forEach(flagKey => {
        setFeatureFlag(flagKey, true);
      });

    } catch (error) {
      logger.warn('Failed to initialize feature flags', error, 'init');
    }
  }

  private initializeStoreSubscriptions(): void {
    try {
      // Subscribe to auth changes
      subscribeToAuthChanges((isAuthenticated: boolean) => {
        crashReporting.addBreadcrumb({
          category: 'auth',
          message: `User ${isAuthenticated ? 'logged in' : 'logged out'}`,
          level: 'info',
        });

        // Update crash reporting user context
        if (isAuthenticated) {
          const { user } = useAppStore.getState();
          if (user?.id) {
            crashReporting.setUserId(user.id);
          }
        } else {
          crashReporting.setUserId('');
        }
      });

      // Subscribe to network changes
      subscribeToNetworkChanges((isOnline: boolean) => {
        crashReporting.addBreadcrumb({
          category: 'network',
          message: `Network ${isOnline ? 'connected' : 'disconnected'}`,
          level: isOnline ? 'info' : 'warning',
        });
      });

    } catch (error) {
      logger.warn('Failed to initialize store subscriptions', error, 'init');
    }
  }

  private async restoreAuthenticationState(): Promise<void> {
    try {
      // Get tokens from secure storage
      const { accessToken, refreshToken } = await secureStorage.getAuthTokens();
      
      if (accessToken && refreshToken) {
        // Update store with tokens
        const { setAuthTokens, setAuthenticated } = useAppStore.getState();
        setAuthTokens(accessToken, refreshToken);

        // Verify token validity by making a test API call
        try {
          const response = await apiService.get('/auth/verify', {}, { 
            timeout: 5000,
            retries: 1,
          });
          
          if (response.status === 200) {
            setAuthenticated(true);
            logger.info('Authentication state restored', null, 'init');
          } else {
            // Token is invalid, clear it
            await this.clearInvalidTokens();
          }
        } catch (apiError) {
          logger.warn('Token verification failed', apiError, 'init');
          await this.clearInvalidTokens();
        }
      }

    } catch (error) {
      logger.warn('Failed to restore authentication state', error, 'init');
    }
  }

  private async clearInvalidTokens(): Promise<void> {
    try {
      await secureStorage.clearAuthData();
      const { resetAuth } = useAppStore.getState();
      resetAuth();
      logger.info('Cleared invalid tokens', null, 'init');
    } catch (error) {
      logger.error('Failed to clear invalid tokens', error, 'init');
    }
  }

  private initializeNetworkMonitoring(): void {
    try {
      // Network monitoring is handled in the API service
      // This is just a placeholder for any additional network setup
      logger.info('Network monitoring is handled by API service', null, 'init');
    } catch (error) {
      logger.warn('Failed to initialize network monitoring', error, 'init');
    }
  }

  private async cleanupOldData(): Promise<void> {
    try {
      // Clean up old crash reports
      await crashReporting.clearOldErrors(7); // Keep 7 days

      // Clean up old performance metrics
      await performanceService.clearOldMetrics(7); // Keep 7 days

      // Clean up expired cache items
      // Cache cleanup is handled automatically in the store

      logger.info('Old data cleanup completed', null, 'init');

    } catch (error) {
      logger.warn('Failed to cleanup old data', error, 'init');
    }
  }

  // Public methods for external use
  isAppInitialized(): boolean {
    return this.isInitialized;
  }

  async reinitialize(): Promise<boolean> {
    this.isInitialized = false;
    this.initPromise = null;
    return this.initialize();
  }

  // Get initialization status
  getInitializationStatus(): InitializationStatus {
    return {
      isInitialized: this.isInitialized,
      isInitializing: this.initPromise !== null && !this.isInitialized,
    };
  }
}

export const appInit = new AppInitializationService();
export default appInit;