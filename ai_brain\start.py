#!/usr/bin/env python3
"""
PayVendy AI Brain Startup Script

Production-ready startup script with proper error handling,
logging configuration, and environment validation.
"""

import os
import sys
import asyncio
import logging
from pathlib import Path
from dotenv import load_dotenv

# Add the ai_brain directory to Python path
sys.path.insert(0, str(Path(__file__).parent))

# Load environment variables from .env file
load_dotenv()

from main import AIBrainApp


def setup_environment():
    """Setup environment variables with defaults"""
    
    # Database configuration
    if not os.getenv('DATABASE_URL'):
        # Default to local PostgreSQL for development
        os.environ['DATABASE_URL'] = 'postgresql://postgres:password@localhost:5432/payvendy'
    
    # AI Brain specific settings
    os.environ.setdefault('AI_LOG_LEVEL', 'INFO')
    os.environ.setdefault('AI_ENABLE_FILE_LOGGING', 'true')
    os.environ.setdefault('AI_LOG_FILE_PATH', 'logs/ai_brain.log')
    
    # AI Model parameters
    os.environ.setdefault('AI_BASE_REWARD_AMOUNT', '10.0')
    os.environ.setdefault('AI_MAX_DAILY_REWARDS', '5')
    os.environ.setdefault('AI_ENGAGEMENT_THRESHOLD', '0.6')
    os.environ.setdefault('AI_SUSPICIOUS_THRESHOLD', '0.8')
    os.environ.setdefault('AI_HIGH_VALUE_THRESHOLD', '1000.0')
    
    # Operational settings
    os.environ.setdefault('AI_ANALYSIS_INTERVAL_SECONDS', '300')  # 5 minutes
    os.environ.setdefault('AI_REWARD_INTERVAL_SECONDS', '600')   # 10 minutes
    os.environ.setdefault('AI_BATCH_SIZE', '100')
    os.environ.setdefault('AI_MAX_CONCURRENT_OPS', '10')
    
    # Environment
    os.environ.setdefault('NODE_ENV', 'development')


def validate_environment():
    """Validate required environment variables"""
    required_vars = [
        'DATABASE_URL'
    ]
    
    missing_vars = []
    for var in required_vars:
        if not os.getenv(var):
            missing_vars.append(var)
    
    if missing_vars:
        print(f"❌ Missing required environment variables: {', '.join(missing_vars)}")
        print("\nPlease set the following environment variables:")
        for var in missing_vars:
            print(f"  export {var}=<value>")
        sys.exit(1)
    
    print("✅ Environment validation passed")


def setup_logging():
    """Setup basic logging for startup"""
    log_level = os.getenv('AI_LOG_LEVEL', 'INFO').upper()
    
    # Create logs directory if it doesn't exist
    log_file_path = os.getenv('AI_LOG_FILE_PATH', 'logs/ai_brain.log')
    log_dir = Path(log_file_path).parent
    log_dir.mkdir(parents=True, exist_ok=True)
    
    # Configure handlers with proper encoding for Windows
    handlers = []
    
    # Console handler with UTF-8 encoding
    console_handler = logging.StreamHandler(sys.stdout)
    handlers.append(console_handler)
    
    # File handler if enabled
    if os.getenv('AI_ENABLE_FILE_LOGGING', 'true').lower() == 'true':
        file_handler = logging.FileHandler(log_file_path, encoding='utf-8')
        handlers.append(file_handler)
    
    logging.basicConfig(
        level=getattr(logging, log_level),
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
        handlers=handlers,
        force=True  # Override any existing configuration
    )


async def main():
    """Main startup function"""
    print("PayVendy AI Brain Starting...")
    print("=" * 50)
    
    # Setup environment
    setup_environment()
    validate_environment()
    setup_logging()
    
    # Display configuration
    print(f"Environment: {os.getenv('NODE_ENV', 'development')}")
    print(f"Log Level: {os.getenv('AI_LOG_LEVEL', 'INFO')}")
    print(f"Database: {os.getenv('DATABASE_URL', 'Not set')[:50]}...")
    print(f"Analysis Interval: {os.getenv('AI_ANALYSIS_INTERVAL_SECONDS', '300')}s")
    print(f"Reward Interval: {os.getenv('AI_REWARD_INTERVAL_SECONDS', '600')}s")
    print("=" * 50)
    
    # Create and run the AI Brain application
    app = AIBrainApp()
    
    try:
        await app.run()
    except KeyboardInterrupt:
        print("\nShutdown requested by user")
    except Exception as e:
        logging.error(f"Fatal error: {str(e)}")
        sys.exit(1)
    finally:
        print("AI Brain shutdown complete")


if __name__ == "__main__":
    try:
        asyncio.run(main())
    except KeyboardInterrupt:
        print("\nInterrupted by user")
        sys.exit(0)
    except Exception as e:
        print(f"Startup failed: {str(e)}")
        sys.exit(1)