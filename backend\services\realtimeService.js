const { getSupabase } = require('../config/database');
const logger = require('../utils/logger');

class RealtimeService {
  constructor() {
    this.supabase = null;
    this.userDeletionChannel = null;
    this.connectedClients = new Map(); // Store connected clients by userId
    this.isInitialized = false;
    this.pollingInterval = null;
    this.lastUserCheck = null;
  }

  /**
   * Initialize the realtime service
   */
  async initialize() {
    try {
      this.supabase = getSupabase();
      
      if (!this.supabase) {
        throw new Error('Supabase client not available');
      }

      logger.info('Initializing realtime service for user deletion detection...');

      // Set up realtime subscription for user deletions
      await this.setupUserDeletionListener();
      
      this.isInitialized = true;
      logger.info('Realtime service initialized successfully');
      
    } catch (error) {
      logger.error('Failed to initialize realtime service:', error);
      logger.warn('Falling back to polling mechanism for user deletion detection');
      
      // Initialize with polling fallback
      this.isInitialized = true;
      this.setupPollingFallback();
    }
  }

  /**
   * Set up listener for user deletions
   */
  async setupUserDeletionListener() {
    try {
      // First, verify that realtime is available and properly configured
      await this.verifyRealtimeConfiguration();

      this.userDeletionChannel = this.supabase
        .channel('user_deletions', {
          config: {
            presence: {
              key: 'user_deletion_listener'
            }
          }
        })
        .on(
          'postgres_changes',
          {
            event: 'DELETE',
            schema: 'public',
            table: 'users'
          },
          (payload) => {
            logger.security('USER_DELETED_REALTIME_DETECTED', {
              deletedUser: payload.old,
              timestamp: new Date().toISOString()
            });
            
            this.handleUserDeletion(payload.old);
          }
        )
        .subscribe((status, err) => {
          if (status === 'SUBSCRIBED') {
            logger.info('Successfully subscribed to user deletion events');
          } else if (status === 'CHANNEL_ERROR') {
            logger.error('Failed to subscribe to user deletion events', { 
              status, 
              error: err,
              message: 'This may be due to: 1) Realtime not enabled for users table, 2) RLS policies blocking access, 3) Service role permissions, or 4) Quota limits'
            });
            
            // Fallback to polling mechanism
            this.setupPollingFallback();
          } else if (status === 'TIMED_OUT') {
            logger.warn('User deletion subscription timed out, retrying...');
            // Retry after delay
            setTimeout(() => {
              this.setupUserDeletionListener();
            }, 10000); // Increased delay to 10 seconds
          } else if (status === 'CLOSED') {
            logger.warn('User deletion channel closed, attempting to reconnect...');
            setTimeout(() => {
              this.setupUserDeletionListener();
            }, 5000);
          }
        });

    } catch (error) {
      logger.error('Failed to setup user deletion listener:', error);
      
      // Fallback to polling mechanism
      this.setupPollingFallback();
    }
  }

  /**
   * Verify realtime configuration
   */
  async verifyRealtimeConfiguration() {
    try {
      // Test basic connection to realtime
      const testChannel = this.supabase.channel('connection_test');
      
      return new Promise((resolve, reject) => {
        const timeout = setTimeout(() => {
          testChannel.unsubscribe();
          reject(new Error('Realtime connection test timed out'));
        }, 5000);

        testChannel.subscribe((status) => {
          clearTimeout(timeout);
          testChannel.unsubscribe();
          
          if (status === 'SUBSCRIBED') {
            logger.info('Realtime connection verified successfully');
            resolve(true);
          } else if (status === 'CHANNEL_ERROR') {
            reject(new Error('Realtime connection test failed with CHANNEL_ERROR'));
          } else {
            reject(new Error(`Realtime connection test failed with status: ${status}`));
          }
        });
      });
    } catch (error) {
      logger.warn('Realtime configuration verification failed:', error.message);
      throw error;
    }
  }

  /**
   * Setup polling fallback when realtime fails
   */
  setupPollingFallback() {
    if (this.pollingInterval) {
      clearInterval(this.pollingInterval);
    }

    logger.info('Setting up polling fallback for user deletion detection');
    
    // Store last check timestamp
    this.lastUserCheck = new Date();
    
    // Poll every 30 seconds for deleted users
    this.pollingInterval = setInterval(async () => {
      try {
        await this.checkForDeletedUsers();
      } catch (error) {
        logger.error('Error in user deletion polling:', error);
      }
    }, 30000);
  }

  /**
   * Check for deleted users using polling
   */
  async checkForDeletedUsers() {
    try {
      // This is a simplified approach - in a real scenario, you might:
      // 1. Keep track of known user IDs
      // 2. Check if they still exist
      // 3. Detect deletions based on missing users
      
      // For now, we'll just log that polling is active
      logger.debug('Polling for user deletions (fallback mechanism active)');
      
      // You could implement more sophisticated logic here based on your needs
      // For example, checking a deletion log table or comparing user lists
      
    } catch (error) {
      logger.error('Failed to check for deleted users:', error);
    }
  }

  /**
   * Handle user deletion event
   */
  async handleUserDeletion(deletedUser) {
    if (!deletedUser || !deletedUser.id) {
      logger.warn('Invalid deleted user data received');
      return;
    }

    const userId = deletedUser.id;
    logger.security('PROCESSING_USER_DELETION', { 
      userId, 
      email: deletedUser.email,
      deletedAt: new Date().toISOString()
    });

    try {
      // Mark user as deleted in our tracking
      await this.markUserAsDeleted(userId);
      
      // Invalidate all tokens for this user
      await this.invalidateUserTokens(userId);
      
      // Log the deletion for audit purposes
      await this.logUserDeletion(userId, deletedUser);
      
      logger.info(`Successfully processed deletion for user ${userId}`);
      
    } catch (error) {
      logger.error('Failed to process user deletion:', error);
    }
  }

  /**
   * Mark user as deleted in our tracking system
   */
  async markUserAsDeleted(userId) {
    try {
      // We can create a deleted_users table to track deletions
      // or use a flag in existing user sessions/tokens
      const supabase = getSupabase();
      
      // For now, we'll just log it - you can extend this to update other tables
      logger.info(`Marking user ${userId} as deleted in tracking system`);
      
      // Example: Update user sessions to mark as invalid
      // await supabase
      //   .from('user_sessions')
      //   .update({ is_valid: false, deleted_at: new Date().toISOString() })
      //   .eq('user_id', userId);
      
    } catch (error) {
      logger.error('Failed to mark user as deleted:', error);
    }
  }

  /**
   * Invalidate all tokens for deleted user
   */
  async invalidateUserTokens(userId) {
    try {
      // This would typically involve:
      // 1. Adding tokens to a blacklist
      // 2. Updating token validation to check for deleted users
      // 3. Clearing any cached user data
      
      logger.info(`Invalidating all tokens for deleted user ${userId}`);
      
      // For now, we'll rely on the existing user lookup in auth middleware
      // which will fail when user doesn't exist
      
    } catch (error) {
      logger.error('Failed to invalidate user tokens:', error);
    }
  }

  /**
   * Log user deletion for audit purposes
   */
  async logUserDeletion(userId, deletedUserData) {
    try {
      const supabase = getSupabase();
      
      // Create audit log entry
      const auditLog = {
        event_type: 'USER_DELETED',
        user_id: userId,
        event_data: {
          deleted_user: {
            id: deletedUserData.id,
            email: deletedUserData.email,
            created_at: deletedUserData.created_at,
            deleted_at: new Date().toISOString()
          }
        },
        created_at: new Date().toISOString()
      };

      // You can create an audit_logs table for this
      logger.info('User deletion audit log created', auditLog);
      
      // Example table insert (uncomment if you have audit_logs table):
      // await supabase
      //   .from('audit_logs')
      //   .insert([auditLog]);
      
    } catch (error) {
      logger.error('Failed to log user deletion:', error);
    }
  }

  /**
   * Check if user exists (for API middleware)
   */
  async checkUserExists(userId) {
    try {
      const supabase = getSupabase();
      
      const { data, error } = await supabase
        .from('users')
        .select('id')
        .eq('id', userId)
        .single();

      if (error && error.code === 'PGRST116') {
        // User not found
        return false;
      }

      if (error) {
        logger.error('Error checking user existence:', error);
        return true; // Assume exists on error to avoid false positives
      }

      return !!data;
      
    } catch (error) {
      logger.error('Failed to check user existence:', error);
      return true; // Assume exists on error
    }
  }

  /**
   * Get service status
   */
  getStatus() {
    return {
      isInitialized: this.isInitialized,
      hasChannel: !!this.userDeletionChannel,
      connectedClients: this.connectedClients.size,
      pollingFallbackActive: !!this.pollingInterval,
      lastUserCheck: this.lastUserCheck
    };
  }

  /**
   * Cleanup resources
   */
  async cleanup() {
    try {
      // Clear polling interval if active
      if (this.pollingInterval) {
        clearInterval(this.pollingInterval);
        this.pollingInterval = null;
      }

      // Remove realtime channel
      if (this.userDeletionChannel && this.supabase) {
        await this.supabase.removeChannel(this.userDeletionChannel);
      }
      
      this.userDeletionChannel = null;
      this.connectedClients.clear();
      this.isInitialized = false;
      this.lastUserCheck = null;
      
      logger.info('Realtime service cleaned up');
      
    } catch (error) {
      logger.error('Failed to cleanup realtime service:', error);
    }
  }
}

// Create singleton instance
const realtimeService = new RealtimeService();

module.exports = realtimeService;