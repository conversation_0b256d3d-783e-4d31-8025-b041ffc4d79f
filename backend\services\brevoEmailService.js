const axios = require('axios');
const logger = require('../utils/logger');
const { getSupabase } = require('../config/database');

class BrevoEmailService {
  constructor() {
    this.apiKey = process.env.BREVO_API_KEY;
    this.fromEmail = process.env.BREVO_FROM_EMAIL || '<EMAIL>';
    this.fromName = process.env.BREVO_FROM_NAME || 'Vendy';
    this.baseURL = 'https://api.brevo.com/v3';

    if (!this.apiKey) {
      throw new Error('Brevo API key is required');
    }

    // Configure optimized axios instance for Brevo API
    this.apiClient = axios.create({
      baseURL: this.baseURL,
      timeout: 10000, // 10 second timeout (reduced from default 30s)
      headers: {
        'api-key': this.apiKey,
        'Content-Type': 'application/json',
        'Accept': 'application/json'
      },
      // Performance optimizations
      maxRedirects: 3,
      validateStatus: (status) => status < 500, // Don't retry on 4xx errors
    });

    // Add response interceptor for better error handling
    this.apiClient.interceptors.response.use(
      (response) => response,
      (error) => {
        // Log error details but don't block the response
        if (error.response?.status >= 500) {
          logger.warn('Brevo API server error', { status: error.response.status, data: error.response.data });
        }
        return Promise.reject(error);
      }
    );
  }

  /**
   * Send email using Brevo API
   * @param {string} to - Recipient email address
   * @param {string} subject - Email subject
   * @param {string} htmlContent - HTML email content
   * @param {string} textContent - Plain text email content
   * @returns {Promise<Object>} - API response
   */
  async sendEmail(to, subject, htmlContent, textContent = null) {
    try {
      console.log('📧 [EMAIL] SendEmail function called');
      console.log('📧 To:', to);
      console.log('📧 Subject:', subject);
      console.log('📧 From:', `${this.fromName} <${this.fromEmail}>`);

      // Validate email format
      const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
      if (!emailRegex.test(to)) {
        throw new Error('Invalid email address format');
      }

      // Prepare email payload for Brevo API
      const emailPayload = {
        subject: subject,
        htmlContent: htmlContent,
        textContent: textContent || this.stripHtml(htmlContent),
        sender: {
          name: this.fromName,
          email: this.fromEmail
        },
        to: [{
          email: to,
          name: to.split('@')[0] // Use email prefix as name
        }]
      };

      console.log('📧 [EMAIL] Configuration:');
      console.log('🔑 API Key:', this.apiKey ? `${this.apiKey.substring(0, 10)}...` : 'NOT SET');
      console.log('📤 From Email:', this.fromEmail);
      console.log('📤 From Name:', this.fromName);

      logger.info(`Sending email to ${to} with subject: ${subject}`);

      console.log('📡 [EMAIL] Making API request to Brevo...');
      const response = await this.apiClient.post('/smtp/email', emailPayload);

      console.log('✅ [EMAIL] Email sent successfully!');
      console.log('📧 Response:', response.data);
      console.log('📧 Message ID:', response.data.messageId);

      logger.info(`Email sent successfully to ${to}. Message ID: ${response.data.messageId}`);

      // PERFORMANCE OPTIMIZATION: Log email delivery asynchronously (don't wait for it)
      console.log('💾 [EMAIL] Logging email delivery asynchronously...');
      this.logEmailDelivery(to, response.data.messageId, 'sent', subject).catch(error => {
        logger.warn('Email logging failed (non-critical)', error);
      });

      return {
        success: true,
        messageId: response.data.messageId,
        message: 'Email sent successfully'
      };

    } catch (error) {
      console.log('💥 [EMAIL] Error occurred:');
      console.log('❌ Error message:', error.message);
      console.log('📊 Error code:', error.status || error.code);
      console.log('📋 Error response:', JSON.stringify(error.response?.body || error.body || {}, null, 2));
      console.log('🔍 Full error:', error);

      logger.error('Email sending failed:', {
        error: error.message,
        email: to,
        response: error.response?.body || error.body
      });

      // PERFORMANCE OPTIMIZATION: Log failed attempt asynchronously (don't wait for it)
      console.log('💾 [EMAIL] Logging failed attempt asynchronously...');
      this.logEmailDelivery(to, null, 'failed', subject, error.message).catch(logError => {
        logger.warn('Failed email logging failed (non-critical)', logError);
      });

      return {
        success: false,
        error: error.response?.body?.message || error.body?.message || error.message || 'Email sending failed'
      };
    }
  }

  /**
   * Send OTP email
   * @param {string} email - Recipient email address
   * @param {string} otp - OTP code
   * @param {string} purpose - Purpose of OTP (verification, reset, etc.)
   * @returns {Promise<Object>} - API response
   */
  async sendOTP(email, otp, purpose = 'verification') {
    // PERFORMANCE OPTIMIZATION: Reduce console logging in production
    if (process.env.NODE_ENV !== 'production') {
      console.log('📨 [EMAIL] SendOTP called');
      console.log('📧 Email:', email);
      console.log('🔢 OTP:', otp);
      console.log('🎯 Purpose:', purpose);
    }

    // PERFORMANCE OPTIMIZATION: Run rate limit check and email content generation in parallel
    const [isRateLimited, emailContent] = await Promise.all([
      this.checkOTPRateLimit(email),
      Promise.resolve(this.generateOTPEmailContent(otp, purpose)) // Wrap sync function in Promise.resolve for consistency
    ]);

    console.log('⏱️ [EMAIL] Rate limited:', isRateLimited);

    if (isRateLimited) {
      console.log('🚫 [EMAIL] Rate limit exceeded');
      return {
        success: false,
        error: 'Too many OTP requests. Please wait before requesting another code.'
      };
    }

    const { subject, htmlContent, textContent } = emailContent;

    console.log('📤 [EMAIL] Calling sendEmail...');
    const result = await this.sendEmail(email, subject, htmlContent, textContent);
    console.log('📤 [EMAIL] SendEmail result:', JSON.stringify(result, null, 2));

    return result;
  }

  /**
   * Generate OTP email content
   * @param {string} otp - OTP code
   * @param {string} purpose - Purpose of OTP
   * @returns {Object} - Email content
   */
  generateOTPEmailContent(otp, purpose) {
    const subjects = {
      verification: 'Verify Your Email - Vendy',
      reset: 'Reset Your Password - Vendy',
      login: 'Login Verification - Vendy'
    };

    const subject = subjects[purpose] || subjects.verification;

    const htmlContent = `
    <!DOCTYPE html>
    <html>
    <head>
        <meta charset="utf-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>${subject}</title>
        <style>
            body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; }
            .container { max-width: 600px; margin: 0 auto; padding: 20px; }
            .header { background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; padding: 30px; text-align: center; border-radius: 10px 10px 0 0; }
            .content { background: #f9f9f9; padding: 30px; border-radius: 0 0 10px 10px; }
            .otp-code { background: #fff; border: 2px dashed #667eea; padding: 20px; text-align: center; margin: 20px 0; border-radius: 8px; }
            .otp-number { font-size: 32px; font-weight: bold; color: #667eea; letter-spacing: 5px; }
            .footer { text-align: center; margin-top: 20px; color: #666; font-size: 12px; }
            .warning { background: #fff3cd; border: 1px solid #ffeaa7; padding: 15px; border-radius: 5px; margin: 20px 0; }
        </style>
    </head>
    <body>
        <div class="container">
            <div class="header">
                <h1>🔐 Vendy Security Code</h1>
                <p>Your verification code is ready</p>
            </div>
            <div class="content">
                <h2>Hello!</h2>
                <p>You requested a verification code for your Vendy account. Use the code below to complete your ${purpose}:</p>

                <div class="otp-code">
                    <div class="otp-number">${otp}</div>
                    <p style="margin: 10px 0 0 0; color: #666;">Enter this code in the app</p>
                </div>

                <div class="warning">
                    <strong>⚠️ Security Notice:</strong>
                    <ul style="margin: 10px 0 0 0; padding-left: 20px;">
                        <li>This code expires in 5 minutes</li>
                        <li>Never share this code with anyone</li>
                        <li>Vendy will never ask for this code via phone or email</li>
                    </ul>
                </div>

                <p>If you didn't request this code, please ignore this email or contact our support team.</p>

                <p>Best regards,<br><strong>The Vendy Team</strong></p>
            </div>
            <div class="footer">
                <p>© 2024 Vendy. All rights reserved.</p>
                <p>This is an automated message, please do not reply to this email.</p>
            </div>
        </div>
    </body>
    </html>`;

    const textContent = `
Vendy Security Code

Hello!

You requested a verification code for your Vendy account. Use the code below to complete your ${purpose}:

Your Code: ${otp}

SECURITY NOTICE:
- This code expires in 5 minutes
- Never share this code with anyone
- Vendy will never ask for this code via phone or email

If you didn't request this code, please ignore this email or contact our support team.

Best regards,
The Vendy Team

© 2024 Vendy. All rights reserved.
This is an automated message, please do not reply to this email.`;

    return { subject, htmlContent, textContent };
  }

  /**
   * Check OTP rate limiting for email - OPTIMIZED VERSION
   * @param {string} email - Email address
   * @returns {Promise<boolean>} - Whether rate limited
   */
  async checkOTPRateLimit(email) {
    try {
      const supabase = getSupabase();
      const now = new Date();
      const fiveMinutesAgo = new Date(now.getTime() - 5 * 60 * 1000);
      const maxOTPsPerFiveMinutes = parseInt(process.env.MAX_EMAIL_OTP_PER_5MIN) || 3;

      // PERFORMANCE OPTIMIZATION: Use count() instead of select() for faster query
      // Only count, don't fetch actual records
      const { count, error } = await supabase
        .from('email_logs')
        .select('*', { count: 'exact', head: true }) // head: true means no data returned, just count
        .eq('recipient', email)
        .eq('status', 'sent')
        .gte('created_at', fiveMinutesAgo.toISOString())
        .like('subject', '%Verify%'); // More specific than '%OTP%' for faster matching

      if (error) {
        logger.error('Rate limit check failed:', error);
        return false; // Allow if check fails (fail open for better UX)
      }

      const otpCount = count || 0;
      console.log(`📧 [EMAIL] OTP count in last 5 minutes: ${otpCount}/${maxOTPsPerFiveMinutes}`);

      return otpCount >= maxOTPsPerFiveMinutes;

    } catch (error) {
      logger.error('Rate limit check error:', error);
      return false; // Allow if check fails (fail open for better UX)
    }
  }

  /**
   * Log email delivery for tracking and analytics
   * @param {string} recipient - Recipient email
   * @param {string} messageId - Brevo message ID
   * @param {string} status - Delivery status
   * @param {string} subject - Email subject
   * @param {string} errorMessage - Error message if failed
   */
  async logEmailDelivery(recipient, messageId, status, subject, errorMessage = null) {
    try {
      const supabase = getSupabase();

      const logData = {
        recipient: recipient,
        message_id: messageId,
        status: status,
        subject: subject,
        error_message: errorMessage,
        created_at: new Date().toISOString()
      };

      const { error } = await supabase
        .from('email_logs')
        .insert([logData]);

      if (error) {
        logger.error('Failed to log email delivery:', error);
      } else {
        console.log('💾 [EMAIL] Email delivery logged successfully');
      }

    } catch (error) {
      logger.error('Email logging error:', error);
    }
  }

  /**
   * Strip HTML tags from content
   * @param {string} html - HTML content
   * @returns {string} - Plain text
   */
  stripHtml(html) {
    return html.replace(/<[^>]*>/g, '').replace(/\s+/g, ' ').trim();
  }

  /**
   * Verify email delivery status
   * @param {string} messageId - Brevo message ID
   * @returns {Promise<Object>} - Delivery status
   */
  async checkDeliveryStatus(messageId) {
    try {
      const supabase = getSupabase();
      const { data, error } = await supabase
        .from('email_logs')
        .select('*')
        .eq('message_id', messageId)
        .single();

      if (error) {
        return {
          success: false,
          error: 'Message not found'
        };
      }

      return {
        success: true,
        status: data.status,
        data: data
      };

    } catch (error) {
      logger.error('Failed to check delivery status:', error.message);
      return {
        success: false,
        error: 'Failed to check delivery status'
      };
    }
  }
}

module.exports = new BrevoEmailService();
