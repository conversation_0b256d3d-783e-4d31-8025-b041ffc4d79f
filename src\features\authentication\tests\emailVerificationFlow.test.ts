/**
 * Test file for email verification flow
 * This tests the complete authentication flow from email verification to final destination
 */

import { navigationHandler } from '../../../navigation/handlers/navigationHandler';
import { setupService } from '../services/setupService';

// Mock the dependencies
jest.mock('../../../navigation/handlers/navigationHandler');
jest.mock('../services/setupService');

const mockNavigationHandler = navigationHandler as jest.Mocked<typeof navigationHandler>;
const mockSetupService = setupService as jest.Mocked<typeof setupService>;

describe('Email Verification Flow', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  describe('navigateAfterEmailVerification', () => {
    const testEmail = '<EMAIL>';

    it('should route to name setup when user has no profile setup', async () => {
      // Mock setup service response - user needs name setup
      mockSetupService.getSetupStatus.mockResolvedValue({
        setupStatus: {
          hasProfileSetup: false,
          hasPinSetup: false,
          hasBiometricSetup: false,
          isEmailVerified: true,
          isPhoneVerified: false,
          setupComplete: false,
        },
        user: {
          id: 'user-123',
          email: testEmail,
          isEmailVerified: true,
          isPhoneVerified: false,
        },
      });

      // Mock navigation methods
      const mockNav = {
        navigate: jest.fn(),
      };
      mockNavigationHandler.ensureNavigation.mockReturnValue(mockNav as any);

      await mockNavigationHandler.navigateAfterEmailVerification(testEmail);

      expect(mockNav.navigate).toHaveBeenCalledWith('NameSetup', {
        userData: expect.objectContaining({
          email: testEmail,
          isEmailVerified: true,
          authMethod: 'email',
          isNewUser: true,
        }),
      });
    });

    it('should route to PIN setup when user has name but no PIN', async () => {
      // Mock setup service response - user has name but needs PIN
      mockSetupService.getSetupStatus.mockResolvedValue({
        setupStatus: {
          hasProfileSetup: true,
          hasPinSetup: false,
          hasBiometricSetup: false,
          isEmailVerified: true,
          isPhoneVerified: false,
          setupComplete: false,
        },
        user: {
          id: 'user-123',
          firstName: 'John',
          email: testEmail,
          isEmailVerified: true,
          isPhoneVerified: false,
        },
      });

      const mockNav = {
        navigate: jest.fn(),
      };
      mockNavigationHandler.ensureNavigation.mockReturnValue(mockNav as any);

      await mockNavigationHandler.navigateAfterEmailVerification(testEmail);

      expect(mockNav.navigate).toHaveBeenCalledWith('PinSetup', {
        userData: expect.objectContaining({
          firstName: 'John',
          email: testEmail,
          hasProfileSetup: true,
          hasPinSetup: false,
        }),
      });
    });

    it('should route to biometric setup when user has name and PIN but no biometric', async () => {
      // Mock setup service response - user has name and PIN but needs biometric
      mockSetupService.getSetupStatus.mockResolvedValue({
        setupStatus: {
          hasProfileSetup: true,
          hasPinSetup: true,
          hasBiometricSetup: false,
          isEmailVerified: true,
          isPhoneVerified: false,
          setupComplete: false,
        },
        user: {
          id: 'user-123',
          firstName: 'John',
          email: testEmail,
          isEmailVerified: true,
          isPhoneVerified: false,
        },
      });

      const mockNav = {
        navigate: jest.fn(),
      };
      mockNavigationHandler.ensureNavigation.mockReturnValue(mockNav as any);

      await mockNavigationHandler.navigateAfterEmailVerification(testEmail);

      expect(mockNav.navigate).toHaveBeenCalledWith('BiometricSetup', {
        userData: expect.objectContaining({
          firstName: 'John',
          email: testEmail,
          hasPinSetup: true,
          hasBiometricSetup: false,
        }),
      });
    });

    it('should route to PIN verification when user has name, PIN, and biometric but setup not complete', async () => {
      // Mock setup service response - user has everything but setup not marked complete
      mockSetupService.getSetupStatus.mockResolvedValue({
        setupStatus: {
          hasProfileSetup: true,
          hasPinSetup: true,
          hasBiometricSetup: true,
          isEmailVerified: true,
          isPhoneVerified: false,
          setupComplete: false,
        },
        user: {
          id: 'user-123',
          firstName: 'John',
          email: testEmail,
          isEmailVerified: true,
          isPhoneVerified: false,
        },
      });

      const mockNav = {
        navigate: jest.fn(),
      };
      mockNavigationHandler.ensureNavigation.mockReturnValue(mockNav as any);

      await mockNavigationHandler.navigateAfterEmailVerification(testEmail);

      expect(mockNav.navigate).toHaveBeenCalledWith('PinVerification', {
        user: expect.objectContaining({
          firstName: 'John',
          email: testEmail,
          hasPinSetup: true,
          hasBiometricSetup: true,
        }),
      });
    });

    it('should route to main app when setup is complete', async () => {
      // Mock setup service response - setup complete
      mockSetupService.getSetupStatus.mockResolvedValue({
        setupStatus: {
          hasProfileSetup: true,
          hasPinSetup: true,
          hasBiometricSetup: true,
          isEmailVerified: true,
          isPhoneVerified: false,
          setupComplete: true,
        },
        user: {
          id: 'user-123',
          firstName: 'John',
          email: testEmail,
          isEmailVerified: true,
          isPhoneVerified: false,
        },
      });

      mockNavigationHandler.resetToScreen.mockImplementation(() => {});

      await mockNavigationHandler.navigateAfterEmailVerification(testEmail);

      expect(mockNavigationHandler.resetToScreen).toHaveBeenCalledWith('MainTabs');
    });

    it('should fallback to startup screen on error', async () => {
      // Mock setup service to throw error
      mockSetupService.getSetupStatus.mockRejectedValue(new Error('API Error'));

      mockNavigationHandler.resetToScreen.mockImplementation(() => {});

      await mockNavigationHandler.navigateAfterEmailVerification(testEmail);

      expect(mockNavigationHandler.resetToScreen).toHaveBeenCalledWith('Startup');
    });
  });
});
