# PayVendy AI Brain Docker Compose Configuration
# Extends the main docker-compose.yml with AI Brain services

version: '3.8'

services:
  # AI Brain Service
  ai-brain:
    build:
      context: ./ai_brain
      dockerfile: Dockerfile
    container_name: payvendy-ai-brain
    restart: unless-stopped
    environment:
      - NODE_ENV=${NODE_ENV:-development}
      - DATABASE_URL=postgresql://${DB_USER:-postgres}:${DB_PASSWORD:-password}@postgres:5432/${DB_NAME:-payvendy}
      
      # AI Configuration
      - AI_LOG_LEVEL=${AI_LOG_LEVEL:-INFO}
      - AI_ENABLE_FILE_LOGGING=true
      - AI_LOG_FILE_PATH=/app/logs/ai_brain.log
      
      # AI Model Parameters
      - AI_BASE_REWARD_AMOUNT=${AI_BASE_REWARD_AMOUNT:-10.0}
      - AI_MAX_DAILY_REWARDS=${AI_MAX_DAILY_REWARDS:-5}
      - AI_ENGAGEMENT_THRESHOLD=${AI_ENGAGEMENT_THRESHOLD:-0.6}
      - AI_SUSPICIOUS_THRESHOLD=${AI_SUSPICIOUS_THRESHOLD:-0.8}
      - AI_HIGH_VALUE_THRESHOLD=${AI_HIGH_VALUE_THRESHOLD:-1000.0}
      - AI_RISK_VELOCITY_THRESHOLD=${AI_RISK_VELOCITY_THRESHOLD:-500.0}
      
      # Operational Settings
      - AI_ANALYSIS_INTERVAL_SECONDS=${AI_ANALYSIS_INTERVAL_SECONDS:-300}
      - AI_REWARD_INTERVAL_SECONDS=${AI_REWARD_INTERVAL_SECONDS:-600}
      - AI_CLEANUP_INTERVAL_HOURS=${AI_CLEANUP_INTERVAL_HOURS:-24}
      - AI_BATCH_SIZE=${AI_BATCH_SIZE:-100}
      - AI_MAX_CONCURRENT_OPS=${AI_MAX_CONCURRENT_OPS:-10}
      
      # Database Pool Settings
      - DB_MIN_POOL_SIZE=${DB_MIN_POOL_SIZE:-5}
      - DB_MAX_POOL_SIZE=${DB_MAX_POOL_SIZE:-20}
      - DB_POOL_TIMEOUT=${DB_POOL_TIMEOUT:-30}
      - DB_STATEMENT_TIMEOUT=${DB_STATEMENT_TIMEOUT:-60}
      
      # Feature Flags
      - AI_ENABLE_PERFORMANCE_MONITORING=${AI_ENABLE_PERFORMANCE_MONITORING:-true}
      - AI_ENABLE_DETAILED_LOGGING=${AI_ENABLE_DETAILED_LOGGING:-false}
      
    volumes:
      - ai_logs:/app/logs
      - ./ai_brain:/app:ro  # Mount source code as read-only in development
    depends_on:
      - postgres
    networks:
      - payvendy-network
    healthcheck:
      test: ["CMD", "python", "-c", "import asyncio; import sys; sys.path.insert(0, '.'); from services.database import DatabaseService; from config.settings import AIBrainConfig; from services.logger import AILogger; asyncio.run(DatabaseService(AIBrainConfig(), AILogger()).initialize())"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 60s

  # Backend service (extends main backend)
  backend:
    environment:
      # AI Integration Settings
      - AI_BRAIN_ENABLED=true
      - AI_BRAIN_URL=http://ai-brain:8080
      - AI_WEBHOOK_SECRET=${AI_WEBHOOK_SECRET:-your-webhook-secret-here}
    depends_on:
      - postgres
      - ai-brain

  # PostgreSQL Database (shared between backend and AI brain)
  postgres:
    environment:
      - POSTGRES_DB=${DB_NAME:-payvendy}
      - POSTGRES_USER=${DB_USER:-postgres}
      - POSTGRES_PASSWORD=${DB_PASSWORD:-password}
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./backend/database:/docker-entrypoint-initdb.d:ro
    ports:
      - "${DB_PORT:-5432}:5432"

  # Redis (for caching and real-time features)
  redis:
    image: redis:7-alpine
    container_name: payvendy-redis
    restart: unless-stopped
    command: redis-server --appendonly yes --requirepass ${REDIS_PASSWORD:-redis_password}
    volumes:
      - redis_data:/data
    networks:
      - payvendy-network
    healthcheck:
      test: ["CMD", "redis-cli", "--raw", "incr", "ping"]
      interval: 30s
      timeout: 10s
      retries: 3

  # AI Brain Monitoring (optional)
  ai-monitor:
    image: prom/prometheus:latest
    container_name: payvendy-ai-monitor
    restart: unless-stopped
    command:
      - '--config.file=/etc/prometheus/prometheus.yml'
      - '--storage.tsdb.path=/prometheus'
      - '--web.console.libraries=/etc/prometheus/console_libraries'
      - '--web.console.templates=/etc/prometheus/consoles'
      - '--storage.tsdb.retention.time=200h'
      - '--web.enable-lifecycle'
    volumes:
      - ./monitoring/prometheus.yml:/etc/prometheus/prometheus.yml:ro
      - prometheus_data:/prometheus
    ports:
      - "9090:9090"
    networks:
      - payvendy-network
    profiles:
      - monitoring

volumes:
  postgres_data:
    driver: local
  redis_data:
    driver: local
  ai_logs:
    driver: local
  prometheus_data:
    driver: local

networks:
  payvendy-network:
    driver: bridge