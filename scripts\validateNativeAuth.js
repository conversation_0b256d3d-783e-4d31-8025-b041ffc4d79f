#!/usr/bin/env node

/**
 * Native Authentication Validation Script
 * 
 * This script validates that the native Google Sign-In implementation
 * is correctly configured and no web-based authentication code remains.
 */

const fs = require('fs');
const path = require('path');

const PROJECT_ROOT = path.join(__dirname, '..');
const SRC_DIR = path.join(PROJECT_ROOT, 'src');

// Colors for console output
const colors = {
  green: '\x1b[32m',
  red: '\x1b[31m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  reset: '\x1b[0m',
  bold: '\x1b[1m'
};

function log(message, color = 'reset') {
  console.log(`${colors[color]}${message}${colors.reset}`);
}

function logHeader(message) {
  log(`\n${colors.bold}${colors.blue}=== ${message} ===${colors.reset}`);
}

function logSuccess(message) {
  log(`✅ ${message}`, 'green');
}

function logError(message) {
  log(`❌ ${message}`, 'red');
}

function logWarning(message) {
  log(`⚠️  ${message}`, 'yellow');
}

// Validation functions
function validateFileExists(filePath, description) {
  const fullPath = path.join(PROJECT_ROOT, filePath);
  if (fs.existsSync(fullPath)) {
    logSuccess(`${description} exists: ${filePath}`);
    return true;
  } else {
    logError(`${description} missing: ${filePath}`);
    return false;
  }
}

function validateNoWebSDKImports() {
  logHeader('Checking for Web SDK Imports');
  
  const webSDKPatterns = [
    /firebase\/auth/,
    /@firebase\/auth/,
    /signInWithPopup/,
    /signInWithRedirect/,
    /getAuth\(/,
    /initializeAuth\(/
  ];
  
  let hasWebSDK = false;
  
  function checkFile(filePath) {
    if (!fs.existsSync(filePath)) return;
    
    const content = fs.readFileSync(filePath, 'utf8');
    const relativePath = path.relative(PROJECT_ROOT, filePath);
    
    webSDKPatterns.forEach(pattern => {
      if (pattern.test(content)) {
        logError(`Web SDK pattern found in ${relativePath}: ${pattern}`);
        hasWebSDK = true;
      }
    });
  }
  
  function walkDirectory(dir) {
    const files = fs.readdirSync(dir);
    
    files.forEach(file => {
      const filePath = path.join(dir, file);
      const stat = fs.statSync(filePath);
      
      if (stat.isDirectory() && !file.startsWith('.') && file !== 'node_modules') {
        walkDirectory(filePath);
      } else if (file.endsWith('.ts') || file.endsWith('.tsx') || file.endsWith('.js') || file.endsWith('.jsx')) {
        checkFile(filePath);
      }
    });
  }
  
  walkDirectory(SRC_DIR);
  
  if (!hasWebSDK) {
    logSuccess('No web SDK imports found');
  }
  
  return !hasWebSDK;
}

function validateNativeAuthFiles() {
  logHeader('Validating Native Auth Files');
  
  const requiredFiles = [
    { path: 'src/config/GoogleSignInConfig.ts', desc: 'Google Sign-In Configuration' },
    { path: 'src/services/nativeAuthService.ts', desc: 'Native Auth Service' },
    { path: 'src/services/googleAuthService.ts', desc: 'Google Auth Service' },
    { path: 'src/services/googleAccountService.ts', desc: 'Google Account Service' },
    { path: 'src/components/ConsentModal.tsx', desc: 'Consent Modal Component' },
    { path: 'src/screens/SignInScreen.tsx', desc: 'Sign-In Screen' }
  ];
  
  let allFilesExist = true;
  
  requiredFiles.forEach(file => {
    if (!validateFileExists(file.path, file.desc)) {
      allFilesExist = false;
    }
  });
  
  return allFilesExist;
}

function validatePackageJson() {
  logHeader('Validating Package Dependencies');
  
  const packageJsonPath = path.join(PROJECT_ROOT, 'package.json');
  
  if (!fs.existsSync(packageJsonPath)) {
    logError('package.json not found');
    return false;
  }
  
  const packageJson = JSON.parse(fs.readFileSync(packageJsonPath, 'utf8'));
  const dependencies = { ...packageJson.dependencies, ...packageJson.devDependencies };
  
  // Check for required packages
  const requiredPackages = [
    '@react-native-google-signin/google-signin',
    '@react-native-firebase/auth',
    '@react-native-firebase/app'
  ];
  
  let hasAllRequired = true;
  
  requiredPackages.forEach(pkg => {
    if (dependencies[pkg]) {
      logSuccess(`Required package found: ${pkg}@${dependencies[pkg]}`);
    } else {
      logError(`Required package missing: ${pkg}`);
      hasAllRequired = false;
    }
  });
  
  // Check for web SDK packages (should not exist)
  const webSDKPackages = [
    'firebase',
    '@firebase/auth',
    '@firebase/app'
  ];
  
  let hasWebSDKPackages = false;
  
  webSDKPackages.forEach(pkg => {
    if (dependencies[pkg]) {
      logWarning(`Web SDK package found (should be removed): ${pkg}@${dependencies[pkg]}`);
      hasWebSDKPackages = true;
    }
  });
  
  if (!hasWebSDKPackages) {
    logSuccess('No web SDK packages found');
  }
  
  return hasAllRequired && !hasWebSDKPackages;
}

function validateGoogleSignInConfig() {
  logHeader('Validating Google Sign-In Configuration');
  
  const configPath = path.join(SRC_DIR, 'config', 'GoogleSignInConfig.ts');
  
  if (!fs.existsSync(configPath)) {
    logError('GoogleSignInConfig.ts not found');
    return false;
  }
  
  const content = fs.readFileSync(configPath, 'utf8');
  
  const checks = [
    { pattern: /webClientId.*apps\.googleusercontent\.com/, desc: 'Web Client ID configured' },
    { pattern: /iosClientId.*apps\.googleusercontent\.com/, desc: 'iOS Client ID configured' },
    { pattern: /configureGoogleSignIn/, desc: 'Configuration function exported' },
    { pattern: /GoogleSignin\.configure/, desc: 'GoogleSignin.configure called' }
  ];
  
  let allChecksPass = true;
  
  checks.forEach(check => {
    if (check.pattern.test(content)) {
      logSuccess(check.desc);
    } else {
      logError(`Configuration check failed: ${check.desc}`);
      allChecksPass = false;
    }
  });
  
  return allChecksPass;
}

function validateAuthServiceIntegration() {
  logHeader('Validating Auth Service Integration');
  
  const authServicePath = path.join(SRC_DIR, 'services', 'nativeAuthService.ts');
  
  if (!fs.existsSync(authServicePath)) {
    logError('nativeAuthService.ts not found');
    return false;
  }
  
  const content = fs.readFileSync(authServicePath, 'utf8');
  
  const checks = [
    { pattern: /@react-native-google-signin\/google-signin/, desc: 'Native Google Sign-In import' },
    { pattern: /@react-native-firebase\/auth/, desc: 'React Native Firebase Auth import' },
    { pattern: /GoogleSignin\.signIn/, desc: 'Native sign-in method' },
    { pattern: /GoogleSignin\.signInSilently/, desc: 'Silent sign-in method' },
    { pattern: /GoogleSignin\.hasPlayServices/, desc: 'Play Services check' },
    { pattern: /auth\(\)\.signInWithCredential/, desc: 'Firebase credential sign-in' },
    { pattern: /MMKV/, desc: 'Secure storage implementation' }
  ];
  
  let allChecksPass = true;
  
  checks.forEach(check => {
    if (check.pattern.test(content)) {
      logSuccess(check.desc);
    } else {
      logError(`Auth service check failed: ${check.desc}`);
      allChecksPass = false;
    }
  });
  
  return allChecksPass;
}

// Main validation function
function runValidation() {
  log(`${colors.bold}${colors.blue}🔍 Native Google Authentication Validation${colors.reset}\n`);
  
  const validations = [
    { name: 'Native Auth Files', fn: validateNativeAuthFiles },
    { name: 'Package Dependencies', fn: validatePackageJson },
    { name: 'Google Sign-In Config', fn: validateGoogleSignInConfig },
    { name: 'Auth Service Integration', fn: validateAuthServiceIntegration },
    { name: 'Web SDK Cleanup', fn: validateNoWebSDKImports }
  ];
  
  let allValidationsPassed = true;
  const results = [];
  
  validations.forEach(validation => {
    try {
      const result = validation.fn();
      results.push({ name: validation.name, passed: result });
      if (!result) {
        allValidationsPassed = false;
      }
    } catch (error) {
      logError(`Validation failed for ${validation.name}: ${error.message}`);
      results.push({ name: validation.name, passed: false });
      allValidationsPassed = false;
    }
  });
  
  // Summary
  logHeader('Validation Summary');
  
  results.forEach(result => {
    if (result.passed) {
      logSuccess(`${result.name}: PASSED`);
    } else {
      logError(`${result.name}: FAILED`);
    }
  });
  
  log('');
  
  if (allValidationsPassed) {
    log(`${colors.bold}${colors.green}🎉 All validations passed! Native Google Authentication is properly configured.${colors.reset}`);
    process.exit(0);
  } else {
    log(`${colors.bold}${colors.red}❌ Some validations failed. Please review the errors above.${colors.reset}`);
    process.exit(1);
  }
}

// Run the validation
runValidation();