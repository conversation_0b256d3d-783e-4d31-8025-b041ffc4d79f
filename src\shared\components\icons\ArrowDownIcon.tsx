import React from 'react';
import Svg, { Path } from 'react-native-svg';

interface ArrowDownIconProps {
  size?: number;
  color?: string;
}

const ArrowDownIcon: React.FC<ArrowDownIconProps> = ({
  size = 24,
  color = '#000000',
}) => {
  return (
    <Svg width={size} height={size} viewBox="0 0 24 24" fill="none">
      <Path
        d="M12 5v14M19 12l-7 7-7-7"
        stroke={color}
        strokeWidth="2"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
    </Svg>
  );
};

export default ArrowDownIcon;