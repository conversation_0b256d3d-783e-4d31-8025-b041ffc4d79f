import React from 'react';
import Svg, { Path } from 'react-native-svg';

interface AirtimeIconProps {
  size?: number;
  color?: string;
}

const AirtimeIcon: React.FC<AirtimeIconProps> = ({ 
  size = 24, 
  color = '#000000' 
}) => {
  return (
    <Svg width={size} height={size} viewBox="0 0 24 24" fill="none">
      <Path
        d="M17 2H7C5.89 2 5 2.9 5 4V20C5 21.1 5.89 22 7 22H17C18.1 22 19 21.1 19 20V4C19 2.9 18.1 2 17 2ZM17 18H7V6H17V18ZM9 8H15V10H9V8ZM9 11H13V13H9V11Z"
        fill={color}
      />
    </Svg>
  );
};

export default AirtimeIcon;