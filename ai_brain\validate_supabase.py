#!/usr/bin/env python3
"""
PayVendy AI Brain - Supabase Connection Validator

This script validates the Supabase connection and database schema
before starting the AI Brain system.
"""

import os
import sys
import asyncio
import asyncpg
from pathlib import Path
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

async def validate_connection():
    """Validate Supabase database connection."""
    print("🔍 Validating Supabase Connection...")
    print("=" * 50)
    
    # Check environment variables
    database_url = os.getenv('DATABASE_URL')
    if not database_url:
        print("❌ DATABASE_URL not found in environment variables")
        return False
    
    if 'supabase.co' not in database_url:
        print("⚠️  Warning: DATABASE_URL doesn't appear to be a Supabase URL")
        print(f"   Current URL: {database_url[:50]}...")
    
    try:
        # Test connection
        print("🔌 Testing database connection...")
        conn = await asyncpg.connect(database_url)
        
        # Test basic query
        result = await conn.fetchval('SELECT 1')
        if result == 1:
            print("✅ Database connection successful")
        
        # Check PostgreSQL version
        pg_version = await conn.fetchval('SELECT version()')
        print(f"📊 PostgreSQL Version: {pg_version.split(',')[0]}")
        
        # Check if main tables exist
        print("\n🗄️  Checking main application tables...")
        main_tables = ['users', 'transactions', 'sessions', 'wallet_transactions']
        
        for table in main_tables:
            exists = await conn.fetchval("""
                SELECT EXISTS (
                    SELECT FROM information_schema.tables 
                    WHERE table_schema = 'public' 
                    AND table_name = $1
                )
            """, table)
            
            status = "✅" if exists else "❌"
            print(f"   {status} {table}")
        
        # Check if AI Brain tables exist
        print("\n🧠 Checking AI Brain tables...")
        ai_tables = ['ai_logs', 'reward_queue', 'user_segments', 'behavior_analytics', 'realtime_events']
        
        missing_ai_tables = []
        for table in ai_tables:
            exists = await conn.fetchval("""
                SELECT EXISTS (
                    SELECT FROM information_schema.tables 
                    WHERE table_schema = 'public' 
                    AND table_name = $1
                )
            """, table)
            
            status = "✅" if exists else "❌"
            print(f"   {status} {table}")
            
            if not exists:
                missing_ai_tables.append(table)
        
        # Check database functions
        print("\n⚙️  Checking database functions...")
        functions = ['update_user_balance', 'apply_reward', 'get_user_behavior_score']
        
        for func in functions:
            exists = await conn.fetchval("""
                SELECT EXISTS (
                    SELECT FROM information_schema.routines 
                    WHERE routine_schema = 'public' 
                    AND routine_name = $1
                )
            """, func)
            
            status = "✅" if exists else "❌"
            print(f"   {status} {func}()")
        
        # Test connection pool settings
        print("\n🏊 Testing connection pool settings...")
        min_pool = int(os.getenv('DB_MIN_POOL_SIZE', '5'))
        max_pool = int(os.getenv('DB_MAX_POOL_SIZE', '20'))
        
        print(f"   Min Pool Size: {min_pool}")
        print(f"   Max Pool Size: {max_pool}")
        
        if min_pool > max_pool:
            print("   ⚠️  Warning: Min pool size is greater than max pool size")
        
        await conn.close()
        
        # Summary
        print("\n" + "=" * 50)
        if missing_ai_tables:
            print("⚠️  Setup Required:")
            print("   Some AI Brain tables are missing. Please run:")
            print("   1. backend/database/schema.sql (if not already done)")
            print("   2. backend/database/ai_brain_schema.sql")
            print("   in your Supabase SQL Editor")
            return False
        else:
            print("✅ All validations passed!")
            print("🚀 AI Brain is ready to start")
            return True
            
    except asyncpg.InvalidAuthorizationSpecificationError:
        print("❌ Authentication failed - check your database password")
        return False
    except asyncpg.InvalidCatalogNameError:
        print("❌ Database not found - check your database name")
        return False
    except asyncpg.CannotConnectNowError:
        print("❌ Cannot connect to database - check your connection string")
        return False
    except Exception as e:
        print(f"❌ Connection failed: {str(e)}")
        return False

def validate_environment():
    """Validate environment configuration."""
    print("\n🔧 Validating Environment Configuration...")
    print("=" * 50)
    
    required_vars = [
        'DATABASE_URL',
        'AI_ENCRYPTION_KEY'
    ]
    
    missing_vars = []
    for var in required_vars:
        value = os.getenv(var)
        if not value:
            missing_vars.append(var)
            print(f"❌ {var}: Not set")
        elif var == 'AI_ENCRYPTION_KEY' and len(value) < 32:
            print(f"⚠️  {var}: Should be at least 32 characters")
        else:
            print(f"✅ {var}: Configured")
    
    # Check optional but important vars
    optional_vars = [
        'AI_LOG_LEVEL',
        'NODE_ENV',
        'AI_ENABLE_PERFORMANCE_MONITORING'
    ]
    
    print("\n📋 Optional Configuration:")
    for var in optional_vars:
        value = os.getenv(var, 'Not set')
        print(f"   {var}: {value}")
    
    return len(missing_vars) == 0

async def main():
    """Main validation function."""
    print("🧠 PayVendy AI Brain - Supabase Validation")
    print("=" * 50)
    
    # Validate environment
    env_valid = validate_environment()
    
    if not env_valid:
        print("\n❌ Environment validation failed")
        sys.exit(1)
    
    # Validate database connection
    db_valid = await validate_connection()
    
    if not db_valid:
        print("\n❌ Database validation failed")
        sys.exit(1)
    
    print("\n🎉 All validations passed! AI Brain is ready to start.")
    print("\nTo start the AI Brain:")
    print("   python start.py")

if __name__ == "__main__":
    asyncio.run(main())