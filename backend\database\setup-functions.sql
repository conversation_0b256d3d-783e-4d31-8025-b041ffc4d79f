-- =====================================================
-- VENDY DATABASE SETUP FUNCTIONS
-- =====================================================
-- Optional RPC functions for table creation
-- Run this script if you want to use RPC-based table creation

-- Function to create users table
CREATE OR REPLACE FUNCTION create_users_table()
RETURNS BOOLEAN AS $$
BEGIN
    CREATE TABLE IF NOT EXISTS users (
        id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
        phone_number VARCHAR(15) UNIQUE NOT NULL,
        first_name VA<PERSON>HA<PERSON>(50),
        last_name VARCHAR(50),
        email VARCHAR(255),
        pin TEXT NOT NULL, -- Encrypted PIN
        is_phone_verified BOOLEAN DEFAULT FALSE,
        balance DECIMAL(15,2) DEFAULT 0.00 CHECK (balance >= 0),
        is_active BOOLEAN DEFAULT TRUE,
        role VARCHAR(20) DEFAULT 'user' CHECK (role IN ('user', 'admin')),
        
        -- Security fields
        login_attempts INTEGER DEFAULT 0,
        lock_until TIMESTAMP WITH TIME ZONE,
        last_login TIMESTAMP WITH TIME ZONE,
        ip_addresses JSONB DEFAULT '[]'::jsonb,
        devices JSONB DEFAULT '[]'::jsonb,
        
        -- Verification tokens
        phone_verification_token TEXT,
        phone_verification_expires TIMESTAMP WITH TIME ZONE,
        
        -- Timestamps
        created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
        updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
    );
    
    -- Create indexes
    CREATE INDEX IF NOT EXISTS idx_users_phone_number ON users(phone_number);
    CREATE INDEX IF NOT EXISTS idx_users_email ON users(email);
    CREATE INDEX IF NOT EXISTS idx_users_is_active ON users(is_active);
    CREATE INDEX IF NOT EXISTS idx_users_created_at ON users(created_at);
    
    RETURN TRUE;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Function to create sessions table
CREATE OR REPLACE FUNCTION create_sessions_table()
RETURNS BOOLEAN AS $$
BEGIN
    CREATE TABLE IF NOT EXISTS sessions (
        id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
        user_id UUID REFERENCES users(id) ON DELETE CASCADE,
        access_token_hash TEXT NOT NULL,
        refresh_token_hash TEXT,
        device_info JSONB,
        ip_address INET,
        user_agent TEXT,
        is_active BOOLEAN DEFAULT TRUE,
        expires_at TIMESTAMP WITH TIME ZONE NOT NULL,
        created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
        updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
    );
    
    -- Create indexes
    CREATE INDEX IF NOT EXISTS idx_sessions_user_id ON sessions(user_id);
    CREATE INDEX IF NOT EXISTS idx_sessions_access_token_hash ON sessions(access_token_hash);
    CREATE INDEX IF NOT EXISTS idx_sessions_is_active ON sessions(is_active);
    CREATE INDEX IF NOT EXISTS idx_sessions_expires_at ON sessions(expires_at);
    
    RETURN TRUE;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Function to create transactions table
CREATE OR REPLACE FUNCTION create_transactions_table()
RETURNS BOOLEAN AS $$
BEGIN
    CREATE TABLE IF NOT EXISTS transactions (
        id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
        user_id UUID REFERENCES users(id) ON DELETE CASCADE,
        type VARCHAR(50) NOT NULL, -- 'airtime', 'data', 'electricity', 'cable', etc.
        amount DECIMAL(15,2) NOT NULL,
        recipient VARCHAR(255), -- Phone number or meter number
        provider VARCHAR(100), -- MTN, Airtel, PHCN, etc.
        status VARCHAR(20) DEFAULT 'pending' CHECK (status IN ('pending', 'completed', 'failed', 'cancelled')),
        reference VARCHAR(100) UNIQUE NOT NULL,
        external_reference VARCHAR(100), -- Provider's reference
        description TEXT,
        metadata JSONB DEFAULT '{}'::jsonb,
        created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
        updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
    );
    
    -- Create indexes
    CREATE INDEX IF NOT EXISTS idx_transactions_user_id ON transactions(user_id);
    CREATE INDEX IF NOT EXISTS idx_transactions_reference ON transactions(reference);
    CREATE INDEX IF NOT EXISTS idx_transactions_status ON transactions(status);
    CREATE INDEX IF NOT EXISTS idx_transactions_type ON transactions(type);
    CREATE INDEX IF NOT EXISTS idx_transactions_created_at ON transactions(created_at);
    
    RETURN TRUE;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Function to setup all tables at once
CREATE OR REPLACE FUNCTION setup_vendy_database()
RETURNS TEXT AS $$
DECLARE
    result TEXT := '';
BEGIN
    -- Enable extensions
    CREATE EXTENSION IF NOT EXISTS "uuid-ossp";
    CREATE EXTENSION IF NOT EXISTS "pgcrypto";
    
    -- Create tables
    PERFORM create_users_table();
    result := result || 'Users table created. ';
    
    PERFORM create_sessions_table();
    result := result || 'Sessions table created. ';
    
    PERFORM create_transactions_table();
    result := result || 'Transactions table created. ';
    
    -- Create update trigger function if not exists
    CREATE OR REPLACE FUNCTION update_updated_at_column()
    RETURNS TRIGGER AS $trigger$
    BEGIN
        NEW.updated_at = NOW();
        RETURN NEW;
    END;
    $trigger$ language 'plpgsql';
    
    -- Create triggers
    DROP TRIGGER IF EXISTS update_users_updated_at ON users;
    CREATE TRIGGER update_users_updated_at BEFORE UPDATE ON users
        FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
        
    DROP TRIGGER IF EXISTS update_sessions_updated_at ON sessions;
    CREATE TRIGGER update_sessions_updated_at BEFORE UPDATE ON sessions
        FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
        
    DROP TRIGGER IF EXISTS update_transactions_updated_at ON transactions;
    CREATE TRIGGER update_transactions_updated_at BEFORE UPDATE ON transactions
        FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
    
    result := result || 'Triggers created. ';
    
    RETURN result || 'Vendy database setup completed successfully!';
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Grant execute permissions to service role
GRANT EXECUTE ON FUNCTION create_users_table() TO service_role;
GRANT EXECUTE ON FUNCTION create_sessions_table() TO service_role;
GRANT EXECUTE ON FUNCTION create_transactions_table() TO service_role;
GRANT EXECUTE ON FUNCTION setup_vendy_database() TO service_role;

SELECT 'Setup functions created successfully! You can now call setup_vendy_database() to initialize your database.' as message;
