import React, { useEffect, useRef } from 'react';
import {
  Modal,
  View,
  StyleSheet,
  TouchableOpacity,
  Animated,
  Dimensions,
  PanResponder,
} from 'react-native';

const SCREEN_HEIGHT = Dimensions.get('window').height;

interface ModalBaseProps {
  visible: boolean;
  onClose: () => void;
  children: React.ReactNode;
  heightPercent?: number; // Optional height percentage for slideUp modals
  variant?: 'center' | 'slideUp'; // Animation variant
  enableSwipeDown?: boolean; // Enable swipe down to dismiss for slideUp modals
}

const ModalBase: React.FC<ModalBaseProps> = ({
  visible,
  onClose,
  children,
  heightPercent,
  variant = 'slideUp',
  enableSwipeDown = true,
}) => {
  // Animation values
  const slideAnim = useRef(new Animated.Value(variant === 'slideUp' ? SCREEN_HEIGHT : 0)).current;
  const opacityAnim = useRef(new Animated.Value(0)).current;
  const scaleAnim = useRef(new Animated.Value(0.9)).current;
  const pan = useRef(new Animated.ValueXY({ x: 0, y: 0 })).current;

  // Pan responder for swipe-down gesture (only for slideUp variant)
  const panResponder = useRef(
    PanResponder.create({
      onStartShouldSetPanResponder: () => variant === 'slideUp' && enableSwipeDown,
      onPanResponderMove: (e, gestureState) => {
        // Only allow dragging down (positive dy), completely ignore upward drags
        if (gestureState.dy >= 0) {
          pan.y.setValue(gestureState.dy);
        } else {
          pan.y.setValue(0); // Explicitly clamp to 0 to prevent any upward movement
        }
      },
      onPanResponderRelease: (e, gestureState) => {
        if (gestureState.dy > 50) {
          // If dragged down significantly, dismiss the modal with animation
          closeModal();
        } else {
          // If not dragged far enough, snap back to original position
          Animated.spring(pan.y, {
            toValue: 0,
            useNativeDriver: false,
          }).start();
        }
      },
    })
  ).current;

  const openModal = () => {
    if (variant === 'slideUp') {
      // SlideUp animation
      Animated.parallel([
        Animated.timing(slideAnim, {
          toValue: 0,
          duration: 300,
          useNativeDriver: true,
        }),
        Animated.timing(opacityAnim, {
          toValue: 1,
          duration: 300,
          useNativeDriver: true,
        }),
      ]).start();
    } else {
      // Center scale/fade animation
      Animated.parallel([
        Animated.timing(scaleAnim, {
          toValue: 1,
          duration: 300,
          useNativeDriver: true,
        }),
        Animated.timing(opacityAnim, {
          toValue: 1,
          duration: 300,
          useNativeDriver: true,
        }),
      ]).start();
    }
  };

  const closeModal = () => {
    if (variant === 'slideUp') {
      // SlideUp close animation
      Animated.parallel([
        Animated.timing(slideAnim, {
          toValue: SCREEN_HEIGHT,
          duration: 250,
          useNativeDriver: true,
        }),
        Animated.timing(opacityAnim, {
          toValue: 0,
          duration: 250,
          useNativeDriver: true,
        }),
      ]).start(() => {
        onClose();
        pan.setValue({ x: 0, y: 0 });
      });
    } else {
      // Center scale/fade close animation
      Animated.parallel([
        Animated.timing(scaleAnim, {
          toValue: 0.9,
          duration: 250,
          useNativeDriver: true,
        }),
        Animated.timing(opacityAnim, {
          toValue: 0,
          duration: 250,
          useNativeDriver: true,
        }),
      ]).start(() => {
        onClose();
      });
    }
  };

  useEffect(() => {
    if (visible) {
      // Add a slight delay to ensure rendering is complete before animation starts
      setTimeout(() => {
        openModal();
      }, 50);
    } else {
      // Reset animation values when modal is hidden
      if (variant === 'slideUp') {
        slideAnim.setValue(SCREEN_HEIGHT);
        opacityAnim.setValue(0);
        pan.setValue({ x: 0, y: 0 });
      } else {
        scaleAnim.setValue(0.9);
        opacityAnim.setValue(0);
      }
    }
  }, [visible]);

  if (!visible) return null;

  const getModalContentStyle = () => {
    if (variant === 'slideUp') {
      const modalHeight = heightPercent ? SCREEN_HEIGHT * (heightPercent / 100) : 'auto';
      
      return [
        styles.slideUpModal,
        {
          height: typeof modalHeight === 'number' ? modalHeight : undefined,
          maxHeight: typeof modalHeight === 'number' ? modalHeight : SCREEN_HEIGHT * 0.8,
          transform: [
            { translateY: slideAnim },
            { translateY: pan.y }
          ],
        }
      ];
    } else {
      return [
        styles.centerModal,
        {
          transform: [{ scale: scaleAnim }],
        }
      ];
    }
  };

  const getOverlayStyle = () => {
    return variant === 'slideUp' ? styles.slideUpOverlay : styles.centerOverlay;
  };

  return (
    <Modal visible={visible} transparent animationType="none" statusBarTranslucent>
      <View style={[styles.overlay, getOverlayStyle()]}>
        <TouchableOpacity 
          style={styles.overlayTouchable} 
          activeOpacity={1} 
          onPress={closeModal}
        />
        <Animated.View
          {...(variant === 'slideUp' && enableSwipeDown ? panResponder.panHandlers : {})}
          style={[
            getModalContentStyle(),
            { opacity: opacityAnim }
          ]}
        >
          {variant === 'slideUp' && (
            <View style={styles.handle} />
          )}
          {children}
        </Animated.View>
      </View>
    </Modal>
  );
};

const styles = StyleSheet.create({
  overlay: {
    flex: 1,
    backgroundColor: 'rgba(0, 0, 0, 0.55)',
  },
  slideUpOverlay: {
    justifyContent: 'flex-end',
  },
  centerOverlay: {
    justifyContent: 'center',
    alignItems: 'center',
    paddingHorizontal: 20,
  },
  overlayTouchable: {
    flex: 1,
  },
  slideUpModal: {
    backgroundColor: '#181818',
    borderTopLeftRadius: 24,
    borderTopRightRadius: 24,
    paddingTop: 16,
    paddingBottom: 32,
    paddingHorizontal: 18,
    alignItems: 'center',
  },
  centerModal: {
    backgroundColor: '#232323',
    borderRadius: 24,
    padding: 32,
    width: '100%',
    maxWidth: 400,
    alignItems: 'center',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.25,
    shadowRadius: 4,
    elevation: 5,
  },
  handle: {
    width: 40,
    height: 5,
    borderRadius: 3,
    backgroundColor: '#444',
    alignSelf: 'center',
    marginBottom: 12,
  },
});

export default ModalBase;
