import React, { createContext, useContext, useEffect, useState, ReactNode } from 'react';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { userService, UserProfile } from '../services/userService';

// Replace this with your actual API call
async function getProfileFromApi(): Promise<UserProfile> {
  const res = await userService.getUserProfile();
  return res.user;
}

interface ProfileContextType {
  profile: UserProfile | null;
  refreshProfile: () => Promise<void>;
  loading: boolean;
}

const ProfileContext = createContext<ProfileContextType>({
  profile: null,
  refreshProfile: async () => {},
  loading: false,
});

const PROFILE_CACHE_KEY = 'profile_data';

export const ProfileProvider = ({ children }: { children: ReactNode }) => {
  const [profile, setProfile] = useState<UserProfile | null>(null);
  const [loading, setLoading] = useState(true);

  // Load cached profile on mount
  useEffect(() => {
    (async () => {
      try {
        const cached = await AsyncStorage.getItem(PROFILE_CACHE_KEY);
        if (cached) {
          setProfile(JSON.parse(cached));
        }
      } catch {}
      setLoading(false);
      // Always try to refresh in background
      refreshProfile();
    })();
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  // Fetch and update profile, update cache, never show error
  const refreshProfile = async () => {
    try {
      const data = await getProfileFromApi();
      setProfile(data);
      await AsyncStorage.setItem(PROFILE_CACHE_KEY, JSON.stringify(data));
    } catch {
      // Silent fail: do not update UI, do not show error
    }
  };

  return (
    <ProfileContext.Provider value={{ profile, refreshProfile, loading }}>
      {children}
    </ProfileContext.Provider>
  );
};

export const useProfile = () => useContext(ProfileContext);
