import { InteractionManager, Platform } from 'react-native';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { logger } from './productionLogger';

interface PerformanceMetric {
  id: string;
  name: string;
  startTime: number;
  endTime?: number;
  duration?: number;
  metadata?: Record<string, any>;
  timestamp: string;
}

interface MemoryMetric {
  timestamp: string;
  jsHeapSizeUsed?: number;
  jsHeapSizeTotal?: number;
  jsHeapSizeLimit?: number;
}

interface NavigationMetric {
  screenName: string;
  startTime: number;
  endTime?: number;
  duration?: number;
  timestamp: string;
}

class PerformanceService {
  private metrics: Map<string, PerformanceMetric> = new Map();
  private navigationMetrics: NavigationMetric[] = [];
  private memoryMetrics: MemoryMetric[] = [];
  private isEnabled = true;
  private maxMetrics = 100;

  constructor() {
    this.initializeService();
  }

  private async initializeService() {
    // Check if performance monitoring is enabled
    try {
      const enabled = await AsyncStorage.getItem('performance_monitoring_enabled');
      this.isEnabled = enabled !== 'false';
    } catch (error) {
      logger.warn('Failed to load performance monitoring setting', {
        service: 'performanceService',
        method: 'initializeService',
        error: error instanceof Error ? error.message : String(error)
      });
    }

    // Start memory monitoring in production
    if (!__DEV__ && this.isEnabled) {
      this.startMemoryMonitoring();
    }
  }

  setEnabled(enabled: boolean) {
    this.isEnabled = enabled;
    AsyncStorage.setItem('performance_monitoring_enabled', enabled.toString());
  }

  // Start timing a performance metric
  startTiming(name: string, metadata?: Record<string, any>): string {
    if (!this.isEnabled) return '';

    const id = `${name}_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
    const metric: PerformanceMetric = {
      id,
      name,
      startTime: performance.now(),
      metadata,
      timestamp: new Date().toISOString(),
    };

    this.metrics.set(id, metric);
    return id;
  }

  // End timing a performance metric
  endTiming(id: string): number | null {
    if (!this.isEnabled || !id) return null;

    const metric = this.metrics.get(id);
    if (!metric) {
      logger.warn('Performance metric not found', {
        service: 'performanceService',
        method: 'endTiming',
        metricId: id
      });
      return null;
    }

    const endTime = performance.now();
    const duration = endTime - metric.startTime;

    metric.endTime = endTime;
    metric.duration = duration;

    // Log slow operations
    if (duration > 1000) { // More than 1 second
      logger.warn('Slow operation detected', {
        service: 'performanceService',
        method: 'endTiming',
        metricName: metric.name,
        duration: duration,
        durationMs: `${duration.toFixed(2)}ms`,
        threshold: '1000ms',
        metadata: metric.metadata
      });
    }

    // Store completed metric
    this.storeMetric(metric);

    // Clean up
    this.metrics.delete(id);

    return duration;
  }

  // Measure a function execution time
  async measureAsync<T>(
    name: string,
    fn: () => Promise<T>,
    metadata?: Record<string, any>
  ): Promise<T> {
    const id = this.startTiming(name, metadata);
    try {
      const result = await fn();
      this.endTiming(id);
      return result;
    } catch (error) {
      this.endTiming(id);
      throw error;
    }
  }

  // Measure synchronous function execution time
  measure<T>(
    name: string,
    fn: () => T,
    metadata?: Record<string, any>
  ): T {
    const id = this.startTiming(name, metadata);
    try {
      const result = fn();
      this.endTiming(id);
      return result;
    } catch (error) {
      this.endTiming(id);
      throw error;
    }
  }

  // Track navigation performance
  startNavigationTiming(screenName: string) {
    if (!this.isEnabled) return;

    const metric: NavigationMetric = {
      screenName,
      startTime: performance.now(),
      timestamp: new Date().toISOString(),
    };

    this.navigationMetrics.push(metric);

    // Keep only recent metrics
    if (this.navigationMetrics.length > this.maxMetrics) {
      this.navigationMetrics = this.navigationMetrics.slice(-this.maxMetrics);
    }
  }

  endNavigationTiming(screenName: string) {
    if (!this.isEnabled) return;

    const metric = this.navigationMetrics
      .reverse()
      .find(m => m.screenName === screenName && !m.endTime);

    if (metric) {
      metric.endTime = performance.now();
      metric.duration = metric.endTime - metric.startTime;

      // Log slow navigation
      if (metric.duration > 500) { // More than 500ms
        logger.warn('Slow navigation detected', {
          service: 'performanceService',
          method: 'endNavigationTiming',
          screenName: screenName,
          duration: metric.duration,
          durationMs: `${metric.duration.toFixed(2)}ms`,
          threshold: '500ms'
        });
      }
    }

    // Reverse back to maintain order
    this.navigationMetrics.reverse();
  }

  // Track API call performance
  trackApiCall(method: string, url: string, duration: number, status: number) {
    if (!this.isEnabled) return;

    const metric: PerformanceMetric = {
      id: `api_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
      name: 'api_call',
      startTime: performance.now() - duration,
      endTime: performance.now(),
      duration,
      metadata: {
        method,
        url,
        status,
      },
      timestamp: new Date().toISOString(),
    };

    // Log slow API calls
    if (duration > 3000) { // More than 3 seconds
      logger.warn('Slow API call detected', {
        service: 'performanceService',
        method: 'trackApiCall',
        apiMethod: method,
        url: url,
        duration: duration,
        durationMs: `${duration.toFixed(2)}ms`,
        threshold: '3000ms',
        status: status
      });
    }

    this.storeMetric(metric);
  }

  // Memory monitoring
  private startMemoryMonitoring() {
    const checkMemory = () => {
      if (!this.isEnabled) return;

      // Use InteractionManager to avoid blocking UI
      InteractionManager.runAfterInteractions(() => {
        try {
          // @ts-ignore - performance.memory is available in some environments
          const memory = global.performance?.memory;
          
          if (memory) {
            const metric: MemoryMetric = {
              timestamp: new Date().toISOString(),
              jsHeapSizeUsed: memory.usedJSHeapSize,
              jsHeapSizeTotal: memory.totalJSHeapSize,
              jsHeapSizeLimit: memory.jsHeapSizeLimit,
            };

            this.memoryMetrics.push(metric);

            // Keep only recent metrics
            if (this.memoryMetrics.length > this.maxMetrics) {
              this.memoryMetrics = this.memoryMetrics.slice(-this.maxMetrics);
            }

            // Warn about high memory usage
            if (memory.usedJSHeapSize > memory.jsHeapSizeLimit * 0.8) {
              logger.warn('High memory usage detected', {
                service: 'performanceService',
                method: 'startMemoryMonitoring',
                usedHeapSize: memory.usedJSHeapSize,
                heapSizeLimit: memory.jsHeapSizeLimit,
                usagePercentage: Math.round((memory.usedJSHeapSize / memory.jsHeapSizeLimit) * 100),
                threshold: '80%'
              });
            }
          }
        } catch (error) {
          // Silently fail - memory monitoring is not critical
        }
      });
    };

    // Check memory every 30 seconds
    setInterval(checkMemory, 30000);
  }

  // Store metric for later analysis
  private async storeMetric(metric: PerformanceMetric) {
    try {
      // In development, just log
      if (__DEV__) {
        logger.debug('Performance metric recorded', {
          service: 'performanceService',
          method: 'storeMetric',
          metricName: metric.name,
          duration: metric.duration,
          durationMs: metric.duration ? `${metric.duration.toFixed(2)}ms` : undefined,
          metadata: metric.metadata,
          environment: 'development'
        });
        return;
      }

      // In production, store for batch upload
      const key = `perf_metric_${metric.id}`;
      await AsyncStorage.setItem(key, JSON.stringify(metric));

      // TODO: Implement batch upload to analytics service
    } catch (error) {
      // Don't throw - performance monitoring shouldn't break the app
      logger.warn('Failed to store performance metric', {
        service: 'performanceService',
        method: 'storeMetric',
        error: error instanceof Error ? error.message : String(error),
        metricId: metric.id,
        metricName: metric.name
      });
    }
  }

  // Get performance summary
  getPerformanceSummary() {
    const navigationSummary = this.navigationMetrics
      .filter(m => m.duration)
      .reduce((acc, metric) => {
        if (!acc[metric.screenName]) {
          acc[metric.screenName] = {
            count: 0,
            totalDuration: 0,
            avgDuration: 0,
            maxDuration: 0,
          };
        }

        const stats = acc[metric.screenName];
        stats.count++;
        stats.totalDuration += metric.duration!;
        stats.avgDuration = stats.totalDuration / stats.count;
        stats.maxDuration = Math.max(stats.maxDuration, metric.duration!);

        return acc;
      }, {} as Record<string, any>);

    return {
      navigation: navigationSummary,
      memoryUsage: this.memoryMetrics.slice(-10), // Last 10 memory readings
      activeTimings: this.metrics.size,
    };
  }

  // Clear old metrics
  async clearOldMetrics(olderThanDays: number = 7) {
    try {
      const keys = await AsyncStorage.getAllKeys();
      const metricKeys = keys.filter(key => key.startsWith('perf_metric_'));
      
      const cutoffDate = new Date();
      cutoffDate.setDate(cutoffDate.getDate() - olderThanDays);

      const keysToDelete: string[] = [];

      for (const key of metricKeys) {
        try {
          const metricData = await AsyncStorage.getItem(key);
          if (metricData) {
            const metric = JSON.parse(metricData);
            if (new Date(metric.timestamp) < cutoffDate) {
              keysToDelete.push(key);
            }
          }
        } catch (parseError) {
          // If we can't parse it, delete it
          keysToDelete.push(key);
        }
      }

      if (keysToDelete.length > 0) {
        await AsyncStorage.multiRemove(keysToDelete);
        logger.info('Cleared old performance metrics', {
          service: 'performanceService',
          method: 'clearOldMetrics',
          clearedCount: keysToDelete.length,
          olderThanDays: olderThanDays
        });
      }
    } catch (error) {
      logger.error('Failed to clear old metrics', {
        service: 'performanceService',
        method: 'clearOldMetrics',
        error: error instanceof Error ? error.message : String(error),
        olderThanDays: olderThanDays
      });
    }
  }

  // React component performance helpers
  createComponentTimer(componentName: string) {
    return {
      onMount: () => this.startTiming(`${componentName}_mount`),
      onUnmount: (timerId: string) => this.endTiming(timerId),
      onRender: () => this.startTiming(`${componentName}_render`),
      onRenderComplete: (timerId: string) => this.endTiming(timerId),
    };
  }
}

export const performanceService = new PerformanceService();
export default performanceService;