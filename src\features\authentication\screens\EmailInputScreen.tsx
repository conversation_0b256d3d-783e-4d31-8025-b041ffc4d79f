import React, { useState, useRef, memo, useCallback, useMemo, useEffect } from "react"
import { View, Text, StyleSheet, TouchableOpacity, StatusBar, TextInput, Animated, Alert, ImageBackground, Platform, KeyboardAvoidingView } from "react-native"
import { useTheme } from "../../../shared/components/layout/ThemeContext"
import { useOptimizedAnimation, usePerformanceMonitor } from "../../../shared/utils/performance"
import type { NativeStackScreenProps } from "@react-navigation/native-stack"
import type { RootStackParamList } from "../../../navigation/navigation"
import ApiService from "../../../infrastructure/api/apiService"
import ArrowRightIcon from "../../../shared/components/icons/ArrowRightIcon"
import { logger } from '../../../infrastructure/monitoring/productionLogger';
import ReactNativeHapticFeedback from "react-native-haptic-feedback"
import Svg, { Path } from "react-native-svg"
import { navigationHandler } from '../../../navigation/handlers/navigationHandler';

type Props = NativeStackScreenProps<RootStackParamList, "EmailInput">

// Debounce utility function
const debounce = (func: Function, wait: number) => {
  let timeout: NodeJS.Timeout;
  return function executedFunction(...args: any[]) {
    const later = () => {
      clearTimeout(timeout);
      func(...args);
    };
    clearTimeout(timeout);
    timeout = setTimeout(later, wait);
  };
};

const EmailInputScreen = memo<Props>(({ }) => {
  const { theme, isDark } = useTheme()
  const [email, setEmail] = useState("")
  const [isEmailFocused, setIsEmailFocused] = useState(false)
  const [isLoading, setIsLoading] = useState(false)
  const [isBackButtonPressed, setIsBackButtonPressed] = useState(false)
  const [isContinuePressed, setIsContinuePressed] = useState(false)
  const [isEmailValid, setIsEmailValid] = useState(false)
  const [showError, setShowError] = useState(false)
  const [errorMessage, setErrorMessage] = useState("")
  const [hasUserInteracted, setHasUserInteracted] = useState(false)
  const emailInputRef = useRef<TextInput>(null)

  // Performance monitoring
  usePerformanceMonitor('EmailInputScreen')

  // Optimized animations
  const { animatedValue: fadeAnim, animate: animateFade } = useOptimizedAnimation(0)
  const { animatedValue: slideUpAnim, animate: animateSlideUp } = useOptimizedAnimation(30)
  const labelAnim = useRef(new Animated.Value(0)).current
  const iconAnim = useRef(new Animated.Value(0)).current
  const continueButtonScale = useRef(new Animated.Value(1)).current
  const shakeAnim = useRef(new Animated.Value(0)).current
  const loadingAnim = useRef(new Animated.Value(0)).current
  const errorFadeAnim = useRef(new Animated.Value(0)).current
  const underlineAnim = useRef(new Animated.Value(0)).current

  useEffect(() => {
    Animated.parallel([
      Animated.timing(fadeAnim, {
        toValue: 1,
        duration: 800,
        useNativeDriver: true,
      }),
      Animated.timing(slideUpAnim, {
        toValue: 0,
        duration: 800,
        useNativeDriver: true,
      }),
    ]).start()

    // Start loading animation loop
    const startLoadingAnimation = () => {
      Animated.loop(
        Animated.timing(loadingAnim, {
          toValue: 1,
          duration: 1000,
          useNativeDriver: true,
        })
      ).start()
    }
    startLoadingAnimation()
  }, [fadeAnim, slideUpAnim, loadingAnim])

  // Improved haptic feedback utility with reduced frequency
  const triggerHaptic = useCallback((type: 'light' | 'medium' | 'heavy' | 'success' | 'error') => {
    const hapticOptions = {
      enableVibrateFallback: true,
      ignoreAndroidSystemSettings: false, // Respect system settings
    }

    try {
      switch (type) {
        case 'light':
          ReactNativeHapticFeedback.trigger("impactLight", hapticOptions)
          break
        case 'medium':
          ReactNativeHapticFeedback.trigger("impactMedium", hapticOptions)
          break
        case 'heavy':
          ReactNativeHapticFeedback.trigger("impactHeavy", hapticOptions)
          break
        case 'success':
          ReactNativeHapticFeedback.trigger("notificationSuccess", hapticOptions)
          break
        case 'error':
          ReactNativeHapticFeedback.trigger("notificationError", hapticOptions)
          break
      }
    } catch (error) {
      logger.warn('Haptic feedback failed', error, 'email_input');
    }
  }, [])

  // Enhanced email sanitization and validation
  const sanitizeEmail = useCallback((emailInput: string): string => {
    return emailInput.trim().toLowerCase().replace(/[<>]/g, '');
  }, []);

  const validateEmailSecurity = useCallback((emailInput: string): { isValid: boolean; error?: string } => {
    const sanitized = sanitizeEmail(emailInput);
    
    if (!sanitized) {
      return { isValid: false, error: 'Email is required' };
    }
    
    // Basic format check
    const emailRegex = /^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$/;
    if (!emailRegex.test(sanitized)) {
      return { isValid: false, error: 'Please enter a valid email address' };
    }
    
    // Check for suspicious patterns
    if (sanitized.includes('script') || sanitized.includes('javascript:')) {
      return { isValid: false, error: 'Invalid email content detected' };
    }
    
    return { isValid: true };
  }, [sanitizeEmail]);

  // Debounced validation to reduce excessive calls
  const debouncedValidation = useCallback(
    debounce((emailInput: string) => {
      if (!hasUserInteracted && !emailInput) return;
      
      const validation = validateEmailSecurity(emailInput);
      setIsEmailValid(validation.isValid);
      
      if (!validation.isValid && emailInput && hasUserInteracted) {
        setShowError(true);
        setErrorMessage(validation.error || 'Invalid email address');
        
        // Animate error appearance
        Animated.timing(errorFadeAnim, {
          toValue: 1,
          duration: 300,
          useNativeDriver: true,
        }).start();
        
        // Shake animation for invalid email
        Animated.sequence([
          Animated.timing(shakeAnim, { toValue: 8, duration: 100, useNativeDriver: true }),
          Animated.timing(shakeAnim, { toValue: -8, duration: 100, useNativeDriver: true }),
          Animated.timing(shakeAnim, { toValue: 8, duration: 100, useNativeDriver: true }),
          Animated.timing(shakeAnim, { toValue: 0, duration: 100, useNativeDriver: true }),
        ]).start();
        
        triggerHaptic('error');
      } else {
        setShowError(false);
        setErrorMessage("");
        
        // Animate error disappearance
        Animated.timing(errorFadeAnim, {
          toValue: 0,
          duration: 300,
          useNativeDriver: true,
        }).start();
      }
    }, 300),
    [validateEmailSecurity, hasUserInteracted, triggerHaptic, errorFadeAnim, shakeAnim]
  );

  // Check email validity with debounced validation
  useEffect(() => {
    debouncedValidation(email);
  }, [email, debouncedValidation]);

  // Enhanced continue handler with better error recovery
  const handleContinue = useCallback(async () => {
    if (!email.trim() || isLoading) return;

    const sanitizedEmail = sanitizeEmail(email);
    const validation = validateEmailSecurity(sanitizedEmail);
    
    if (!validation.isValid) {
      logger.security('INVALID_EMAIL_INPUT_ATTEMPT', {
        emailDomain: sanitizedEmail.split('@')[1] || 'unknown',
        error: validation.error
      });
      setShowError(true);
      setErrorMessage(validation.error || 'Please enter a valid email address');
      triggerHaptic('error');
      return;
    }

    setIsLoading(true);

    try {
      logger.userAction('email_otp_requested', {
        emailDomain: sanitizedEmail.split('@')[1] || 'unknown',
        hasEmail: !!sanitizedEmail
      });
      
      const response = await ApiService.sendEmailOTP(sanitizedEmail, 'verification');

      if (response.status === 'success' || response.data?.status === 'success') {
        logger.info('Email OTP sent successfully', {
          emailDomain: sanitizedEmail.split('@')[1] || 'unknown'
        }, 'email_input');
        triggerHaptic('success');
        navigationHandler.navigateToEmailVerification(sanitizedEmail);
      } else {
        const errorMsg = response.message || response.data?.message || 'Failed to send verification code';
        logger.warn('Failed to send email OTP', {
          error: errorMsg,
          emailDomain: sanitizedEmail.split('@')[1] || 'unknown'
        }, 'email_input');
        
        Alert.alert(
          'Connection Error',
          'Unable to send verification code. Please check your connection and try again.',
          [
            { text: 'Cancel', style: 'cancel' },
            { text: 'Retry', onPress: handleContinue }
          ]
        );
      }
    } catch (error) {
      logger.error('Error sending email OTP', error, 'email_input');
      const errorMessage = (typeof error === 'object' && error !== null && 'message' in error)
        ? (error as { message?: string }).message
        : undefined;
      
      Alert.alert(
        'Connection Error',
        'Unable to send verification code. Please check your connection and try again.',
        [
          { text: 'Cancel', style: 'cancel' },
          { text: 'Retry', onPress: handleContinue }
        ]
      );
    } finally {
      setIsLoading(false);
    }
  }, [email, isLoading, triggerHaptic, sanitizeEmail, validateEmailSecurity]);

  const handleBack = useCallback(() => {
    triggerHaptic('light');
    navigationHandler.goBack();
  }, [triggerHaptic]);

  // Enhanced focus handlers with underline animation
  const handleFocus = useCallback(() => {
    setIsEmailFocused(true);
    setHasUserInteracted(true);
    
    Animated.parallel([
      Animated.timing(labelAnim, {
        toValue: 1,
        duration: 200,
        useNativeDriver: false,
      }),
      Animated.timing(iconAnim, {
        toValue: 1,
        duration: 200,
        useNativeDriver: false,
      }),
      Animated.timing(underlineAnim, {
        toValue: 1,
        duration: 200,
        useNativeDriver: false,
      })
    ]).start();
    
    triggerHaptic('light');
  }, [labelAnim, iconAnim, underlineAnim, triggerHaptic]);

  const handleBlur = useCallback(() => {
    setIsEmailFocused(false);
    
    if (!email) {
      Animated.parallel([
        Animated.timing(labelAnim, {
          toValue: 0,
          duration: 200,
          useNativeDriver: false,
        }),
        Animated.timing(iconAnim, {
          toValue: 0,
          duration: 200,
          useNativeDriver: false,
        })
      ]).start();
    }
    
    Animated.timing(underlineAnim, {
      toValue: 0,
      duration: 200,
      useNativeDriver: false,
    }).start();
  }, [email, labelAnim, iconAnim, underlineAnim]);

  // Enhanced text change handler with reduced haptic frequency
  const handleTextChange = useCallback((text: string) => {
    setEmail(text);
    setHasUserInteracted(true);
    
    // Only trigger haptic on significant changes (every 3 characters)
    if (text.length % 3 === 0 && text.length > 0) {
      triggerHaptic('light');
    }
  }, [triggerHaptic]);

  // Memoized styles with enhanced underline design and better text alignment
  const styles = useMemo(() => StyleSheet.create({
    container: {
      flex: 1,
      backgroundColor: theme.colors.background,
    },
    header: {
      paddingTop: Platform.OS === 'ios' ? 60 : 40,
      paddingHorizontal: 20,
      paddingBottom: 20,
    },
    backButton: {
      width: 44,
      height: 44,
      justifyContent: 'center',
      alignItems: 'center',
      borderRadius: 22,
      backgroundColor: isDark ? 'rgba(255, 255, 255, 0.1)' : 'rgba(0, 0, 0, 0.05)',
    },
    backButtonPressed: {
      transform: [{ scale: 0.95 }],
      backgroundColor: isDark ? 'rgba(255, 255, 255, 0.15)' : 'rgba(0, 0, 0, 0.1)',
    },
    titleContainer: {
      paddingHorizontal: 30,
      marginBottom: 40,
    },
    title: {
      fontSize: 28,
      fontWeight: '700',
      color: theme.colors.text,
      marginBottom: 8,
      textAlign: 'center',
      fontFamily: 'InterVariable',
      lineHeight: 34,
    },
    subtitle: {
      fontSize: 16,
      fontWeight: '400',
      color: theme.colors.text,
      opacity: 0.7,
      textAlign: 'center',
      fontFamily: 'InterVariable',
      lineHeight: 22,
    },
    content: {
      flex: 1,
      paddingHorizontal: 30,
      paddingBottom: 120, // Add bottom padding to prevent content from being hidden behind footer
    },
    inputContainer: {
      marginBottom: 40,
      position: 'relative',
      paddingTop: 20,
      paddingBottom: 8, // Reduced bottom padding for better text alignment
    },
    iconContainer: {
      position: 'absolute',
      left: 0,
      justifyContent: 'center',
      alignItems: 'center',
      zIndex: 2,
      width: 40,
    },
    floatingLabel: {
      position: 'absolute',
      left: 40,
      color: theme.colors.text,
      opacity: 0.7,
      zIndex: 1,
      fontFamily: 'InterVariable',
    },
    input: {
      borderWidth: 0,
      paddingTop: 8, // Reduced top padding
      paddingBottom: 8, // Reduced bottom padding for better centering
      paddingLeft: 40,
      paddingRight: 0,
      fontSize: 16,
      color: theme.colors.text,
      backgroundColor: 'transparent',
      textAlign: 'left',
      width: '100%',
      minHeight: 40, // Reduced height for better proportion
      fontFamily: 'InterVariable',
      lineHeight: 20, // Added explicit line height for better text centering
      textAlignVertical: 'center', // Android-specific vertical centering
    },
    underline: {
      position: 'absolute',
      bottom: 0,
      left: 0,
      right: 0,
      height: 1,
      backgroundColor: isDark ? 'rgba(255, 255, 255, 0.2)' : 'rgba(0, 0, 0, 0.2)',
    },
    focusedUnderline: {
      position: 'absolute',
      bottom: 0,
      left: 0,
      height: 2,
      backgroundColor: showError && !isEmailValid 
        ? '#FF3B30' 
        : '#8A2BE2',
    },
    errorContainer: {
      marginTop: 8,
      marginLeft: 5,
    },
    errorText: {
      color: '#FF3B30',
      fontSize: 14,
      fontFamily: 'InterVariable',
      fontWeight: '500',
    },
    continueButton: {
      backgroundColor: isDark ? '#FFFFFF' : '#000000',
      borderRadius: 12,
      paddingVertical: 16,
      paddingHorizontal: 32,
      alignItems: 'center',
      justifyContent: 'center',
      marginBottom: 30,
      minHeight: 52,
      opacity: (email.trim() && isEmailValid && !isLoading) ? 1 : 0.4,
      transform: [{ scale: continueButtonScale }],
      shadowColor: isDark ? '#FFFFFF' : '#000000',
      shadowOffset: { width: 0, height: 2 },
      shadowOpacity: (email.trim() && isEmailValid && !isLoading) ? 0.1 : 0,
      shadowRadius: 4,
      elevation: (email.trim() && isEmailValid && !isLoading) ? 2 : 0,
    },
    continueButtonText: {
      color: isDark ? '#000000' : '#FFFFFF',
      fontSize: 16,
      fontWeight: '600',
      letterSpacing: 0.5,
      fontFamily: 'InterVariable',
    },
    loadingContainer: {
      flexDirection: 'row',
      alignItems: 'center',
    },
    loadingSpinner: {
      width: 20,
      height: 20,
      borderRadius: 10,
      borderWidth: 2,
      borderColor: isDark ? '#000000' : '#FFFFFF',
      borderTopColor: 'transparent',
      marginRight: 10,
    },
    footer: {
      paddingHorizontal: 30,
      paddingBottom: Platform.OS === 'ios' ? 40 : 30,
      paddingTop: 20, // Added top padding for better spacing
      backgroundColor: theme.colors.background, // Ensure footer has background color
    },
    scrollContent: {
      flexGrow: 1,
      justifyContent: 'space-between',
    },
  }), [theme, isDark, isEmailFocused, email, showError, isEmailValid, isLoading]);

  return (
    <View style={styles.container}>
      <StatusBar
        barStyle={isDark ? "light-content" : "dark-content"}
        backgroundColor="transparent"
        translucent={true}
      />
      <ImageBackground
        source={isDark ? require("../../../../assets/images/bg.jpeg") : require("../../../../assets/images/bg-white.jpeg")}
        style={StyleSheet.absoluteFillObject}
        resizeMode="cover"
      />
      
      <KeyboardAvoidingView 
        style={{ flex: 1 }}
        behavior={Platform.OS === 'ios' ? 'padding' : undefined}
        keyboardVerticalOffset={Platform.OS === 'ios' ? 0 : 0}
      >
        {/* Header */}
        <View style={styles.header}>
          <TouchableOpacity 
            style={[
              styles.backButton,
              isBackButtonPressed && styles.backButtonPressed
            ]}
            onPress={handleBack}
            onPressIn={() => setIsBackButtonPressed(true)}
            onPressOut={() => setIsBackButtonPressed(false)}
            activeOpacity={0.8}
            hitSlop={{ top: 10, bottom: 10, left: 10, right: 10 }}
            accessible={true}
            accessibilityLabel="Go back"
            accessibilityRole="button"
          >
            <Animated.View 
              style={{ 
                transform: [
                  { translateX: isBackButtonPressed ? -1 : 0 }
                ]
              }}
            >
              <BackArrowIcon 
                size={18} 
                color={theme.colors.text} 
              />
            </Animated.View>
          </TouchableOpacity>
        </View>
        
        {/* Title Section */}
        <View style={styles.titleContainer}>
          <Text style={styles.title}>
            Let's get started
          </Text>
          <Text style={styles.subtitle}>
            Enter your email to continue
          </Text>
        </View>
        
        {/* Content */}
        <Animated.View
          style={[
            styles.content,
            {
              opacity: fadeAnim,
              transform: [{ translateY: slideUpAnim }],
            }
          ]}
        >
          <TouchableOpacity 
            style={styles.inputContainer} 
            onPress={() => {
              if (!isEmailFocused && emailInputRef.current) {
                emailInputRef.current.focus();
              }
            }}
            activeOpacity={1}
            accessible={false}
          >
            <Animated.Text
              style={[
                styles.floatingLabel,
                {
                  fontSize: labelAnim.interpolate({
                    inputRange: [0, 1],
                    outputRange: [16, 12],
                  }),
                  top: labelAnim.interpolate({
                    inputRange: [0, 1],
                    outputRange: [28, 5], // Adjusted for better alignment
                  }),
                }
              ]}
            >
              Email address
            </Animated.Text>
            
            <Animated.View 
              style={[
                styles.iconContainer,
                {
                  top: iconAnim.interpolate({
                    inputRange: [0, 1],
                    outputRange: [28, 5], // Adjusted to match label
                  }),
                }
              ]}
            >
              <EmailIcon color={
                showError && !isEmailValid 
                  ? '#FF3B30'
                  : isEmailFocused 
                    ? '#8A2BE2'
                    : (isDark ? 'rgba(255, 255, 255, 0.6)' : 'rgba(0, 0, 0, 0.6)')
              } />
            </Animated.View>
            
            <Animated.View style={{ transform: [{ translateX: shakeAnim }] }}>
              <TextInput
                ref={emailInputRef}
                style={styles.input}
                value={email}
                onChangeText={handleTextChange}
                onFocus={handleFocus}
                onBlur={handleBlur}
                placeholder=""
                keyboardType="email-address"
                autoCapitalize="none"
                autoCorrect={false}
                autoComplete="email"
                textContentType="emailAddress"
                cursorColor={isDark ? '#FFFFFF' : '#000000'}
                selectionColor={isDark ? 'rgba(255, 255, 255, 0.3)' : 'rgba(0, 0, 0, 0.3)'}
                accessible={true}
                accessibilityLabel="Email address input field"
                accessibilityHint="Enter your email address to receive a verification code"
                accessibilityRole="text"
                returnKeyType="done"
                onSubmitEditing={handleContinue}
                editable={!isLoading}
              />
            </Animated.View>
            
            {/* Static underline */}
            <View style={styles.underline} />
            
            {/* Animated focus underline */}
            <Animated.View 
              style={[
                styles.focusedUnderline,
                {
                  width: underlineAnim.interpolate({
                    inputRange: [0, 1],
                    outputRange: ['0%', '100%'],
                  }),
                }
              ]}
            />
          </TouchableOpacity>
          
          {/* Inline Error Message */}
          {showError && errorMessage && (
            <Animated.View 
              style={[
                styles.errorContainer,
                { opacity: errorFadeAnim }
              ]}
            >
              <Text style={styles.errorText}>
                {errorMessage}
              </Text>
            </Animated.View>
          )}
        </Animated.View>
        
        {/* Footer */}
        <View style={styles.footer}>
          <TouchableOpacity
            style={styles.continueButton}
            onPress={handleContinue}
            disabled={!email.trim() || isLoading || !isEmailValid}
            activeOpacity={0.8}
            onPressIn={() => {
              setIsContinuePressed(true);
              triggerHaptic('medium');
              Animated.spring(continueButtonScale, {
                toValue: 0.95,
                useNativeDriver: true,
                friction: 4,
              }).start();
            }}
            onPressOut={() => {
              setIsContinuePressed(false);
              Animated.spring(continueButtonScale, {
                toValue: 1,
                useNativeDriver: true,
                friction: 4,
              }).start();
            }}
            accessible={true}
            accessibilityLabel={isLoading ? "Sending verification code" : "Continue with email"}
            accessibilityRole="button"
            accessibilityState={{ disabled: !email.trim() || isLoading || !isEmailValid }}
          >
            {isLoading ? (
              <View style={styles.loadingContainer}>
                <Animated.View
                  style={[
                    styles.loadingSpinner,
                    {
                      transform: [{ 
                        rotate: loadingAnim.interpolate({
                          inputRange: [0, 1],
                          outputRange: ['0deg', '360deg'],
                        })
                      }],
                    }
                  ]}
                />
                <Text style={styles.continueButtonText}>Sending...</Text>
              </View>
            ) : (
              <Text style={styles.continueButtonText}>Continue</Text>
            )}
          </TouchableOpacity>
        </View>
      </KeyboardAvoidingView>
    </View>
  )
})

const EmailIcon = memo(({ color }: { color: string }) => (
  <Svg width="20" height="20" viewBox="0 0 24 24" fill="none">
    <Path
      d="M20 4H4C2.9 4 2.01 4.9 2.01 6L2 18C2 19.1 2.9 20 4 20H20C21.1 20 22 19.1 22 18V6C22 4.9 21.1 4 20 4ZM20 8L12 13L4 8V6L12 11L20 6V8Z"
      fill={color}
    />
  </Svg>
))

const BackArrowIcon = memo(({ color, size }: { color: string, size: number }) => (
  <Svg width={size} height={size} viewBox="0 0 24 24" fill="none">
    <Path
      d="M20 11.5H9.41L14.46 6.46C14.85 6.07 14.85 5.44 14.46 5.05C14.27 4.86 14.02 4.76 13.76 4.76C13.5 4.76 13.25 4.86 13.05 5.05L6.46 11.64C6.07 12.03 6.07 12.66 6.46 13.05L13.05 19.64C13.44 20.03 14.07 20.03 14.46 19.64C14.85 19.25 14.85 18.62 14.46 18.23L9.41 13.18H20C20.55 13.18 21 12.73 21 12.18C21 11.63 20.55 11.18 20 11.18V11.5Z"
      fill={color}
      stroke={color}
      strokeWidth="2"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
  </Svg>
))

EmailInputScreen.displayName = 'EmailInputScreen'

export default EmailInputScreen
