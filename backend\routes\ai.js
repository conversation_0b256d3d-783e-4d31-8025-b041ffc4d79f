/**
 * PayVendy AI Brain API Routes
 * 
 * Handles communication between the backend and AI Brain system
 * for user analysis, reward processing, and fraud detection.
 */

const express = require('express');
const { body, param, query, validationResult } = require('express-validator');
const { authenticateToken, requireRole } = require('../middleware/auth');
const { rateLimiter } = require('../middleware/rateLimiter');
const logger = require('../utils/logger');
const pool = require('../config/database');

const router = express.Router();

/**
 * Trigger AI analysis for a specific user
 * POST /api/ai/analyze/:userId
 */
router.post('/analyze/:userId',
    authenticateToken,
    requireRole(['admin', 'ai_system']),
    param('userId').isUUID().withMessage('Invalid user ID'),
    rateLimiter({ windowMs: 60000, max: 10 }), // 10 requests per minute
    async (req, res) => {
        try {
            const errors = validationResult(req);
            if (!errors.isEmpty()) {
                return res.status(400).json({
                    success: false,
                    message: 'Validation failed',
                    errors: errors.array()
                });
            }

            const { userId } = req.params;
            const { priority = 'normal' } = req.body;

            // Check if user exists
            const userResult = await pool.query(
                'SELECT id, status FROM users WHERE id = $1',
                [userId]
            );

            if (userResult.rows.length === 0) {
                return res.status(404).json({
                    success: false,
                    message: 'User not found'
                });
            }

            const user = userResult.rows[0];
            if (user.status !== 'active') {
                return res.status(400).json({
                    success: false,
                    message: 'User is not active'
                });
            }

            // Create real-time event for AI processing
            await pool.query(`
                INSERT INTO realtime_events (user_id, event_type, event_data, created_at)
                VALUES ($1, $2, $3, NOW())
            `, [
                userId,
                'manual_analysis_request',
                JSON.stringify({
                    priority,
                    requested_by: req.user.id,
                    timestamp: new Date().toISOString()
                })
            ]);

            logger.info(`AI analysis requested for user ${userId}`, {
                userId,
                requestedBy: req.user.id,
                priority
            });

            res.json({
                success: true,
                message: 'AI analysis request queued successfully',
                data: {
                    userId,
                    priority,
                    status: 'queued'
                }
            });

        } catch (error) {
            logger.error('Error requesting AI analysis:', error);
            res.status(500).json({
                success: false,
                message: 'Internal server error'
            });
        }
    }
);

/**
 * Get AI analysis results for a user
 * GET /api/ai/analysis/:userId
 */
router.get('/analysis/:userId',
    authenticateToken,
    requireRole(['admin', 'ai_system']),
    param('userId').isUUID().withMessage('Invalid user ID'),
    query('limit').optional().isInt({ min: 1, max: 100 }).withMessage('Limit must be between 1 and 100'),
    async (req, res) => {
        try {
            const errors = validationResult(req);
            if (!errors.isEmpty()) {
                return res.status(400).json({
                    success: false,
                    message: 'Validation failed',
                    errors: errors.array()
                });
            }

            const { userId } = req.params;
            const limit = parseInt(req.query.limit) || 10;

            // Get latest behavior analytics
            const analyticsResult = await pool.query(`
                SELECT 
                    analysis_type,
                    period_start,
                    period_end,
                    metrics,
                    insights,
                    recommendations,
                    confidence_level,
                    created_at
                FROM behavior_analytics 
                WHERE user_id = $1 
                ORDER BY created_at DESC 
                LIMIT $2
            `, [userId, limit]);

            // Get current user segment
            const segmentResult = await pool.query(`
                SELECT 
                    segment_name,
                    segment_score,
                    attributes,
                    last_updated
                FROM user_segments 
                WHERE user_id = $1 
                ORDER BY last_updated DESC
            `, [userId]);

            // Get recent AI logs
            const logsResult = await pool.query(`
                SELECT 
                    action_type,
                    description,
                    output_data,
                    confidence_score,
                    created_at
                FROM ai_logs 
                WHERE user_id = $1 
                AND action_type IN ('user_behavior_analysis', 'fraud_detection')
                ORDER BY created_at DESC 
                LIMIT $2
            `, [userId, limit]);

            res.json({
                success: true,
                data: {
                    userId,
                    analytics: analyticsResult.rows,
                    segments: segmentResult.rows,
                    logs: logsResult.rows,
                    summary: {
                        total_analyses: analyticsResult.rows.length,
                        latest_analysis: analyticsResult.rows[0]?.created_at || null,
                        current_segment: segmentResult.rows[0]?.segment_name || 'unknown'
                    }
                }
            });

        } catch (error) {
            logger.error('Error fetching AI analysis:', error);
            res.status(500).json({
                success: false,
                message: 'Internal server error'
            });
        }
    }
);

/**
 * Get pending AI rewards for a user
 * GET /api/ai/rewards/:userId
 */
router.get('/rewards/:userId',
    authenticateToken,
    requireRole(['admin', 'ai_system']),
    param('userId').isUUID().withMessage('Invalid user ID'),
    async (req, res) => {
        try {
            const errors = validationResult(req);
            if (!errors.isEmpty()) {
                return res.status(400).json({
                    success: false,
                    message: 'Validation failed',
                    errors: errors.array()
                });
            }

            const { userId } = req.params;

            const rewardsResult = await pool.query(`
                SELECT 
                    id,
                    reward_type,
                    amount,
                    reason,
                    status,
                    expires_at,
                    applied_at,
                    metadata,
                    created_at
                FROM reward_queue 
                WHERE user_id = $1 
                ORDER BY created_at DESC
            `, [userId]);

            const summary = {
                total_rewards: rewardsResult.rows.length,
                pending_rewards: rewardsResult.rows.filter(r => r.status === 'pending').length,
                applied_rewards: rewardsResult.rows.filter(r => r.status === 'applied').length,
                total_pending_amount: rewardsResult.rows
                    .filter(r => r.status === 'pending')
                    .reduce((sum, r) => sum + parseFloat(r.amount), 0),
                total_applied_amount: rewardsResult.rows
                    .filter(r => r.status === 'applied')
                    .reduce((sum, r) => sum + parseFloat(r.amount), 0)
            };

            res.json({
                success: true,
                data: {
                    userId,
                    rewards: rewardsResult.rows,
                    summary
                }
            });

        } catch (error) {
            logger.error('Error fetching AI rewards:', error);
            res.status(500).json({
                success: false,
                message: 'Internal server error'
            });
        }
    }
);

/**
 * Manually apply a pending reward
 * POST /api/ai/rewards/:rewardId/apply
 */
router.post('/rewards/:rewardId/apply',
    authenticateToken,
    requireRole(['admin']),
    param('rewardId').isUUID().withMessage('Invalid reward ID'),
    async (req, res) => {
        try {
            const errors = validationResult(req);
            if (!errors.isEmpty()) {
                return res.status(400).json({
                    success: false,
                    message: 'Validation failed',
                    errors: errors.array()
                });
            }

            const { rewardId } = req.params;

            // Get reward details
            const rewardResult = await pool.query(`
                SELECT user_id, amount, reason, reward_type, status
                FROM reward_queue 
                WHERE id = $1
            `, [rewardId]);

            if (rewardResult.rows.length === 0) {
                return res.status(404).json({
                    success: false,
                    message: 'Reward not found'
                });
            }

            const reward = rewardResult.rows[0];

            if (reward.status !== 'pending') {
                return res.status(400).json({
                    success: false,
                    message: `Reward is already ${reward.status}`
                });
            }

            // Apply the reward using the database function
            const applyResult = await pool.query(`
                SELECT apply_reward($1, $2, $3, $4) as success
            `, [reward.user_id, reward.amount, reward.reason, rewardId]);

            const success = applyResult.rows[0].success;

            if (success) {
                logger.info(`Reward ${rewardId} applied manually`, {
                    rewardId,
                    userId: reward.user_id,
                    amount: reward.amount,
                    appliedBy: req.user.id
                });

                res.json({
                    success: true,
                    message: 'Reward applied successfully',
                    data: {
                        rewardId,
                        userId: reward.user_id,
                        amount: reward.amount,
                        type: reward.reward_type
                    }
                });
            } else {
                res.status(400).json({
                    success: false,
                    message: 'Failed to apply reward'
                });
            }

        } catch (error) {
            logger.error('Error applying reward:', error);
            res.status(500).json({
                success: false,
                message: 'Internal server error'
            });
        }
    }
);

/**
 * Get AI system statistics
 * GET /api/ai/stats
 */
router.get('/stats',
    authenticateToken,
    requireRole(['admin']),
    async (req, res) => {
        try {
            // Get analysis statistics
            const analysisStats = await pool.query(`
                SELECT 
                    COUNT(*) as total_analyses,
                    COUNT(*) FILTER (WHERE created_at >= NOW() - INTERVAL '24 hours') as analyses_last_24h,
                    COUNT(*) FILTER (WHERE created_at >= NOW() - INTERVAL '7 days') as analyses_last_7d,
                    AVG(confidence_level) as avg_confidence
                FROM behavior_analytics
            `);

            // Get reward statistics
            const rewardStats = await pool.query(`
                SELECT 
                    COUNT(*) as total_rewards,
                    COUNT(*) FILTER (WHERE status = 'pending') as pending_rewards,
                    COUNT(*) FILTER (WHERE status = 'applied') as applied_rewards,
                    COUNT(*) FILTER (WHERE created_at >= NOW() - INTERVAL '24 hours') as rewards_last_24h,
                    COALESCE(SUM(amount) FILTER (WHERE status = 'applied'), 0) as total_applied_amount,
                    COALESCE(SUM(amount) FILTER (WHERE status = 'pending'), 0) as total_pending_amount
                FROM reward_queue
            `);

            // Get fraud detection statistics
            const fraudStats = await pool.query(`
                SELECT 
                    COUNT(*) as total_fraud_checks,
                    COUNT(*) FILTER (WHERE action_type = 'fraud_alert') as fraud_alerts,
                    COUNT(*) FILTER (WHERE created_at >= NOW() - INTERVAL '24 hours' AND action_type = 'fraud_alert') as alerts_last_24h
                FROM ai_logs
                WHERE action_type IN ('fraud_detection', 'fraud_alert')
            `);

            // Get user segment distribution
            const segmentStats = await pool.query(`
                SELECT 
                    segment_name,
                    COUNT(*) as user_count,
                    AVG(segment_score) as avg_score
                FROM user_segments
                GROUP BY segment_name
                ORDER BY user_count DESC
            `);

            res.json({
                success: true,
                data: {
                    analysis: analysisStats.rows[0],
                    rewards: rewardStats.rows[0],
                    fraud: fraudStats.rows[0],
                    segments: segmentStats.rows,
                    generated_at: new Date().toISOString()
                }
            });

        } catch (error) {
            logger.error('Error fetching AI stats:', error);
            res.status(500).json({
                success: false,
                message: 'Internal server error'
            });
        }
    }
);

/**
 * Webhook endpoint for AI Brain to report events
 * POST /api/ai/webhook/event
 */
router.post('/webhook/event',
    // Note: This should use API key authentication in production
    body('event_type').notEmpty().withMessage('Event type is required'),
    body('user_id').optional().isUUID().withMessage('Invalid user ID'),
    body('data').isObject().withMessage('Event data must be an object'),
    rateLimiter({ windowMs: 60000, max: 100 }), // 100 requests per minute
    async (req, res) => {
        try {
            const errors = validationResult(req);
            if (!errors.isEmpty()) {
                return res.status(400).json({
                    success: false,
                    message: 'Validation failed',
                    errors: errors.array()
                });
            }

            const { event_type, user_id, data, timestamp } = req.body;

            // Log the AI event
            await pool.query(`
                INSERT INTO ai_logs (user_id, action_type, description, input_data, created_at)
                VALUES ($1, $2, $3, $4, $5)
            `, [
                user_id || null,
                `ai_webhook_${event_type}`,
                `AI Brain webhook event: ${event_type}`,
                JSON.stringify(data),
                timestamp ? new Date(timestamp) : new Date()
            ]);

            logger.info(`AI webhook event received: ${event_type}`, {
                event_type,
                user_id,
                data
            });

            res.json({
                success: true,
                message: 'Event received successfully'
            });

        } catch (error) {
            logger.error('Error processing AI webhook:', error);
            res.status(500).json({
                success: false,
                message: 'Internal server error'
            });
        }
    }
);

module.exports = router;