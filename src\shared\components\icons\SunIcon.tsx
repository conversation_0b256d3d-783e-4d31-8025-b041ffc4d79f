import React from 'react';
import Svg, { Circle, Path } from 'react-native-svg';

interface SunIconProps {
  size?: number;
  color?: string;
}

const SunIcon: React.FC<SunIconProps> = ({
  size = 24,
  color = '#FFA500',
}) => {
  return (
    <Svg width={size} height={size} viewBox="0 0 24 24" fill="none">
      {/* Sun rays */}
      <Path
        d="M12 1v2M12 21v2M4.22 4.22l1.42 1.42M18.36 18.36l1.42 1.42M1 12h2M21 12h2M4.22 19.78l1.42-1.42M18.36 5.64l1.42-1.42"
        stroke={color}
        strokeWidth="2"
        strokeLinecap="round"
      />
      {/* Sun center */}
      <Circle
        cx="12"
        cy="12"
        r="5"
        fill={color}
        stroke={color}
        strokeWidth="2"
      />
    </Svg>
  );
};

export default SunIcon;