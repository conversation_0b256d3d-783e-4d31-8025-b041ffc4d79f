import React, { useEffect, useRef } from 'react';
import { NavigationContainer, NavigationContainerRef } from '@react-navigation/native';
import { createNativeStackNavigator } from '@react-navigation/native-stack';
import { StatusBar } from 'react-native';

// Import Firebase initialization first - this will initialize Firebase synchronously
import './src/infrastructure/config/initFirebase';

import { ThemeProvider } from "./src/shared/components/layout/ThemeContext";
import ErrorBoundary from './src/shared/components/feedback/ErrorBoundary';
import BottomTabNavigator from './src/navigation/tabs/BottomTabNavigator';

// Authentication screens
import ProfileScreen from './src/features/profile/screens/ProfileScreen';
import StartupScreen from './src/features/authentication/screens/StartupScreen';
import SplashScreen from './src/features/authentication/screens/SplashScreen';
import PhoneInputScreen from './src/features/authentication/screens/PhoneInputScreen';
import EmailInputScreen from './src/features/authentication/screens/EmailInputScreen';
import EmailVerificationScreen from './src/features/authentication/screens/EmailVerificationScreen';
import VerificationScreen from './src/features/authentication/screens/VerificationScreen';
import AvatarSelectionScreen from './src/features/authentication/screens/AvatarSelectionScreen';
import NameSetupScreen from './src/features/authentication/screens/NameSetupScreen';
import PinSetupScreen from './src/features/authentication/screens/PinSetupScreen';
import PinVerificationScreen from './src/features/authentication/screens/PinVerificationScreen';
import BiometricSetupScreen from './src/features/authentication/screens/BiometricSetupScreen';
import SetupCompleteScreen from './src/features/authentication/screens/SetupCompleteScreen';
import SetupLoadingScreen from './src/features/authentication/screens/SetupLoadingScreen';

// Feature screens
import AirtimeScreen from './src/features/airtime/screens/AirtimeScreen';
import SettingsScreen from './src/features/settings/screens/SettingsScreen';
import SecurityScreen from './src/features/settings/screens/SecurityScreen';
import AppearanceScreen from './src/features/settings/screens/AppearanceScreen';
import ReferEarnScreen from './src/features/rewards/screens/ReferEarnScreen';

// Services and handlers
import { ProfileProvider } from './src/features/profile/components/ProfileContext';
import { userDeletionHandler } from './src/features/authentication/services/userDeletionHandler';
import { globalErrorHandler } from './src/infrastructure/monitoring/globalErrorHandler';
import securityInit from './src/infrastructure/security/securityInitService';
import { navigationHandler } from './src/navigation/handlers/navigationHandler';

import type { RootStackParamList } from './src/navigation/navigation';

const Stack = createNativeStackNavigator<RootStackParamList>();

const App = (): React.ReactElement => {
  const navigationRef = useRef<NavigationContainerRef<RootStackParamList>>(null);

  useEffect(() => {
    // Initialize security services first
    const initializeSecurity = async () => {
      try {
        const result = await securityInit.initialize();
        
        if (result.success) {
          console.log('🔒 Security services initialized successfully');
        } else {
          console.warn('⚠️ Security services initialization had issues:', result.errors);
        }
      } catch (error) {
        console.error('❌ Failed to initialize security services:', error);
      }
    };

    // Set navigation reference for user deletion handler and navigation handler
    if (navigationRef.current) {
      userDeletionHandler.setNavigationRef(navigationRef.current);
      navigationHandler.setNavigation(navigationRef.current);
    }
    
    // Initialize global error handler
    globalErrorHandler.initialize();
    
    // Initialize security services
    initializeSecurity();
  }, []);

  return (
    <ThemeProvider>
      <ProfileProvider>
        <ErrorBoundary>
          <StatusBar backgroundColor="#111" barStyle="light-content" />
          <NavigationContainer ref={navigationRef}>
            <Stack.Navigator initialRouteName="Splash">
              <Stack.Screen name="Splash" component={SplashScreen} options={{ headerShown: false }} />
              <Stack.Screen name="Startup" component={StartupScreen} options={{ headerShown: false }} />
              <Stack.Screen name="PhoneInput" component={PhoneInputScreen} options={{ headerShown: false }} />
              <Stack.Screen name="EmailInput" component={EmailInputScreen} options={{ headerShown: false }} />
              <Stack.Screen name="EmailVerification" component={EmailVerificationScreen} options={{ headerShown: false }} />
              <Stack.Screen name="Verification" component={VerificationScreen} options={{ headerShown: false }} />
              <Stack.Screen name="AvatarSelection" component={AvatarSelectionScreen} options={{ headerShown: false }} />
              <Stack.Screen name="NameSetup" component={NameSetupScreen} options={{ headerShown: false }} />
              <Stack.Screen name="PinSetup" component={PinSetupScreen} options={{ headerShown: false }} />
              <Stack.Screen name="PinVerification" component={PinVerificationScreen} options={{ headerShown: false }} />
              <Stack.Screen name="BiometricSetup" component={BiometricSetupScreen} options={{ headerShown: false }} />
              <Stack.Screen name="SetupComplete" component={SetupCompleteScreen} options={{ headerShown: false }} />
              <Stack.Screen name="SetupLoading" component={SetupLoadingScreen} options={{ headerShown: false }} />
              <Stack.Screen name="Airtime" component={AirtimeScreen} options={{ headerShown: false }} />
              <Stack.Screen name="MainTabs" component={BottomTabNavigator} options={{ headerShown: false }} />
              <Stack.Screen name="Profile" component={ProfileScreen} />
              <Stack.Screen name="Settings" component={SettingsScreen} options={{ headerShown: false }} />
              <Stack.Screen name="Security" component={SecurityScreen} options={{ headerShown: false }} />
              <Stack.Screen name="Appearance" component={AppearanceScreen} options={{ headerShown: false }} />
              <Stack.Screen name="ReferEarn" component={ReferEarnScreen} options={{ headerShown: false }} />
            </Stack.Navigator>
          </NavigationContainer>
        </ErrorBoundary>
      </ProfileProvider>
    </ThemeProvider>
  );
};

export default App;
