import { NavigationProp } from '@react-navigation/native';
import { RootStackParamList } from '../../../navigation/navigation';
import { logger } from '../../../infrastructure/monitoring/productionLogger';
import { navigationHandler } from '../../../navigation/handlers/navigationHandler';

// Type for setup screens that accept userData parameter
type SetupScreens = 'NameSetup' | 'PinSetup' | 'PinVerification' | 'BiometricSetup' | 'SetupComplete' | 'AvatarSelection';

export interface UserData {
  id: string;
  email?: string;
  phoneNumber?: string;
  firstName?: string;
  lastName?: string;
  authMethod: 'email' | 'phone' | 'google';
  isNewUser?: boolean;
  isEmailVerified?: boolean;
  isPhoneVerified?: boolean;
  pin?: string;
  avatar?: string;
  pinVerifiedInSession?: boolean; // Track if PIN was verified in this session
  [key: string]: any;
}

/**
 * Determines the next setup step for a user after authentication
 * @param userData - User data from authentication
 * @returns The next screen to navigate to
 */
export function getNextSetupStep(userData: UserData): SetupScreens {
  logger.debug('🔍 [SETUP-NAV] Checking setup step for user:', {
    firstName: userData.firstName,
    authMethod: userData.authMethod,
    pin: userData.pin ? 'SET' : 'NOT_SET',
    isNewUser: userData.isNewUser,
    pinVerifiedInSession: userData.pinVerifiedInSession
  });

  // If user has no name/profile info, start with name setup
  // BUT for Google users, check if name is typically provided by Google
  if (!userData.firstName || userData.firstName.trim() === '') {
    if (userData.authMethod === 'google') {
      logger.warn('⚠️ [SETUP-NAV] Google user missing firstName, this should not happen, but proceeding based on PIN status');
      // For Google users, we should always have a name, but if not, check PIN
      if (!userData.pin || userData.pin === '0000' || userData.pin === '') {
        return 'PinSetup';
      } else {
        return 'PinVerification';
      }
    } else {
      logger.debug('🔍 [SETUP-NAV] Non-Google user missing firstName, enforcing NameSetup');
      return 'NameSetup';
    }
  }
  
  // Check PIN status
  const hasPin = userData.pin && userData.pin !== '0000' && userData.pin !== '';
  
  if (!hasPin) {
    // No PIN set - go to PIN setup
    logger.debug('🔍 [SETUP-NAV] No PIN set, going to PinSetup');
    return 'PinSetup';
  } else {
    // PIN exists - go to PIN verification
    logger.debug('🔍 [SETUP-NAV] PIN exists, going to PinVerification');
    return 'PinVerification';
  }
}

/**
 * Checks if user needs to go through setup flow
 * @param userData - User data from authentication
 * @returns true if setup is needed, false if user can go directly to Home
 */
export function needsSetupFlow(userData: UserData): boolean {
  logger.debug('🔍 [SETUP-NAV] Checking if setup flow needed:', {
    isNewUser: userData.isNewUser,
    authMethod: userData.authMethod,
    firstName: userData.firstName ? 'SET' : 'NOT_SET',
    pin: userData.pin ? 'SET' : 'NOT_SET',
    pinVerifiedInSession: userData.pinVerifiedInSession,
    isEmailVerified: userData.isEmailVerified,
    isPhoneVerified: userData.isPhoneVerified
  });

  // Check if user has completed basic setup
  const hasName = userData.firstName && userData.firstName.trim() !== '';
  const hasPin = userData.pin && userData.pin !== '0000' && userData.pin !== '';
  const isVerified = userData.isEmailVerified || userData.isPhoneVerified;
  
  // If PIN was already verified in this session, skip PIN verification
  if (userData.pinVerifiedInSession) {
    logger.info('✅ [SETUP-NAV] PIN already verified in this session, checking other requirements');
    
    // For Google users, they should always have a name, so check other requirements
    if (userData.authMethod === 'google') {
      const needsSetup = !hasPin; // Google users just need PIN setup if no PIN
      logger.debug(`🔍 [SETUP-NAV] Google user needs setup: ${needsSetup} (hasPin: ${hasPin})`);
      return needsSetup;
    }
    
    // For email/phone users, check all requirements
    const needsSetup = !hasName || !hasPin || !isVerified;
    logger.debug(`🔍 [SETUP-NAV] ${userData.authMethod} user needs setup: ${needsSetup} (hasName: ${hasName}, hasPin: ${hasPin}, isVerified: ${isVerified})`);
    return needsSetup;
  }
  
  // PIN not verified in this session - always require setup flow for PIN verification
  logger.debug('🔍 [SETUP-NAV] PIN not verified in session, requiring setup flow');
  return true;
}

/**
 * Navigates user to appropriate screen after authentication
 * @param navigation - Navigation object
 * @param userData - User data from authentication
 * @param isExistingAccount - Whether this is an existing account (setup complete)
 */
export const navigateAfterAuth = async (navigation: any, userData: any, isExistingAccount: boolean = false) => {
  logger.debug('🔄 [SETUP-NAV] Processing navigation with user data:', userData)
  logger.debug('🔄 [SETUP-NAV] Is existing account:', isExistingAccount)

  // For existing users, we need to check the actual setup status from the backend
  // but only if we don't have sufficient information from the auth response
  if (!userData.isNewUser) {
    logger.debug('🔍 [SETUP-NAV] Existing user detected - checking if backend call is needed')
    
    // Check if we already have sufficient PIN information from auth response
    const hasPinFromAuth = userData.pin && userData.pin !== '0000' && userData.pin !== '' && userData.pin !== 'NOT_SET';
    
    if (hasPinFromAuth) {
      logger.info('🚀 [SETUP-NAV] OPTIMIZATION: PIN info available from auth, skipping backend call')
      logger.debug('🔐 [SETUP-NAV] User has PIN from auth - proceeding to verification')
      navigationHandler.resetToScreen('PinVerification', { user: userData })
      return;
    }
    
    logger.debug('🔍 [SETUP-NAV] PIN info not available from auth - checking backend setup status')
    
    try {
      // Import setupService dynamically to avoid circular dependencies
      const { setupService } = await import('../services/setupService')
      const setupResponse = await setupService.getSetupStatus()
      
      logger.debug('🔍 [SETUP-NAV] Backend setup status:', setupResponse)
      
      // Update user data with accurate backend information
      // Include setup status fields in the user data for the PIN verification screen
      const updatedUserData = {
        ...userData,
        ...setupResponse.user,
        // Preserve auth method and other auth-specific data
        authMethod: userData.authMethod,
        isNewUser: userData.isNewUser,
        // Include setup status fields for PIN verification screen
        hasPinSetup: setupResponse.setupStatus?.hasPinSetup,
        hasBiometricSetup: setupResponse.setupStatus?.hasBiometricSetup,
        hasProfileSetup: setupResponse.setupStatus?.hasProfileSetup,
        setupComplete: setupResponse.setupStatus?.setupComplete,
      }
      
      logger.debug('🔍 [SETUP-NAV] Updated user data with backend info:', {
        ...updatedUserData,
        hasPinSetup: updatedUserData.hasPinSetup,
        hasBiometricSetup: updatedUserData.hasBiometricSetup,
      })
      
      // Check if user has PIN setup based on backend data
      if (setupResponse.setupStatus?.hasPinSetup) {
        logger.info('🔐 [SETUP-NAV] SECURITY: Backend confirms user has PIN - requiring verification')
        navigationHandler.resetToScreen('PinVerification', { user: updatedUserData })
        return;
      } else {
        logger.debug('🔍 [SETUP-NAV] Backend confirms no PIN setup - proceeding to PIN setup')
        navigationHandler.resetToScreen('PinSetup', { user: updatedUserData })
        return;
      }
    } catch (error) {
      logger.error('❌ [SETUP-NAV] Failed to get backend setup status:', { error })
      logger.debug('🔄 [SETUP-NAV] Falling back to auth data for navigation decision')
      // Fallback to original logic if backend call fails
    }
  }

  // Original logic for new users or when backend call fails
  const hasName = userData.firstName && userData.firstName.trim() !== '' && userData.firstName !== 'NOT_SET';
  const hasPin = userData.pin && userData.pin !== '0000' && userData.pin !== '' && userData.pin !== 'NOT_SET';
  
  logger.debug('🔍 [SETUP-NAV] Fallback setup status:', {
    hasName,
    hasPin,
    firstName: userData.firstName,
    pin: userData.pin ? 'SET' : 'NOT_SET',
    isNewUser: userData.isNewUser,
    authMethod: userData.authMethod
  });

  if (!hasName) {
    // User needs to set up their name first
    logger.debug('➡️ [SETUP-NAV] Navigating to name setup')
    navigationHandler.navigateToNameSetup({ user: userData })
  } else if (!hasPin) {
    // User has name but no PIN - go to PIN setup to create PIN
    logger.debug('➡️ [SETUP-NAV] Navigating to PIN setup (create PIN)')
    navigationHandler.resetToScreen('PinSetup', { user: userData })
  } else {
    // User has both name and PIN - go to PIN verification
    logger.debug('➡️ [SETUP-NAV] Navigating to PIN verification (verify existing PIN)')
    navigationHandler.resetToScreen('PinVerification', { user: userData })
  }
}

/**
 * Creates standardized user data object for setup flow
 * @param authResponse - Response from authentication API
 * @param authMethod - Method used for authentication
 * @returns Standardized user data object
 */
export function createUserDataFromAuth(
  authResponse: any,
  authMethod: 'email' | 'phone' | 'google'
): UserData {
  const user = authResponse.data?.user || authResponse.user || {};
  
  // Handle Google-specific data structure
  if (authMethod === 'google') {
    const googleUser = authResponse.googleUser || authResponse.data?.googleUser || {};
    const backendUser = authResponse.user || authResponse.data?.user || {};
    
    logger.debug('🔍 [SETUP-NAV] Creating Google user data:', {
      googleUser: googleUser.name || googleUser.displayName,
      backendUser: backendUser.first_name || backendUser.firstName,
      authResponse: Object.keys(authResponse)
    });
    
    return {
      id: backendUser.id || authResponse.data?.userId || googleUser.id,
      email: googleUser.email || backendUser.email,
      phoneNumber: backendUser.phone_number || backendUser.phoneNumber,
      firstName: googleUser.givenName || googleUser.name?.split(' ')[0] || backendUser.first_name || backendUser.firstName,
      lastName: googleUser.familyName || googleUser.name?.split(' ').slice(1).join(' ') || backendUser.last_name || backendUser.lastName,
      authMethod,
      isNewUser: authResponse.data?.isNewUser || authResponse.isNewUser || false,
      isEmailVerified: true, // Google users are always email verified
      isPhoneVerified: backendUser.is_phone_verified || backendUser.isPhoneVerified || false,
      pin: backendUser.pin,
      avatar: googleUser.photo || backendUser.avatar || backendUser.picture,
      balance: backendUser.balance || 0,
      createdAt: backendUser.created_at || backendUser.createdAt,
      // Include any other fields from the response
      ...backendUser,
    };
  }
  
  // Handle email/phone data structure
  logger.debug('🔍 [SETUP-NAV] Creating email/phone user data:', {
    userId: user.id || authResponse.data?.userId,
    email: user.email,
    firstName: user.first_name || user.firstName,
    isNewUser: authResponse.data?.isNewUser || authResponse.isNewUser || false,
    hasAvatar: !!(user.avatar || user.picture)
  });
  
  return {
    id: user.id || authResponse.data?.userId,
    email: user.email,
    phoneNumber: user.phone_number || user.phoneNumber,
    firstName: user.first_name || user.firstName,
    lastName: user.last_name || user.lastName,
    authMethod,
    isNewUser: authResponse.data?.isNewUser || authResponse.isNewUser || false,
    isEmailVerified: user.is_email_verified || user.isEmailVerified || (authMethod === 'email'),
    isPhoneVerified: user.is_phone_verified || user.isPhoneVerified || false,
    pin: user.pin,
    avatar: user.avatar || user.picture,
    balance: user.balance || 0,
    createdAt: user.created_at || user.createdAt,
    // Include any other fields from the response
    ...user,
  };
}
