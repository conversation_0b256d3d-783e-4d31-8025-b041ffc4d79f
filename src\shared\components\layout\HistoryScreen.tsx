import React, { useState } from 'react';
import {
  View,
  Text,
  StyleSheet,
  SafeAreaView,
  ScrollView,
  TouchableOpacity,
  StatusBar,
} from 'react-native';
import { useTheme } from './ThemeContext';
import AirtimeIcon from '../icons/AirtimeIcon';
import DataIcon from '../icons/DataIcon';
import BillsIcon from '../icons/BillsIcon';
import TransferIcon from '../icons/TransferIcon';
import DefaultIcon from '../icons/DefaultIcon';

const HistoryScreen: React.FC = () => {
  const { theme, isDark } = useTheme();
  const [selectedFilter, setSelectedFilter] = useState('All');

  const filters = ['All', 'Airtime', 'Data', 'Bills', 'Transfer'];

  const transactions = [
    {
      id: 1,
      type: 'Airtime',
      network: 'MTN',
      amount: 1500,
      phone: '08012345678',
      status: 'Successful',
      date: '2024-01-15',
      time: '10:30 AM',
      reference: 'VTU123456789'
    },
    {
      id: 2,
      type: 'Data',
      network: 'Airtel',
      amount: 2000,
      phone: '***********',
      status: 'Successful',
      date: '2024-01-15',
      time: '09:15 AM',
      reference: 'VTU987654321'
    },
    {
      id: 3,
      type: 'Bills',
      service: 'EKEDC',
      amount: 5000,
      meter: '***********',
      status: 'Pending',
      date: '2024-01-14',
      time: '08:45 PM',
      reference: 'BILL123456789'
    },
    {
      id: 4,
      type: 'Transfer',
      bank: 'GTBank',
      amount: 10000,
      account: '**********',
      status: 'Failed',
      date: '2024-01-14',
      time: '02:20 PM',
      reference: 'TRF123456789'
    },
  ];

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'Successful':
        return '#22C55E';
      case 'Pending':
        return '#F59E0B';
      case 'Failed':
        return '#EF4444';
      default:
        return theme.colors.muted;
    }
  };

  const getTypeIcon = (type: string, size = 20) => {
    switch (type) {
      case 'Airtime':
        return <AirtimeIcon size={size} color={theme.colors.primary} />;
      case 'Data':
        return <DataIcon size={size} color={'#3B82F6'} />;
      case 'Bills':
        return <BillsIcon size={size} color={theme.colors.warning} />;
      case 'Transfer':
        return <TransferIcon size={size} color={'#6366F1'} />;
      default:
        return <DefaultIcon size={size} color={theme.colors.muted} />;
    }
  };

  const filteredTransactions = selectedFilter === 'All' 
    ? transactions 
    : transactions.filter(t => t.type === selectedFilter);

  const styles = StyleSheet.create({
    container: {
      flex: 1,
      backgroundColor: theme.colors.background,
    },
    header: {
      paddingHorizontal: 20,
      paddingVertical: 24,
      backgroundColor: theme.colors.surface,
    },
    title: {
      fontSize: 28,
      fontWeight: '700',
      color: theme.colors.text,
      marginBottom: 8,
    },
    subtitle: {
      fontSize: 16,
      color: theme.colors.muted,
    },
    filterContainer: {
      paddingHorizontal: 20,
      paddingVertical: 16,
    },
    filterScrollView: {
      flexDirection: 'row',
    },
    filterButton: {
      paddingHorizontal: 20,
      paddingVertical: 10,
      borderRadius: 20,
      marginRight: 12,
      backgroundColor: theme.colors.card,
      borderWidth: 1,
      borderColor: theme.colors.border,
    },
    filterButtonActive: {
      backgroundColor: theme.colors.primary,
      borderColor: theme.colors.primary,
    },
    filterText: {
      fontSize: 14,
      fontWeight: '500',
      color: theme.colors.text,
    },
    filterTextActive: {
      color: '#FFFFFF',
    },
    transactionItem: {
      backgroundColor: theme.colors.card,
      marginHorizontal: 20,
      marginBottom: 12,
      borderRadius: 16,
      padding: 16,
      borderWidth: 1,
      borderColor: theme.colors.border,
      shadowColor: theme.colors.text,
      shadowOffset: { width: 0, height: 2 },
      shadowOpacity: 0.1,
      shadowRadius: 8,
      elevation: 3,
    },
    transactionHeader: {
      flexDirection: 'row',
      alignItems: 'center',
      marginBottom: 12,
    },
    transactionIcon: {
      width: 32,
      height: 32,
      borderRadius: 16,
      backgroundColor: theme.colors.card,
      justifyContent: 'center',
      alignItems: 'center',
      marginRight: 10,
    },
    transactionIconText: {
      fontSize: 16,
    },
    transactionDetails: {
      flex: 1,
    },
    transactionType: {
      fontSize: 14,
      fontWeight: '600',
      color: theme.colors.text,
      marginBottom: 2,
    },
    transactionInfo: {
      fontSize: 12,
      color: theme.colors.muted,
    },
    transactionAmount: {
      fontSize: 14,
      fontWeight: '700',
      color: theme.colors.text,
    },
    transactionFooter: {
      flexDirection: 'row',
      justifyContent: 'space-between',
      alignItems: 'center',
      paddingTop: 8,
      borderTopWidth: 1,
      borderTopColor: theme.colors.border,
    },
    transactionDate: {
      fontSize: 12,
      color: theme.colors.muted,
    },
    transactionStatus: {
      fontSize: 12,
      fontWeight: '600',
    },
    emptyState: {
      flex: 1,
      justifyContent: 'center',
      alignItems: 'center',
      paddingVertical: 60,
    },
    emptyStateText: {
      fontSize: 18,
      color: theme.colors.muted,
      textAlign: 'center',
      marginTop: 16,
    },
    emptyStateIcon: {
      fontSize: 48,
      marginBottom: 16,
    },
  });

  return (
    <SafeAreaView style={styles.container}>
      <StatusBar barStyle={isDark ? "light-content" : "dark-content"} backgroundColor={theme.colors.surface} />
      
      <View style={styles.header}>
        <Text style={styles.title}>Transaction History</Text>
        <Text style={styles.subtitle}>Track all your transactions</Text>
      </View>

      <View style={styles.filterContainer}>
        <ScrollView horizontal showsHorizontalScrollIndicator={false} style={styles.filterScrollView}>
          {filters.map((filter) => (
            <TouchableOpacity
              key={filter}
              style={[
                styles.filterButton,
                selectedFilter === filter && styles.filterButtonActive
              ]}
              onPress={() => setSelectedFilter(filter)}
            >
              <Text style={[
                styles.filterText,
                selectedFilter === filter && styles.filterTextActive
              ]}>
                {filter}
              </Text>
            </TouchableOpacity>
          ))}
        </ScrollView>
      </View>

      <ScrollView showsVerticalScrollIndicator={false}>
        {filteredTransactions.length > 0 ? (
          filteredTransactions.map((transaction) => (
            <TouchableOpacity key={transaction.id} style={styles.transactionItem}>
              <View style={styles.transactionHeader}>
                <View style={styles.transactionIcon}>
                  {getTypeIcon(transaction.type, 20)}
                </View>
                <View style={styles.transactionDetails}>
                  <Text style={styles.transactionType}>
                    {transaction.type} {transaction.network || transaction.service || transaction.bank}
                  </Text>
                  <Text style={styles.transactionInfo}>
                    {transaction.phone || transaction.meter || transaction.account}
                  </Text>
                </View>
                <Text style={styles.transactionAmount}>
                  ₦{transaction.amount.toLocaleString()}
                </Text>
              </View>
              <View style={styles.transactionFooter}>
                <Text style={styles.transactionDate}>
                  {transaction.date} • {transaction.time}
                </Text>
                <Text style={[
                  styles.transactionStatus,
                  { color: getStatusColor(transaction.status) }
                ]}>
                  {transaction.status}
                </Text>
              </View>
            </TouchableOpacity>
          ))
        ) : (
          <View style={styles.emptyState}>
            <DefaultIcon size={40} color={theme.colors.muted} />
            <Text style={styles.emptyStateText}>
              No {selectedFilter.toLowerCase()} transactions found
            </Text>
          </View>
        )}
      </ScrollView>
    </SafeAreaView>
  );
};

export default HistoryScreen;