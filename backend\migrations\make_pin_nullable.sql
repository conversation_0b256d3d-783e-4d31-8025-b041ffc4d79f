-- Migration: Make PIN field nullable for new users
-- This allows new users to be created without a PIN initially
-- They will set their PIN during the onboarding flow

-- Make the pin column nullable
ALTER TABLE users ALTER COLUMN pin DROP NOT NULL;

-- Add a comment to document this change
COMMENT ON COLUMN users.pin IS 'Encrypted PIN - NULL for new users who have not set up their PIN yet';

-- Note: This migration is safe to run multiple times