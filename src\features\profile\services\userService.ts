import { apiService } from '../../../infrastructure/api/apiService';
import { logger } from '../../../infrastructure/monitoring/productionLogger';

export interface UserProfile {
  id: string;
  firstName?: string;
  lastName?: string;
  email?: string;
  phoneNumber?: string;
  picture?: string;
  avatar?: string;
  isEmailVerified: boolean;
  isPhoneVerified: boolean;
  balance: number;
  authProvider?: string;
  createdAt?: string;
}

export interface UserProfileResponse {
  user: UserProfile;
}

export interface UserBalanceResponse {
  balance: number;
  currency: string;
}

class UserService {
  /**
   * Get current user's profile data
   */
  async getUserProfile(): Promise<UserProfileResponse> {
    try {
      logger.info('Getting user profile', { service: 'userService', action: 'getUserProfile' });
      
      const response = await apiService.get('/user/profile', {}, {
        timeout: 10000, // 10 seconds timeout
        retries: 2, // 2 retries
        cache: true, // Enable caching
        cacheTime: 5 * 60 * 1000, // Cache for 5 minutes
      });
      
      logger.info('Profile response received', { service: 'userService', action: 'getUserProfile', hasData: !!response?.data });

      if (response.data.status === 'success') {
        logger.info('Profile data retrieved successfully', { service: 'userService', action: 'getUserProfile', userId: response.data.data?.user?.id });
        return response.data.data;
      } else {
        throw new Error(response.data.message || 'Failed to get user profile');
      }
    } catch (error: any) {
      logger.error('Failed to get user profile', { service: 'userService', action: 'getUserProfile', error: error.message, statusCode: error.status });
      
      // Provide more specific error messages
      if (error.status === 401) {
        throw new Error('Authentication required. Please login again.');
      } else if (error.status === 404) {
        throw new Error('User profile not found.');
      } else if (error.name === 'AbortError' || error.message === 'Aborted') {
        throw new Error('Request timed out. Please check your connection.');
      } else if (error.message?.includes('Network')) {
        throw new Error('Network error. Please check your connection.');
      } else {
        throw new Error(error.response?.data?.message || error.message || 'Failed to get user profile');
      }
    }
  }

  /**
   * Update current user's profile data
   */
  async updateUserProfile(profileData: Partial<UserProfile>): Promise<UserProfileResponse> {
    try {
      logger.info('Updating user profile', { service: 'userService', action: 'updateUserProfile', fieldsCount: Object.keys(profileData).length });
      
      const response = await apiService.put('/user/profile', profileData, {
        timeout: 15000, // 15 seconds timeout for updates
        retries: 1, // 1 retry for updates
      });
      
      logger.info('Profile update response received', { service: 'userService', action: 'updateUserProfile', hasData: !!response?.data });

      if (response.data.status === 'success') {
        logger.info('Profile updated successfully', { service: 'userService', action: 'updateUserProfile', userId: response.data.data?.user?.id });
        return response.data.data;
      } else {
        throw new Error(response.data.message || 'Failed to update user profile');
      }
    } catch (error: any) {
      logger.error('Failed to update user profile', { service: 'userService', action: 'updateUserProfile', error: error.message, statusCode: error.status });
      
      // Provide more specific error messages
      if (error.status === 401) {
        throw new Error('Authentication required. Please login again.');
      } else if (error.status === 400) {
        throw new Error('Invalid profile data provided.');
      } else if (error.name === 'AbortError' || error.message === 'Aborted') {
        throw new Error('Request timed out. Please try again.');
      } else if (error.message?.includes('Network')) {
        throw new Error('Network error. Please check your connection.');
      } else {
        throw new Error(error.response?.data?.message || error.message || 'Failed to update user profile');
      }
    }
  }

  /**
   * Get current user's balance
   */
  async getUserBalance(): Promise<UserBalanceResponse> {
    try {
      logger.info('Getting user balance', { service: 'userService', action: 'getUserBalance' });
      
      const response = await apiService.get('/user/balance', {}, {
        timeout: 10000, // 10 seconds timeout
        retries: 2, // 2 retries
        cache: true, // Enable caching
        cacheTime: 2 * 60 * 1000, // Cache for 2 minutes (balance changes frequently)
      });
      
      logger.info('Balance response received', { service: 'userService', action: 'getUserBalance', hasData: !!response?.data });

      if (response.data.status === 'success') {
        logger.info('Balance data retrieved successfully', { service: 'userService', action: 'getUserBalance', balance: response.data.data?.balance, currency: response.data.data?.currency });
        return response.data.data;
      } else {
        throw new Error(response.data.message || 'Failed to get user balance');
      }
    } catch (error: any) {
      logger.error('Failed to get user balance', { service: 'userService', action: 'getUserBalance', error: error.message, statusCode: error.status });
      
      // Provide more specific error messages
      if (error.status === 401) {
        throw new Error('Authentication required. Please login again.');
      } else if (error.name === 'AbortError' || error.message === 'Aborted') {
        throw new Error('Request timed out. Please check your connection.');
      } else if (error.message?.includes('Network')) {
        throw new Error('Network error. Please check your connection.');
      } else {
        throw new Error(error.response?.data?.message || error.message || 'Failed to get user balance');
      }
    }
  }

  /**
   * Refresh user profile data (clears cache and fetches fresh data)
   */
  async refreshUserProfile(): Promise<UserProfileResponse> {
    try {
      logger.info('Refreshing user profile', { service: 'userService', action: 'refreshUserProfile' });
      
      // Force fresh data by disabling cache
      const response = await apiService.get('/user/profile', {}, {
        timeout: 10000,
        retries: 2,
        cache: false, // Disable cache for refresh
      });
      
      if (response.data.status === 'success') {
        logger.info('Profile refreshed successfully', { service: 'userService', action: 'refreshUserProfile', userId: response.data.data?.user?.id });
        return response.data.data;
      } else {
        throw new Error(response.data.message || 'Failed to refresh user profile');
      }
    } catch (error: any) {
      logger.error('Failed to refresh user profile', { service: 'userService', action: 'refreshUserProfile', error: error.message });
      throw error; // Re-throw to let caller handle
    }
  }
}

export const userService = new UserService();