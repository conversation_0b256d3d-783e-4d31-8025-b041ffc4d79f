/**
 * PayVendy AI Service
 * 
 * Service for communicating with the AI Brain system and triggering
 * real-time events for user analysis and reward processing.
 */

const pool = require('../config/database');
const logger = require('../utils/logger');

class AIService {
    constructor() {
        this.initialized = false;
    }

    /**
     * Initialize the AI service
     */
    async initialize() {
        try {
            // Test database connection
            await pool.query('SELECT 1');
            this.initialized = true;
            logger.info('AI Service initialized successfully');
        } catch (error) {
            logger.error('Failed to initialize AI Service:', error);
            throw error;
        }
    }

    /**
     * Trigger a real-time event for AI processing
     * @param {string} eventType - Type of event
     * @param {string} userId - User ID (optional)
     * @param {object} eventData - Event data
     * @param {string} priority - Event priority (low, normal, high, critical)
     */
    async triggerEvent(eventType, userId = null, eventData = {}, priority = 'normal') {
        try {
            if (!this.initialized) {
                logger.warn('AI Service not initialized, skipping event trigger');
                return false;
            }

            const enrichedEventData = {
                ...eventData,
                priority,
                timestamp: new Date().toISOString(),
                source: 'backend_service'
            };

            await pool.query(`
                INSERT INTO realtime_events (user_id, event_type, event_data, created_at)
                VALUES ($1, $2, $3, NOW())
            `, [userId, eventType, JSON.stringify(enrichedEventData)]);

            logger.debug(`AI event triggered: ${eventType}`, {
                eventType,
                userId,
                priority,
                dataKeys: Object.keys(eventData)
            });

            return true;

        } catch (error) {
            logger.error('Error triggering AI event:', error);
            return false;
        }
    }

    /**
     * Trigger user analysis
     * @param {string} userId - User ID
     * @param {string} reason - Reason for analysis
     * @param {string} priority - Priority level
     */
    async triggerUserAnalysis(userId, reason = 'manual_trigger', priority = 'normal') {
        return await this.triggerEvent('user_analysis_request', userId, {
            reason,
            analysis_type: 'comprehensive'
        }, priority);
    }

    /**
     * Trigger transaction analysis
     * @param {string} userId - User ID
     * @param {object} transactionData - Transaction details
     */
    async triggerTransactionAnalysis(userId, transactionData) {
        const priority = transactionData.amount > 1000 ? 'high' : 'normal';
        
        return await this.triggerEvent('transaction_completed', userId, {
            transaction_id: transactionData.id,
            amount: transactionData.amount,
            type: transactionData.type,
            status: transactionData.status,
            description: transactionData.description
        }, priority);
    }

    /**
     * Trigger login analysis
     * @param {string} userId - User ID
     * @param {object} loginData - Login details
     */
    async triggerLoginAnalysis(userId, loginData) {
        return await this.triggerEvent('user_login', userId, {
            login_method: loginData.method,
            ip_address: loginData.ip,
            user_agent: loginData.userAgent,
            location: loginData.location
        });
    }

    /**
     * Trigger suspicious activity alert
     * @param {string} userId - User ID
     * @param {object} activityData - Suspicious activity details
     */
    async triggerSuspiciousActivity(userId, activityData) {
        return await this.triggerEvent('suspicious_activity', userId, {
            activity_type: activityData.type,
            description: activityData.description,
            risk_level: activityData.riskLevel || 'medium',
            detected_by: activityData.detectedBy || 'system'
        }, 'critical');
    }

    /**
     * Trigger balance change analysis
     * @param {string} userId - User ID
     * @param {object} balanceData - Balance change details
     */
    async triggerBalanceChange(userId, balanceData) {
        const priority = Math.abs(balanceData.change) > 500 ? 'high' : 'normal';
        
        return await this.triggerEvent('balance_changed', userId, {
            previous_balance: balanceData.previousBalance,
            new_balance: balanceData.newBalance,
            change_amount: balanceData.change,
            change_type: balanceData.changeType,
            reference: balanceData.reference
        }, priority);
    }

    /**
     * Trigger profile update analysis
     * @param {string} userId - User ID
     * @param {object} updateData - Profile update details
     */
    async triggerProfileUpdate(userId, updateData) {
        return await this.triggerEvent('profile_updated', userId, {
            updated_fields: updateData.updatedFields,
            sensitive_fields_changed: updateData.sensitiveFieldsChanged || false,
            verification_status_changed: updateData.verificationStatusChanged || false
        });
    }

    /**
     * Get AI analysis results for a user
     * @param {string} userId - User ID
     * @param {number} limit - Number of results to return
     */
    async getUserAnalysis(userId, limit = 10) {
        try {
            const result = await pool.query(`
                SELECT 
                    analysis_type,
                    period_start,
                    period_end,
                    metrics,
                    insights,
                    recommendations,
                    confidence_level,
                    created_at
                FROM behavior_analytics 
                WHERE user_id = $1 
                ORDER BY created_at DESC 
                LIMIT $2
            `, [userId, limit]);

            return result.rows;

        } catch (error) {
            logger.error('Error fetching user analysis:', error);
            return [];
        }
    }

    /**
     * Get user's current segment
     * @param {string} userId - User ID
     */
    async getUserSegment(userId) {
        try {
            const result = await pool.query(`
                SELECT 
                    segment_name,
                    segment_score,
                    attributes,
                    last_updated
                FROM user_segments 
                WHERE user_id = $1 
                ORDER BY last_updated DESC 
                LIMIT 1
            `, [userId]);

            return result.rows[0] || null;

        } catch (error) {
            logger.error('Error fetching user segment:', error);
            return null;
        }
    }

    /**
     * Get pending rewards for a user
     * @param {string} userId - User ID
     */
    async getUserRewards(userId) {
        try {
            const result = await pool.query(`
                SELECT 
                    id,
                    reward_type,
                    amount,
                    reason,
                    status,
                    expires_at,
                    applied_at,
                    metadata,
                    created_at
                FROM reward_queue 
                WHERE user_id = $1 
                ORDER BY created_at DESC
            `, [userId]);

            return result.rows;

        } catch (error) {
            logger.error('Error fetching user rewards:', error);
            return [];
        }
    }

    /**
     * Get AI system statistics
     */
    async getSystemStats() {
        try {
            // Get analysis statistics
            const analysisStats = await pool.query(`
                SELECT 
                    COUNT(*) as total_analyses,
                    COUNT(*) FILTER (WHERE created_at >= NOW() - INTERVAL '24 hours') as analyses_last_24h,
                    COUNT(*) FILTER (WHERE created_at >= NOW() - INTERVAL '7 days') as analyses_last_7d,
                    AVG(confidence_level) as avg_confidence
                FROM behavior_analytics
            `);

            // Get reward statistics
            const rewardStats = await pool.query(`
                SELECT 
                    COUNT(*) as total_rewards,
                    COUNT(*) FILTER (WHERE status = 'pending') as pending_rewards,
                    COUNT(*) FILTER (WHERE status = 'applied') as applied_rewards,
                    COUNT(*) FILTER (WHERE created_at >= NOW() - INTERVAL '24 hours') as rewards_last_24h,
                    COALESCE(SUM(amount) FILTER (WHERE status = 'applied'), 0) as total_applied_amount,
                    COALESCE(SUM(amount) FILTER (WHERE status = 'pending'), 0) as total_pending_amount
                FROM reward_queue
            `);

            // Get event processing statistics
            const eventStats = await pool.query(`
                SELECT 
                    COUNT(*) as total_events,
                    COUNT(*) FILTER (WHERE processed = true) as processed_events,
                    COUNT(*) FILTER (WHERE processed = false) as pending_events,
                    COUNT(*) FILTER (WHERE created_at >= NOW() - INTERVAL '24 hours') as events_last_24h
                FROM realtime_events
            `);

            return {
                analysis: analysisStats.rows[0],
                rewards: rewardStats.rows[0],
                events: eventStats.rows[0],
                generated_at: new Date().toISOString()
            };

        } catch (error) {
            logger.error('Error fetching AI system stats:', error);
            return null;
        }
    }

    /**
     * Check if AI Brain is healthy
     */
    async healthCheck() {
        try {
            // Check if recent events are being processed
            const recentProcessing = await pool.query(`
                SELECT COUNT(*) as processed_count
                FROM realtime_events 
                WHERE processed = true 
                AND processed_at >= NOW() - INTERVAL '1 hour'
            `);

            // Check if there are too many unprocessed events
            const unprocessedEvents = await pool.query(`
                SELECT COUNT(*) as unprocessed_count
                FROM realtime_events 
                WHERE processed = false 
                AND created_at <= NOW() - INTERVAL '10 minutes'
            `);

            const processedCount = parseInt(recentProcessing.rows[0].processed_count);
            const unprocessedCount = parseInt(unprocessedEvents.rows[0].unprocessed_count);

            const isHealthy = processedCount > 0 && unprocessedCount < 100;

            return {
                healthy: isHealthy,
                processed_last_hour: processedCount,
                unprocessed_old_events: unprocessedCount,
                status: isHealthy ? 'operational' : 'degraded',
                checked_at: new Date().toISOString()
            };

        } catch (error) {
            logger.error('Error checking AI health:', error);
            return {
                healthy: false,
                status: 'error',
                error: error.message,
                checked_at: new Date().toISOString()
            };
        }
    }

    /**
     * Cleanup old processed events
     * @param {number} daysOld - Days old to cleanup (default: 7)
     */
    async cleanupOldEvents(daysOld = 7) {
        try {
            const result = await pool.query(`
                DELETE FROM realtime_events 
                WHERE processed = true 
                AND processed_at < NOW() - INTERVAL '${daysOld} days'
            `);

            logger.info(`Cleaned up ${result.rowCount} old AI events`);
            return result.rowCount;

        } catch (error) {
            logger.error('Error cleaning up old AI events:', error);
            return 0;
        }
    }
}

// Create singleton instance
const aiService = new AIService();

module.exports = aiService;