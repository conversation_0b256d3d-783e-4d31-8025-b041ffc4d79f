"""
PayVendy AI Brain Orchestrator

Central orchestrator that manages all AI Brain operations including
scheduled analysis, reward processing, and real-time event handling.
"""

import asyncio
import time
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Any
import logging

from core.intelligence import AIIntelligenceEngine


class AIOrchestrator:
    """Central orchestrator for AI Brain operations."""
    
    def __init__(self, db_service, logger, config):
        self.db = db_service
        self.logger = logger
        self.config = config
        self.intelligence_engine = AIIntelligenceEngine(db_service)
        self.running = False
        self.tasks: List[asyncio.Task] = []
        
        # Performance tracking
        self.stats = {
            'analyses_completed': 0,
            'rewards_processed': 0,
            'fraud_alerts_generated': 0,
            'errors_encountered': 0,
            'last_cleanup': None,
            'uptime_start': datetime.utcnow()
        }
    
    async def initialize(self) -> None:
        """Initialize the orchestrator."""
        try:
            self.logger.info("Initializing AI Brain Orchestrator...")
            
            # Validate configuration
            self.config.validate_config()
            
            # Log startup configuration
            self.logger.log_system_event(
                'orchestrator_init',
                'AI Brain Orchestrator initialized successfully',
                metadata=self.config.to_dict()
            )
            
        except Exception as e:
            self.logger.error(f"Failed to initialize orchestrator: {str(e)}")
            raise
    
    async def run(self) -> None:
        """Main orchestrator loop."""
        self.running = True
        self.logger.info("Starting AI Brain Orchestrator main loop...")
        
        try:
            # Start background tasks
            self.tasks = [
                asyncio.create_task(self._user_analysis_loop()),
                asyncio.create_task(self._reward_processing_loop()),
                asyncio.create_task(self._cleanup_loop()),
                asyncio.create_task(self._performance_monitoring_loop())
            ]
            
            # Wait for all tasks to complete (or until shutdown)
            await asyncio.gather(*self.tasks, return_exceptions=True)
            
        except Exception as e:
            self.logger.error(f"Error in orchestrator main loop: {str(e)}")
            raise
        finally:
            await self.shutdown()
    
    async def shutdown(self) -> None:
        """Gracefully shutdown the orchestrator."""
        if not self.running:
            return
        
        self.logger.info("Shutting down AI Brain Orchestrator...")
        self.running = False
        
        # Cancel all running tasks
        for task in self.tasks:
            if not task.done():
                task.cancel()
        
        # Wait for tasks to complete cancellation
        if self.tasks:
            await asyncio.gather(*self.tasks, return_exceptions=True)
        
        # Log shutdown stats
        uptime = datetime.utcnow() - self.stats['uptime_start']
        self.logger.log_system_event(
            'orchestrator_shutdown',
            f"AI Brain Orchestrator shutdown complete. Uptime: {uptime}",
            metadata=self.stats
        )
    
    async def _user_analysis_loop(self) -> None:
        """Background loop for user behavior analysis."""
        self.logger.info("Starting user analysis loop...")
        
        while self.running:
            try:
                start_time = time.time()
                
                # Get batch of users to analyze
                user_ids = await self.db.get_batch_users_for_analysis(
                    self.config.operational.batch_size
                )
                
                if user_ids:
                    self.logger.info(f"Processing analysis batch of {len(user_ids)} users")
                    
                    # Process users in parallel with semaphore
                    semaphore = asyncio.Semaphore(self.config.operational.max_concurrent_operations)
                    
                    async def analyze_user_with_semaphore(user_id: str):
                        async with semaphore:
                            return await self._analyze_single_user(user_id)
                    
                    # Execute batch analysis
                    results = await asyncio.gather(
                        *[analyze_user_with_semaphore(uid) for uid in user_ids],
                        return_exceptions=True
                    )
                    
                    # Count results
                    success_count = sum(1 for r in results if r is True)
                    error_count = sum(1 for r in results if isinstance(r, Exception))
                    
                    processing_time = int((time.time() - start_time) * 1000)
                    
                    self.logger.log_batch_operation(
                        'user_analysis', len(user_ids), success_count, 
                        error_count, processing_time
                    )
                    
                    self.stats['analyses_completed'] += success_count
                    self.stats['errors_encountered'] += error_count
                
                # Wait for next interval
                await asyncio.sleep(self.config.operational.analysis_interval_seconds)
                
            except asyncio.CancelledError:
                break
            except Exception as e:
                self.logger.error(f"Error in user analysis loop: {str(e)}")
                self.stats['errors_encountered'] += 1
                await asyncio.sleep(60)  # Wait before retrying
    
    async def _analyze_single_user(self, user_id: str) -> bool:
        """Analyze a single user and handle rewards."""
        try:
            start_time = time.time()
            
            # Perform user analysis
            analysis = await self.intelligence_engine.analyze_user_behavior(user_id)
            
            # Save analysis to database
            await self.db.save_user_analysis(user_id, {
                'segment': analysis.segment.value,
                'risk_level': analysis.risk_level.value,
                'engagement_score': analysis.engagement_score,
                'transaction_velocity': analysis.transaction_velocity,
                'lifetime_value': analysis.lifetime_value,
                'confidence_score': analysis.confidence_score,
                'metrics': {
                    'days_since_last_activity': analysis.days_since_last_activity,
                    'suspicious_patterns': analysis.suspicious_patterns
                },
                'recommendations': analysis.recommended_actions
            })
            
            # Generate reward decisions
            reward_decisions = await self.intelligence_engine.decide_rewards(user_id, analysis)
            
            # Queue rewards
            for reward in reward_decisions:
                await self.db.queue_reward(
                    user_id=reward.user_id,
                    reward_type=reward.reward_type.value,
                    amount=reward.amount,
                    reason=reward.reason,
                    expires_at=reward.expires_at,
                    metadata={'confidence': reward.confidence}
                )
                
                self.logger.log_reward_decision(
                    reward.user_id, reward.reward_type.value, 
                    reward.amount, reward.reason, reward.confidence
                )
            
            # Run fraud detection
            fraud_result = await self.intelligence_engine.detect_fraud_patterns(user_id)
            
            if fraud_result['requires_investigation']:
                await self.db.log_fraud_alert(user_id, fraud_result)
                self.logger.log_fraud_detection(
                    user_id, fraud_result['risk_score'],
                    fraud_result['patterns_detected'],
                    fraud_result['requires_investigation']
                )
                self.stats['fraud_alerts_generated'] += 1
            
            processing_time = int((time.time() - start_time) * 1000)
            
            self.logger.log_user_analysis(
                user_id, {
                    'segment': analysis.segment.value,
                    'risk_level': analysis.risk_level.value,
                    'engagement_score': analysis.engagement_score
                }, processing_time, analysis.confidence_score
            )
            
            return True
            
        except Exception as e:
            self.logger.error(f"Error analyzing user {user_id}: {str(e)}")
            return False
    
    async def _reward_processing_loop(self) -> None:
        """Background loop for processing queued rewards."""
        self.logger.info("Starting reward processing loop...")
        
        while self.running:
            try:
                start_time = time.time()
                
                # Get pending rewards
                pending_rewards = await self.db.get_pending_rewards()
                
                if pending_rewards:
                    self.logger.info(f"Processing {len(pending_rewards)} pending rewards")
                    
                    success_count = 0
                    error_count = 0
                    
                    for reward in pending_rewards:
                        try:
                            success = await self.db.apply_reward(reward['id'])
                            if success:
                                success_count += 1
                                self.logger.info(
                                    f"Applied reward {reward['id']}: "
                                    f"{reward['reward_type']} ${reward['amount']} to user {reward['user_id']}"
                                )
                            else:
                                error_count += 1
                                self.logger.warning(f"Failed to apply reward {reward['id']}")
                                
                        except Exception as e:
                            error_count += 1
                            self.logger.error(f"Error applying reward {reward['id']}: {str(e)}")
                    
                    processing_time = int((time.time() - start_time) * 1000)
                    
                    self.logger.log_batch_operation(
                        'reward_processing', len(pending_rewards), 
                        success_count, error_count, processing_time
                    )
                    
                    self.stats['rewards_processed'] += success_count
                    self.stats['errors_encountered'] += error_count
                
                # Wait for next interval
                await asyncio.sleep(self.config.operational.reward_processing_interval_seconds)
                
            except asyncio.CancelledError:
                break
            except Exception as e:
                self.logger.error(f"Error in reward processing loop: {str(e)}")
                self.stats['errors_encountered'] += 1
                await asyncio.sleep(60)  # Wait before retrying
    
    async def _cleanup_loop(self) -> None:
        """Background loop for data cleanup."""
        self.logger.info("Starting cleanup loop...")
        
        while self.running:
            try:
                # Check if cleanup is due
                if (self.stats['last_cleanup'] is None or 
                    datetime.utcnow() - self.stats['last_cleanup'] >= 
                    timedelta(hours=self.config.operational.cleanup_interval_hours)):
                    
                    self.logger.info("Starting scheduled data cleanup...")
                    start_time = time.time()
                    
                    deleted_count = await self.db.cleanup_old_data()
                    
                    processing_time = int((time.time() - start_time) * 1000)
                    self.stats['last_cleanup'] = datetime.utcnow()
                    
                    self.logger.log_system_event(
                        'data_cleanup',
                        f"Cleanup completed: {deleted_count} records removed",
                        metadata={
                            'deleted_count': deleted_count,
                            'processing_time_ms': processing_time
                        }
                    )
                
                # Wait for next check (1 hour)
                await asyncio.sleep(3600)
                
            except asyncio.CancelledError:
                break
            except Exception as e:
                self.logger.error(f"Error in cleanup loop: {str(e)}")
                await asyncio.sleep(3600)  # Wait before retrying
    
    async def _performance_monitoring_loop(self) -> None:
        """Background loop for performance monitoring."""
        if not self.config.operational.enable_performance_monitoring:
            return
        
        self.logger.info("Starting performance monitoring loop...")
        
        while self.running:
            try:
                # Log performance metrics every 5 minutes
                uptime = datetime.utcnow() - self.stats['uptime_start']
                
                self.logger.log_performance_metric(
                    'analyses_per_hour',
                    self.stats['analyses_completed'] / max(uptime.total_seconds() / 3600, 1),
                    'analyses/hour'
                )
                
                self.logger.log_performance_metric(
                    'rewards_per_hour',
                    self.stats['rewards_processed'] / max(uptime.total_seconds() / 3600, 1),
                    'rewards/hour'
                )
                
                self.logger.log_performance_metric(
                    'error_rate',
                    self.stats['errors_encountered'] / max(
                        self.stats['analyses_completed'] + self.stats['rewards_processed'], 1
                    ),
                    'errors/operation'
                )
                
                await asyncio.sleep(300)  # 5 minutes
                
            except asyncio.CancelledError:
                break
            except Exception as e:
                self.logger.error(f"Error in performance monitoring: {str(e)}")
                await asyncio.sleep(300)
    
    async def process_realtime_event(self, event_data: Dict[str, Any]) -> None:
        """Process a real-time event."""
        try:
            user_id = event_data.get('user_id')
            event_type = event_data.get('event_type')
            
            if not user_id or not event_type:
                self.logger.warning("Invalid real-time event data", extra={'event_data': event_data})
                return
            
            self.logger.debug(f"Processing real-time event: {event_type} for user {user_id}")
            
            # Trigger immediate analysis for high-priority events
            priority_events = ['transaction_completed', 'large_transaction', 'suspicious_activity']
            
            if event_type in priority_events:
                await self._analyze_single_user(user_id)
            
            # Log the event processing
            await self.db.log_ai_action(
                action_type='realtime_event_processed',
                description=f"Processed real-time event: {event_type}",
                user_id=user_id,
                input_data=event_data,
                confidence_score=1.0
            )
            
        except Exception as e:
            self.logger.error(f"Error processing real-time event: {str(e)}", extra={'event_data': event_data})
    
    def get_stats(self) -> Dict[str, Any]:
        """Get current orchestrator statistics."""
        uptime = datetime.utcnow() - self.stats['uptime_start']
        
        return {
            **self.stats,
            'uptime_seconds': int(uptime.total_seconds()),
            'uptime_formatted': str(uptime),
            'running': self.running,
            'active_tasks': len([t for t in self.tasks if not t.done()]),
            'analyses_per_hour': self.stats['analyses_completed'] / max(uptime.total_seconds() / 3600, 1),
            'rewards_per_hour': self.stats['rewards_processed'] / max(uptime.total_seconds() / 3600, 1)
        }