import React, { useEffect, useState } from 'react';
import { View, Text, StyleSheet, TouchableOpacity, Platform } from 'react-native';
import Clipboard from '@react-native-clipboard/clipboard';
import { useTheme } from '../layout/ThemeContext';
import ModalBase from './ModalBase';

interface ClipboardPhoneModalProps {
  visible: boolean;
  phoneNumber: string;
  onIgnore: () => void;
  onUse: (phone: string, type: 'airtime' | 'data') => void;
}

const ClipboardPhoneModal: React.FC<ClipboardPhoneModalProps> = ({ visible, phoneNumber, onIgnore, onUse }) => {
  const { theme } = useTheme();
  return (
    <ModalBase visible={visible} onClose={onIgnore} variant="center">
      <View style={[styles.modal, { backgroundColor: '#232323', borderRadius: 18 }]}> 
        <Text style={[styles.title, { color: '#fff' }]}>Use this phone number?</Text>
        <View style={styles.accountBox}>
          <Text style={styles.accountLabel}>Phone Number</Text>
          <Text style={styles.accountNumber} numberOfLines={1} ellipsizeMode="middle">{formatPhoneNumber(phoneNumber)}</Text>
        </View>
        <Text style={styles.info}>Please verify the phone number, as successful purchase cannot be reversed.</Text>
        <View style={styles.buttonRow}>
          <TouchableOpacity style={styles.ignoreButton} onPress={onIgnore} activeOpacity={0.8}>
            <Text style={styles.ignoreText}>Ignore</Text>
          </TouchableOpacity>
        </View>
        <TouchableOpacity
          style={styles.useButton}
          onPress={() => onUse(phoneNumber, 'data')}
          activeOpacity={0.85}
        >
          <Text style={styles.useText}>Buy Data</Text>
        </TouchableOpacity>
      </View>
    </ModalBase>
  );
};

const styles = StyleSheet.create({
  overlay: {
    flex: 1,
    backgroundColor: 'rgba(0,0,0,0.55)',
    justifyContent: 'center',
    alignItems: 'center',
  },
  modal: {
    width: '88%',
    borderRadius: 22, // more curved corners
    paddingTop: 34,
    paddingBottom: 26,
    paddingHorizontal: 0,
    alignItems: 'center',
    shadowColor: '#000',
    shadowOpacity: 0.18,
    shadowRadius: 16,
    shadowOffset: { width: 0, height: 6 },
    elevation: 8,
  },
  title: {
    fontSize: 18,
    fontWeight: '700',
    marginBottom: 22,
    textAlign: 'center',
    letterSpacing: 0.1,
  },
  accountBox: {
    backgroundColor: '#181818',
    borderRadius: 14,
    paddingVertical: 18,
    paddingHorizontal: 28,
    marginBottom: 18,
    marginTop: 2,
    width: '95%', // widened to fit long numbers
    alignItems: 'center',
  },
  accountLabel: {
    color: '#aaa',
    fontSize: 13,
    marginBottom: 2,
    fontWeight: '500',
    letterSpacing: 0.5,
  },
  accountNumber: {
    fontSize: 26,
    fontWeight: '700',
    color: '#fff',
    letterSpacing: 2,
    textAlign: 'center',
    marginTop: 2,
  },
  info: {
    fontSize: 13,
    marginBottom: 28,
    textAlign: 'center',
    width: '80%',
    color: '#bdbdbd',
  },
  buttonRow: {
    flexDirection: 'row',
    width: '80%',
    justifyContent: 'center',
    marginBottom: 14,
    gap: 0,
  },
  actionButtonsRow: {
    flexDirection: 'row',
    width: '95%',
    justifyContent: 'space-between',
    marginTop: 6,
    gap: 10,
  },
  ignoreButton: {
    flex: 1,
    borderRadius: 22, // more curved
    paddingVertical: 15,
    alignItems: 'center',
    backgroundColor: '#1a2336',
    marginHorizontal: 0,
  },
  ignoreText: {
    fontSize: 16,
    fontWeight: '600',
    color: '#4A90E2',
    letterSpacing: 0.2,
  },
  useButton: {
    width: '80%',
    borderRadius: 22,
    paddingVertical: 18,
    alignItems: 'center',
    backgroundColor: '#4A90E2',
    marginTop: 6,
    borderWidth: 2,
    borderColor: '#6EC1FF', // subtle lighter blue border
    shadowColor: '#4A90E2',
    shadowOpacity: 0.25,
    shadowRadius: 10,
    shadowOffset: { width: 0, height: 6 },
    elevation: 8,
    // Add a slight scale effect on press (handled in TouchableOpacity below)
  },
  useText: {
    fontSize: 18, // slightly larger
    fontWeight: '800', // bolder
    color: '#fff',
    letterSpacing: 0.3,
    textShadowColor: 'rgba(0,0,0,0.15)',
    textShadowOffset: { width: 0, height: 2 },
    textShadowRadius: 2,
  },
});

// Helper to format phone number with international-style spacing
function formatPhoneNumber(phone: string) {
  // Remove all non-digits
  const digits = phone.replace(/\D/g, '');
  // +234 ************ or 0************
  if (digits.length === 13 && digits.startsWith('234')) {
    return `+234 ${digits.slice(3,6)} ${digits.slice(6,9)} ${digits.slice(9,13)}`;
  } else if (digits.length === 11 && digits.startsWith('0')) {
    return `${digits.slice(0,4)} ${digits.slice(4,7)} ${digits.slice(7,11)}`;
  } else if (digits.length === 10) {
    return `${digits.slice(0,3)} ${digits.slice(3,6)} ${digits.slice(6,10)}`;
  }
  return phone;
}

export default ClipboardPhoneModal;
