"""
PayVendy AI Brain - Main Application Entry Point
Autonomous AI system for user behavior analysis, rewards, and fraud detection
"""

import asyncio
import logging
import signal
import sys
from datetime import datetime
from typing import Optional

from config.settings import AIBrainConfig
from services.database import DatabaseService
from services.logger import AILogger
from orchestrator import AIOrchestrator


class AIBrainApp:
    """Main AI Brain application class"""
    
    def __init__(self):
        self.config = AIBrainConfig()
        self.logger = AILogger(self.config)
        self.db_service: Optional[DatabaseService] = None
        self.orchestrator: Optional[AIOrchestrator] = None
        self.running = False
        
    async def startup(self) -> None:
        """Initialize all services and start the AI brain"""
        try:
            self.logger.info("Starting PayVendy AI Brain...")
            
            # Initialize database service
            self.db_service = DatabaseService(self.config, self.logger)
            await self.db_service.initialize()
            
            # Initialize AI orchestrator
            self.orchestrator = AIOrchestrator(self.db_service, self.logger, self.config)
            await self.orchestrator.initialize()
            
            # Setup signal handlers for graceful shutdown
            signal.signal(signal.SIGINT, self._signal_handler)
            signal.signal(signal.SIGTERM, self._signal_handler)
            
            self.running = True
            self.logger.info("AI Brain started successfully")
            
            # Log startup metrics
            await self._log_startup_metrics()
            
        except Exception as e:
            self.logger.error(f"Failed to start AI Brain: {str(e)}")
            raise
    
    async def run(self) -> None:
        """Main application loop"""
        if not self.running:
            await self.startup()
        
        try:
            # Start the orchestrator main loop
            await self.orchestrator.run()
            
        except KeyboardInterrupt:
            self.logger.info("Received shutdown signal")
        except Exception as e:
            self.logger.error(f"Unexpected error in main loop: {str(e)}")
            raise
        finally:
            await self.shutdown()
    
    async def shutdown(self) -> None:
        """Gracefully shutdown all services"""
        if not self.running:
            return
        
        self.logger.info("Shutting down AI Brain...")
        self.running = False
        
        try:
            if self.orchestrator:
                await self.orchestrator.shutdown()
            
            if self.db_service:
                await self.db_service.close()
            
            self.logger.info("AI Brain shutdown complete")
            
        except Exception as e:
            self.logger.error(f"Error during shutdown: {str(e)}")
    
    def _signal_handler(self, signum, frame):
        """Handle shutdown signals"""
        self.logger.info(f"Received signal {signum}")
        self.running = False
    
    async def _log_startup_metrics(self) -> None:
        """Log startup metrics to database"""
        try:
            if self.db_service:
                await self.db_service.log_ai_action(
                    action_type="system_startup",
                    description="AI Brain system started successfully",
                    input_data={
                        "startup_time": datetime.utcnow().isoformat(),
                        "environment": self.config.environment,
                        "python_version": sys.version
                    },
                    output_data={"status": "success"},
                    confidence_score=1.0,
                    processing_time_ms=0
                )
        except Exception as e:
            self.logger.error(f"Failed to log startup metrics: {str(e)}")


async def main():
    """Application entry point"""
    app = AIBrainApp()
    
    try:
        await app.run()
    except Exception as e:
        logging.error(f"Fatal error: {str(e)}")
        sys.exit(1)


if __name__ == "__main__":
    # Configure basic logging for startup
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
    )
    
    # Run the application
    asyncio.run(main())
