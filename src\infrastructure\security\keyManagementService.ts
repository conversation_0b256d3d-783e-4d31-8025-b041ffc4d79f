import { Platform } from 'react-native';
import DeviceInfo from 'react-native-device-info';
import CryptoJS from 'crypto-js';
import { logger } from '../monitoring/productionLogger';
import { secureStorage } from './secureStorageService';

interface KeyDerivationConfig {
  iterations: number;
  keySize: number;
  saltSize: number;
  algorithm: string;
}

interface MasterKey {
  key: string;
  derivedAt: number;
  version: string;
  deviceFingerprint: string;
}

class KeyManagementService {
  private static instance: KeyManagementService;
  private masterKey: string | null = null;
  private keyCache = new Map<string, { key: string; expiresAt: number }>();
  
  private readonly config: KeyDerivationConfig = {
    iterations: 100000, // PBKDF2 iterations for production security
    keySize: 256 / 32,  // 256-bit key
    saltSize: 128 / 8,  // 128-bit salt
    algorithm: 'PBKDF2'
  };

  private constructor() {
    this.initializeKeyManagement();
  }

  static getInstance(): KeyManagementService {
    if (!KeyManagementService.instance) {
      KeyManagementService.instance = new KeyManagementService();
    }
    return KeyManagementService.instance;
  }

  private async initializeKeyManagement(): Promise<void> {
    try {
      await this.loadOrCreateMasterKey();
      logger.info('Key management service initialized', null, 'security');
    } catch (error) {
      logger.error('Failed to initialize key management', error, 'security');
      throw error;
    }
  }

  private async loadOrCreateMasterKey(): Promise<void> {
    try {
      // Try to load existing master key
      const storedKey = await secureStorage.getSecureItem('master_key_v2');
      
      if (storedKey) {
        const masterKeyData: MasterKey = JSON.parse(storedKey);
        
        // Verify device fingerprint matches
        const currentFingerprint = await this.generateDeviceFingerprint();
        if (masterKeyData.deviceFingerprint === currentFingerprint) {
          this.masterKey = masterKeyData.key;
          logger.info('Master key loaded from secure storage', null, 'security');
          return;
        } else {
          logger.warn('Device fingerprint mismatch, regenerating master key', null, 'security');
        }
      }

      // Create new master key
      await this.generateNewMasterKey();
    } catch (error) {
      logger.error('Failed to load/create master key', error, 'security');
      throw error;
    }
  }

  private async generateNewMasterKey(): Promise<void> {
    try {
      const deviceFingerprint = await this.generateDeviceFingerprint();
      const entropy = this.generateEntropy();
      const timestamp = Date.now();
      
      // Generate master key using device-specific data
      const keyMaterial = `${deviceFingerprint}-${entropy}-${timestamp}`;
      const salt = CryptoJS.lib.WordArray.random(this.config.saltSize);
      
      const masterKey = CryptoJS.PBKDF2(keyMaterial, salt, {
        keySize: this.config.keySize,
        iterations: this.config.iterations
      }).toString();

      const masterKeyData: MasterKey = {
        key: masterKey,
        derivedAt: timestamp,
        version: '2.0',
        deviceFingerprint
      };

      // Store securely
      await secureStorage.setSecureItem('master_key_v2', JSON.stringify(masterKeyData));
      this.masterKey = masterKey;
      
      logger.info('New master key generated and stored', {
        version: masterKeyData.version,
        derivedAt: masterKeyData.derivedAt
      }, 'security');
    } catch (error) {
      logger.error('Failed to generate master key', error, 'security');
      throw error;
    }
  }

  private async generateDeviceFingerprint(): Promise<string> {
    try {
      const deviceId = await DeviceInfo.getUniqueId();
      const bundleId = DeviceInfo.getBundleId();
      const version = DeviceInfo.getVersion();
      const buildNumber = DeviceInfo.getBuildNumber();
      
      const fingerprintData = {
        deviceId,
        bundleId,
        version,
        buildNumber,
        platform: Platform.OS,
        platformVersion: Platform.Version
      };

      return CryptoJS.SHA256(JSON.stringify(fingerprintData)).toString();
    } catch (error) {
      logger.error('Failed to generate device fingerprint', error, 'security');
      // Fallback to basic fingerprint
      return CryptoJS.SHA256(`${Platform.OS}-${Platform.Version}-fallback`).toString();
    }
  }

  private generateEntropy(): string {
    // Generate cryptographically secure random entropy
    const randomBytes = CryptoJS.lib.WordArray.random(32); // 256 bits
    const timestamp = Date.now();
    const performanceNow = performance.now();
    
    return CryptoJS.SHA256(`${randomBytes.toString()}-${timestamp}-${performanceNow}`).toString();
  }

  /**
   * Derive a specific key for a given purpose
   */
  async deriveKey(purpose: string, context?: string): Promise<string> {
    if (!this.masterKey) {
      await this.loadOrCreateMasterKey();
    }

    const cacheKey = `${purpose}-${context || 'default'}`;
    const cached = this.keyCache.get(cacheKey);
    
    if (cached && cached.expiresAt > Date.now()) {
      return cached.key;
    }

    try {
      const keyInfo = `${purpose}-${context || 'default'}-${Date.now()}`;
      const derivedKey = CryptoJS.HmacSHA256(keyInfo, this.masterKey!).toString();

      // Cache for 1 hour
      this.keyCache.set(cacheKey, {
        key: derivedKey,
        expiresAt: Date.now() + (60 * 60 * 1000)
      });

      return derivedKey;
    } catch (error) {
      logger.error('Failed to derive key', error, 'security');
      throw error;
    }
  }

  /**
   * Get encryption key for MMKV storage
   */
  async getStorageEncryptionKey(): Promise<string> {
    return this.deriveKey('storage', 'mmkv');
  }

  /**
   * Get encryption key for API request signing
   */
  async getApiSigningKey(): Promise<string> {
    return this.deriveKey('api', 'signing');
  }

  /**
   * Get encryption key for sensitive data
   */
  async getSensitiveDataKey(): Promise<string> {
    return this.deriveKey('sensitive', 'data');
  }

  /**
   * Rotate master key (should be called periodically)
   */
  async rotateMasterKey(): Promise<void> {
    try {
      logger.info('Starting master key rotation', null, 'security');
      
      // Clear cache
      this.keyCache.clear();
      
      // Generate new master key
      await this.generateNewMasterKey();
      
      logger.info('Master key rotation completed', null, 'security');
    } catch (error) {
      logger.error('Master key rotation failed', error, 'security');
      throw error;
    }
  }

  /**
   * Clear all cached keys
   */
  clearKeyCache(): void {
    this.keyCache.clear();
    logger.info('Key cache cleared', null, 'security');
  }

  /**
   * Get key management status
   */
  getStatus(): { initialized: boolean; cacheSize: number; masterKeyExists: boolean } {
    return {
      initialized: this.masterKey !== null,
      cacheSize: this.keyCache.size,
      masterKeyExists: this.masterKey !== null
    };
  }
}

// Export singleton instance
export const keyManagement = KeyManagementService.getInstance();
export default keyManagement;
