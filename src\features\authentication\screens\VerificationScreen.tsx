"use client"

import { useState, useRef, useEffect } from "react"
import { View, Text, StyleSheet, TouchableOpacity, StatusBar, SafeAreaView, TextInput, Animated, Alert } from "react-native"
import type { NativeStackScreenProps } from "@react-navigation/native-stack"
import type { RootStackParamList } from "../../../navigation/navigation"
import { useTheme } from "../../../shared/components/layout/ThemeContext"
import ApiService from "../../../infrastructure/api/apiService"
import { createUserDataFromAuth } from "../services/setupNavigation"
import secureStorage from "../../../infrastructure/security/secureStorageService"
import { logger } from '../../../infrastructure/monitoring/productionLogger';
import { navigationHandler } from '../../../navigation/handlers/navigationHandler';

type Props = NativeStackScreenProps<RootStackParamList, "Verification">

const VerificationScreen = ({ route }: Props) => {
  const { theme, isDark } = useTheme()
  const [code, setCode] = useState(["", "", "", "", "", ""])
  const [timer, setTimer] = useState(60)
  const [canResend, setCanResend] = useState(false)
  const [isLoading, setIsLoading] = useState(false)
  const [isResending, setIsResending] = useState(false)
  const buttonScaleAnim = useRef(new Animated.Value(1)).current
  const inputRefs = useRef<TextInput[]>([])

  // Get phone number from route params (passed from previous screen)
  const phoneNumber = route.params?.phoneNumber || "08012345678"

  useEffect(() => {
    const interval = setInterval(() => {
      setTimer((prev) => {
        if (prev <= 1) {
          setCanResend(true)
          clearInterval(interval)
          return 0
        }
        return prev - 1
      })
    }, 1000)

    return () => clearInterval(interval)
  }, [])

  const handleCodeChange = (text: string, index: number) => {
    const newCode = [...code]
    newCode[index] = text

    setCode(newCode)

    // Auto-focus next input
    if (text && index < 5) {
      inputRefs.current[index + 1]?.focus()
    }
  }

  const handleKeyPress = (key: string, index: number) => {
    if (key === "Backspace" && !code[index] && index > 0) {
      inputRefs.current[index - 1]?.focus()
    }
  }

  const handleVerify = async () => {
    const verificationCode = code.join("")
    if (verificationCode.length === 6 && !isLoading) {
      setIsLoading(true)

      Animated.sequence([
        Animated.timing(buttonScaleAnim, {
          toValue: 0.95,
          duration: 100,
          useNativeDriver: true,
        }),
        Animated.timing(buttonScaleAnim, {
          toValue: 1,
          duration: 100,
          useNativeDriver: true,
        }),
      ]).start(async () => {
        try {
          const response = await ApiService.verifyOTP(phoneNumber, verificationCode)

          if (response.status === 'success') {
            // Store authentication tokens securely
            const tokens = response.data?.tokens || response.data; // Handle both token structures
            const accessToken = tokens?.accessToken || response.data?.token;
            const refreshToken = tokens?.refreshToken || response.data?.refreshToken;
            
            if (accessToken && refreshToken) {
              try {
                await secureStorage.storeAuthTokens(accessToken, refreshToken);
                logger.security('AUTH_TOKENS_STORED', {
                  hasAccessToken: !!accessToken,
                  hasRefreshToken: !!refreshToken,
                  phoneNumber: phoneNumber ? phoneNumber.slice(0, 4) + '***' : 'unknown'
                });
                logger.info('✅ JWT tokens stored successfully after verification', null, 'auth');
              } catch (tokenError) {
                logger.error('Failed to store authentication tokens securely', tokenError, 'verification');
                throw new Error('Authentication storage failed');
              }
            } else {
              logger.warn('⚠️ No tokens received from verification response', { 
                hasTokens: !!tokens,
                hasAccessToken: !!accessToken,
                hasRefreshToken: !!refreshToken
              }, 'auth');
            }
            
            // Create standardized user data object
            const userData = createUserDataFromAuth(response, 'phone');
            // Ensure phone number is set correctly
            userData.phoneNumber = phoneNumber;
            userData.isPhoneVerified = true;

            logger.userAction('phone_verification_successful', {
              phoneNumberPrefix: phoneNumber ? phoneNumber.slice(0, 4) + '***' : 'unknown',
              hasUserData: !!userData,
              verificationMethod: 'sms_otp'
            });

            // Use navigation handler for phone verification success
            navigationHandler.navigateAfterPhoneVerification(userData);
          } else {
            Alert.alert('Error', response.message || 'Invalid verification code')
            setCode(["", "", "", "", "", ""])
            inputRefs.current[0]?.focus()
          }
        } catch (error) {
          Alert.alert('Error', error instanceof Error ? error.message : 'Verification failed')
          setCode(["", "", "", "", "", ""])
          inputRefs.current[0]?.focus()
        } finally {
          setIsLoading(false)
        }
      })
    }
  }

  const handleResend = async () => {
    if (canResend && !isResending) {
      setIsResending(true)

      try {
        const response = await ApiService.resendOTP()

        if (response.status === 'success') {
          setTimer(60)
          setCanResend(false)
          setCode(["", "", "", "", "", ""])
          inputRefs.current[0]?.focus()
          Alert.alert('Success', 'Verification code sent successfully')
        } else {
          Alert.alert('Error', response.message || 'Failed to resend code')
        }
      } catch (error) {
        Alert.alert('Error', error instanceof Error ? error.message : 'Failed to resend code')
      } finally {
        setIsResending(false)
      }
    }
  }

  const isCodeComplete = code.every(digit => digit !== "")

  const styles = StyleSheet.create({
    container: {
      flex: 1,
      backgroundColor: theme.colors.background,
    },
    header: {
      paddingHorizontal: 20,
      paddingTop: 20,
      paddingBottom: 20,
      flexDirection: "row",
      alignItems: "center",
    },
    backButton: {
      padding: 8,
    },
    backButtonText: {
      fontSize: 20,
      color: theme.colors.text,
      fontWeight: "400",
    },
    content: {
      paddingHorizontal: 24,
      paddingTop: 20,
      alignItems: "center",
    },
    title: {
      fontSize: 24,
      fontWeight: "600",
      color: theme.colors.text,
      marginBottom: 8,
      textAlign: "center",
    },
    subtitle: {
      fontSize: 15,
      color: theme.colors.muted,
      lineHeight: 22,
      marginBottom: 8,
      textAlign: "center",
      maxWidth: 280,
    },
    phoneNumber: {
      fontSize: 15,
      color: theme.colors.text,
      fontWeight: "600",
      marginBottom: 40,
      textAlign: "center",
    },
    codeContainer: {
      flexDirection: "row",
      justifyContent: "space-between",
      width: "100%",
      maxWidth: 300,
      marginBottom: 32,
    },
    codeInput: {
      width: 45,
      height: 50,
      backgroundColor: theme.colors.card,
      borderRadius: 8,
      borderWidth: 1,
      borderColor: theme.colors.border,
      textAlign: "center",
      fontSize: 18,
      fontWeight: "600",
      color: theme.colors.text,
    },
    codeInputFilled: {
      borderColor: theme.colors.primary,
      borderWidth: 2,
    },
    resendContainer: {
      alignItems: "center",
      marginBottom: 40,
    },
    resendButton: {
      padding: 12,
    },
    resendButtonText: {
      fontSize: 14,
      color: canResend ? theme.colors.primary : theme.colors.muted,
      fontWeight: "500",
      textAlign: "center",
    },
    bottomButtonContainer: {
      paddingHorizontal: 24,
      paddingBottom: 32,
      paddingTop: 16,
    },
    verifyButton: {
      backgroundColor: isCodeComplete ? "#007AFF" : theme.colors.muted,
      borderRadius: 12,
      paddingVertical: 16,
      alignItems: "center",
      justifyContent: "center",
      opacity: isCodeComplete ? 1 : 0.6,
    },
    verifyText: {
      color: "#FFFFFF",
      fontSize: 16,
      fontWeight: "600",
    },
    footer: {
      paddingHorizontal: 24,
      paddingBottom: 40,
    },
    footerText: {
      fontSize: 13,
      color: theme.colors.muted,
      textAlign: "center",
      lineHeight: 20,
    },
  })

  return (
    <SafeAreaView style={styles.container}>
      <StatusBar barStyle={isDark ? "light-content" : "dark-content"} backgroundColor={theme.colors.background} />

      {/* Header */}
      <View style={styles.header}>
        <TouchableOpacity
          style={styles.backButton}
          onPress={() => navigationHandler.navigateBackFromPhoneVerification()}
          activeOpacity={0.7}
        >
          <Text style={styles.backButtonText}>←</Text>
        </TouchableOpacity>
      </View>

      {/* Content */}
      <View style={styles.content}>
        <Text style={styles.title}>Enter verification code</Text>
        <Text style={styles.subtitle}>
          We sent a 6-digit code to
        </Text>
        <Text style={styles.phoneNumber}>{phoneNumber}</Text>

        {/* Code Input */}
        <View style={styles.codeContainer}>
          {code.map((digit, index) => (
            <TextInput
              key={index}
              ref={(ref) => {
                if (ref) inputRefs.current[index] = ref
              }}
              style={[
                styles.codeInput,
                digit && styles.codeInputFilled
              ]}
              value={digit}
              onChangeText={(text) => handleCodeChange(text, index)}
              onKeyPress={({ nativeEvent }) => handleKeyPress(nativeEvent.key, index)}
              keyboardType="number-pad"
              maxLength={1}
              autoFocus={index === 0}
            />
          ))}
        </View>

        {/* Resend */}
        <View style={styles.resendContainer}>
          <TouchableOpacity
            style={styles.resendButton}
            onPress={handleResend}
            disabled={!canResend}
            activeOpacity={0.7}
          >
            <Text style={styles.resendButtonText}>
              {isResending ? "Sending..." : canResend ? "Resend code" : `Resend code in ${timer}s`}
            </Text>
          </TouchableOpacity>
        </View>
      </View>

      {/* Spacer to push button to bottom */}
      <View style={{ flex: 1 }} />

      {/* Bottom Button */}
      <View style={styles.bottomButtonContainer}>
        <Animated.View
          style={[
            styles.verifyButton,
            {
              transform: [{ scale: buttonScaleAnim }],
            },
          ]}
        >
          <TouchableOpacity
            onPress={handleVerify}
            disabled={!isCodeComplete}
            activeOpacity={0.85}
            style={{ width: "100%", alignItems: "center" }}
          >
            <Text style={styles.verifyText}>
              {isLoading ? 'Verifying...' : 'Verify'}
            </Text>
          </TouchableOpacity>
        </Animated.View>
      </View>

      {/* Footer */}
      <View style={styles.footer}>
        <Text style={styles.footerText}>
          Enter the 6-digit code to verify your phone number
        </Text>
      </View>
    </SafeAreaView>
  )
}

export default VerificationScreen
