-- Pay<PERSON><PERSON>y AI Brain Database Tables
-- Migration 007: Create AI Brain related tables

-- AI Logs table for tracking all AI actions and decisions
CREATE TABLE IF NOT EXISTS ai_logs (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id UUID REFERENCES users(id) ON DELETE CASCADE,
    action_type VARCHAR(100) NOT NULL,
    description TEXT,
    input_data JSONB,
    output_data JSONB,
    confidence_score DECIMAL(3,2) CHECK (confidence_score >= 0 AND confidence_score <= 1),
    processing_time_ms INTEGER DEFAULT 0,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    
    -- Indexes for performance
    INDEX idx_ai_logs_user_id (user_id),
    INDEX idx_ai_logs_action_type (action_type),
    INDEX idx_ai_logs_created_at (created_at),
    INDEX idx_ai_logs_confidence (confidence_score)
);

-- Behavior Analytics table for storing user behavior analysis results
CREATE TABLE IF NOT EXISTS behavior_analytics (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE,
    analysis_type VARCHAR(50) NOT NULL DEFAULT 'comprehensive',
    period_start TIMESTAMP WITH TIME ZONE NOT NULL,
    period_end TIMESTAMP WITH TIME ZONE NOT NULL,
    metrics JSONB NOT NULL DEFAULT '{}',
    insights JSONB NOT NULL DEFAULT '{}',
    recommendations JSONB NOT NULL DEFAULT '[]',
    confidence_level DECIMAL(3,2) NOT NULL CHECK (confidence_level >= 0 AND confidence_level <= 1),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    
    -- Indexes
    INDEX idx_behavior_analytics_user_id (user_id),
    INDEX idx_behavior_analytics_type (analysis_type),
    INDEX idx_behavior_analytics_created_at (created_at),
    INDEX idx_behavior_analytics_period (period_start, period_end)
);

-- User Segments table for AI-driven user categorization
CREATE TABLE IF NOT EXISTS user_segments (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE,
    segment_name VARCHAR(50) NOT NULL,
    segment_score DECIMAL(3,2) NOT NULL CHECK (segment_score >= 0 AND segment_score <= 1),
    attributes JSONB NOT NULL DEFAULT '{}',
    last_updated TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    
    -- Ensure unique segment per user
    UNIQUE(user_id, segment_name),
    
    -- Indexes
    INDEX idx_user_segments_user_id (user_id),
    INDEX idx_user_segments_name (segment_name),
    INDEX idx_user_segments_score (segment_score),
    INDEX idx_user_segments_updated (last_updated)
);

-- Reward Queue table for AI-generated rewards
CREATE TABLE IF NOT EXISTS reward_queue (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE,
    reward_type VARCHAR(50) NOT NULL,
    amount DECIMAL(10,2) NOT NULL CHECK (amount > 0),
    reason TEXT NOT NULL,
    status VARCHAR(20) NOT NULL DEFAULT 'pending' CHECK (status IN ('pending', 'applied', 'expired', 'cancelled')),
    expires_at TIMESTAMP WITH TIME ZONE,
    applied_at TIMESTAMP WITH TIME ZONE,
    metadata JSONB DEFAULT '{}',
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    
    -- Indexes
    INDEX idx_reward_queue_user_id (user_id),
    INDEX idx_reward_queue_status (status),
    INDEX idx_reward_queue_type (reward_type),
    INDEX idx_reward_queue_created_at (created_at),
    INDEX idx_reward_queue_expires_at (expires_at)
);

-- Real-time Events table for AI processing queue
CREATE TABLE IF NOT EXISTS realtime_events (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id UUID REFERENCES users(id) ON DELETE CASCADE,
    event_type VARCHAR(100) NOT NULL,
    event_data JSONB NOT NULL DEFAULT '{}',
    processed BOOLEAN DEFAULT FALSE,
    processed_at TIMESTAMP WITH TIME ZONE,
    error_message TEXT,
    retry_count INTEGER DEFAULT 0,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    
    -- Indexes for AI processing
    INDEX idx_realtime_events_processed (processed),
    INDEX idx_realtime_events_user_id (user_id),
    INDEX idx_realtime_events_type (event_type),
    INDEX idx_realtime_events_created_at (created_at),
    INDEX idx_realtime_events_processing_queue (processed, created_at) WHERE NOT processed
);

-- Fraud Alerts table for storing fraud detection results
CREATE TABLE IF NOT EXISTS fraud_alerts (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE,
    alert_type VARCHAR(50) NOT NULL,
    risk_score DECIMAL(3,2) NOT NULL CHECK (risk_score >= 0 AND risk_score <= 1),
    patterns_detected JSONB NOT NULL DEFAULT '[]',
    recommendations JSONB NOT NULL DEFAULT '[]',
    status VARCHAR(20) NOT NULL DEFAULT 'open' CHECK (status IN ('open', 'investigating', 'resolved', 'false_positive')),
    investigated_by UUID REFERENCES users(id),
    investigated_at TIMESTAMP WITH TIME ZONE,
    resolution_notes TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    
    -- Indexes
    INDEX idx_fraud_alerts_user_id (user_id),
    INDEX idx_fraud_alerts_status (status),
    INDEX idx_fraud_alerts_risk_score (risk_score),
    INDEX idx_fraud_alerts_created_at (created_at)
);

-- AI Configuration table for dynamic AI parameters
CREATE TABLE IF NOT EXISTS ai_configuration (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    config_key VARCHAR(100) NOT NULL UNIQUE,
    config_value JSONB NOT NULL,
    description TEXT,
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    
    -- Index
    INDEX idx_ai_configuration_key (config_key),
    INDEX idx_ai_configuration_active (is_active)
);

-- Functions for reward processing
CREATE OR REPLACE FUNCTION apply_reward(
    p_user_id UUID,
    p_amount DECIMAL(10,2),
    p_reason TEXT,
    p_reward_id UUID DEFAULT NULL
) RETURNS BOOLEAN AS $$
DECLARE
    current_balance DECIMAL(10,2);
    new_balance DECIMAL(10,2);
BEGIN
    -- Get current balance
    SELECT COALESCE(balance, 0) INTO current_balance
    FROM users 
    WHERE id = p_user_id;
    
    -- Calculate new balance
    new_balance := current_balance + p_amount;
    
    -- Update user balance
    UPDATE users 
    SET balance = new_balance,
        updated_at = NOW()
    WHERE id = p_user_id;
    
    -- Mark reward as applied if reward_id provided
    IF p_reward_id IS NOT NULL THEN
        UPDATE reward_queue 
        SET status = 'applied',
            applied_at = NOW()
        WHERE id = p_reward_id;
    END IF;
    
    -- Log the transaction
    INSERT INTO ai_logs (
        user_id, 
        action_type, 
        description, 
        output_data,
        confidence_score,
        created_at
    ) VALUES (
        p_user_id,
        'reward_applied',
        p_reason,
        jsonb_build_object(
            'amount', p_amount,
            'previous_balance', current_balance,
            'new_balance', new_balance,
            'reward_id', p_reward_id
        ),
        1.0,
        NOW()
    );
    
    RETURN TRUE;
    
EXCEPTION WHEN OTHERS THEN
    -- Log error
    INSERT INTO ai_logs (
        user_id, 
        action_type, 
        description, 
        output_data,
        confidence_score,
        created_at
    ) VALUES (
        p_user_id,
        'reward_application_error',
        'Failed to apply reward: ' || SQLERRM,
        jsonb_build_object(
            'amount', p_amount,
            'error', SQLERRM,
            'reward_id', p_reward_id
        ),
        0.0,
        NOW()
    );
    
    RETURN FALSE;
END;
$$ LANGUAGE plpgsql;

-- Function to clean up old processed events
CREATE OR REPLACE FUNCTION cleanup_old_events(days_old INTEGER DEFAULT 7) RETURNS INTEGER AS $$
DECLARE
    deleted_count INTEGER;
BEGIN
    DELETE FROM realtime_events 
    WHERE processed = TRUE 
    AND processed_at < NOW() - INTERVAL '1 day' * days_old;
    
    GET DIAGNOSTICS deleted_count = ROW_COUNT;
    
    -- Log cleanup
    INSERT INTO ai_logs (
        action_type, 
        description, 
        output_data,
        confidence_score,
        created_at
    ) VALUES (
        'system_cleanup',
        'Cleaned up old processed events',
        jsonb_build_object(
            'deleted_count', deleted_count,
            'days_old', days_old
        ),
        1.0,
        NOW()
    );
    
    RETURN deleted_count;
END;
$$ LANGUAGE plpgsql;

-- Function to cleanup old AI data (called by AI Brain)
CREATE OR REPLACE FUNCTION cleanup_old_ai_data()
RETURNS INTEGER AS $$
DECLARE
    deleted_count INTEGER := 0;
    temp_count INTEGER;
BEGIN
    -- Clean up old AI logs (keep last 90 days)
    DELETE FROM ai_logs WHERE created_at < NOW() - INTERVAL '90 days';
    GET DIAGNOSTICS temp_count = ROW_COUNT;
    deleted_count := deleted_count + temp_count;
    
    -- Clean up expired rewards
    DELETE FROM reward_queue WHERE status = 'pending' AND expires_at < NOW();
    GET DIAGNOSTICS temp_count = ROW_COUNT;
    deleted_count := deleted_count + temp_count;
    
    -- Clean up old processed events (keep last 30 days)
    DELETE FROM realtime_events WHERE processed = TRUE AND processed_at < NOW() - INTERVAL '30 days';
    GET DIAGNOSTICS temp_count = ROW_COUNT;
    deleted_count := deleted_count + temp_count;
    
    -- Clean up old behavior analytics (keep last 180 days)
    DELETE FROM behavior_analytics WHERE created_at < NOW() - INTERVAL '180 days';
    GET DIAGNOSTICS temp_count = ROW_COUNT;
    deleted_count := deleted_count + temp_count;
    
    -- Log cleanup action
    INSERT INTO ai_logs (
        action_type, 
        description, 
        output_data,
        confidence_score,
        created_at
    ) VALUES (
        'system_cleanup',
        'AI Brain data cleanup completed',
        jsonb_build_object(
            'total_deleted_count', deleted_count
        ),
        1.0,
        NOW()
    );
    
    RETURN deleted_count;
END;
$$ LANGUAGE plpgsql;

-- Insert default AI configuration
INSERT INTO ai_configuration (config_key, config_value, description) VALUES
('engagement_threshold', '0.6', 'Minimum engagement score for rewards'),
('suspicious_threshold', '0.8', 'Risk score threshold for fraud alerts'),
('base_reward_amount', '10.0', 'Base reward amount in currency'),
('max_daily_rewards', '5', 'Maximum rewards per user per day'),
('high_value_threshold', '1000.0', 'Threshold for high-value user classification')
ON CONFLICT (config_key) DO NOTHING;

-- Create indexes for performance optimization
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_users_balance ON users(balance);
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_users_created_at ON users(created_at);
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_users_last_login ON users(last_login);

-- Add comments for documentation
COMMENT ON TABLE ai_logs IS 'Stores all AI system actions and decisions for audit and analysis';
COMMENT ON TABLE behavior_analytics IS 'Stores user behavior analysis results from AI system';
COMMENT ON TABLE user_segments IS 'AI-driven user categorization and segmentation';
COMMENT ON TABLE reward_queue IS 'Queue of AI-generated rewards pending application';
COMMENT ON TABLE realtime_events IS 'Real-time events queue for AI processing';
COMMENT ON TABLE fraud_alerts IS 'Fraud detection alerts generated by AI system';
COMMENT ON TABLE ai_configuration IS 'Dynamic configuration parameters for AI system';

-- Grant permissions (adjust as needed for your setup)
-- GRANT SELECT, INSERT, UPDATE, DELETE ON ALL TABLES IN SCHEMA public TO ai_brain_user;
-- GRANT USAGE, SELECT ON ALL SEQUENCES IN SCHEMA public TO ai_brain_user;