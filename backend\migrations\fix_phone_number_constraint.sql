-- =====================================================
-- FIX PHONE NUMBER CONSTRAINT FOR EMAIL-ONLY USERS
-- =====================================================
-- This migration allows users to be created with email only
-- by making phone_number nullable and adding proper constraints

-- Make phone_number nullable to allow email-only users
ALTER TABLE users ALTER COLUMN phone_number DROP NOT NULL;

-- Add constraint to ensure at least email OR phone_number is provided
ALTER TABLE users ADD CONSTRAINT check_email_or_phone 
CHECK (
    (email IS NOT NULL AND email != '') OR 
    (phone_number IS NOT NULL AND phone_number != '')
);

-- Update unique constraint to handle nulls properly
-- Drop existing unique constraint on phone_number if it exists
DO $$
BEGIN
    IF EXISTS (SELECT 1 FROM information_schema.table_constraints 
               WHERE constraint_name = 'users_phone_number_key' 
               AND table_name = 'users') THEN
        ALTER TABLE users DROP CONSTRAINT users_phone_number_key;
        RAISE NOTICE 'Dropped existing phone_number unique constraint';
    END IF;
END $$;

-- Create partial unique index for phone_number (only when not null)
CREATE UNIQUE INDEX IF NOT EXISTS idx_users_phone_number_unique 
ON users(phone_number) WHERE phone_number IS NOT NULL;

-- Create partial unique index for email (only when not null)
CREATE UNIQUE INDEX IF NOT EXISTS idx_users_email_unique 
ON users(email) WHERE email IS NOT NULL;

-- Update existing users table structure info
COMMENT ON COLUMN users.phone_number IS 'Phone number (nullable for email-only users)';
COMMENT ON COLUMN users.email IS 'Email address (nullable for phone-only users)';

SELECT 'Phone number constraint fix completed successfully!' as message;
