#!/bin/bash

# PayVendy Complete System Startup Script
# Starts both the backend API and AI Brain system

set -e

echo "🚀 Starting PayVendy Complete System with AI Brain"
echo "=================================================="

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Check if required commands exist
check_requirements() {
    print_status "Checking system requirements..."
    
    if ! command -v node &> /dev/null; then
        print_error "Node.js is not installed"
        exit 1
    fi
    
    if ! command -v python3 &> /dev/null; then
        print_error "Python 3 is not installed"
        exit 1
    fi
    
    if ! command -v docker &> /dev/null; then
        print_warning "Docker is not installed - manual database setup required"
    fi
    
    print_success "System requirements check passed"
}

# Setup environment
setup_environment() {
    print_status "Setting up environment..."
    
    # Create logs directories
    mkdir -p logs
    mkdir -p ai_brain/logs
    
    # Check for .env file
    if [ ! -f .env ]; then
        print_warning ".env file not found - creating from template"
        if [ -f .env.example ]; then
            cp .env.example .env
            print_status "Please edit .env file with your configuration"
        else
            print_error ".env.example not found - please create .env manually"
            exit 1
        fi
    fi
    
    print_success "Environment setup complete"
}

# Install dependencies
install_dependencies() {
    print_status "Installing dependencies..."
    
    # Backend dependencies
    print_status "Installing backend dependencies..."
    cd backend
    npm install
    cd ..
    
    # AI Brain dependencies
    print_status "Installing AI Brain dependencies..."
    cd ai_brain
    pip3 install -r requirements.txt
    cd ..
    
    print_success "Dependencies installed"
}

# Setup database
setup_database() {
    print_status "Setting up database..."
    
    # Check if PostgreSQL is running
    if ! pg_isready -h localhost -p 5432 &> /dev/null; then
        print_warning "PostgreSQL not running on localhost:5432"
        print_status "Starting with Docker Compose..."
        
        if command -v docker-compose &> /dev/null; then
            docker-compose up -d postgres
            sleep 10
        else
            print_error "Please start PostgreSQL manually or install Docker Compose"
            exit 1
        fi
    fi
    
    # Run database migrations
    print_status "Running database migrations..."
    cd backend
    
    # Run main migrations
    if [ -f scripts/setup-database.js ]; then
        node scripts/setup-database.js
    fi
    
    # Run AI Brain migration
    node scripts/run-ai-migration.js
    
    cd ..
    
    print_success "Database setup complete"
}

# Start services
start_services() {
    print_status "Starting services..."
    
    # Create log files
    touch logs/backend.log
    touch logs/ai-brain.log
    
    # Start backend
    print_status "Starting backend API server..."
    cd backend
    nohup npm start > ../logs/backend.log 2>&1 &
    BACKEND_PID=$!
    echo $BACKEND_PID > ../logs/backend.pid
    cd ..
    
    # Wait for backend to start
    sleep 5
    
    # Check if backend is running
    if ! curl -s http://localhost:3000/health > /dev/null; then
        print_error "Backend failed to start - check logs/backend.log"
        exit 1
    fi
    
    print_success "Backend API started (PID: $BACKEND_PID)"
    
    # Start AI Brain
    print_status "Starting AI Brain system..."
    cd ai_brain
    nohup python3 start.py > ../logs/ai-brain.log 2>&1 &
    AI_BRAIN_PID=$!
    echo $AI_BRAIN_PID > ../logs/ai-brain.pid
    cd ..
    
    # Wait for AI Brain to start
    sleep 10
    
    print_success "AI Brain started (PID: $AI_BRAIN_PID)"
}

# Health check
health_check() {
    print_status "Performing health checks..."
    
    # Check backend
    if curl -s http://localhost:3000/health | grep -q "success"; then
        print_success "Backend API is healthy"
    else
        print_error "Backend API health check failed"
    fi
    
    # Check AI Brain (if it has a health endpoint)
    # This would need to be implemented in the AI Brain
    
    # Check database connectivity
    if pg_isready -h localhost -p 5432 &> /dev/null; then
        print_success "Database is accessible"
    else
        print_warning "Database connectivity check failed"
    fi
    
    print_success "Health checks complete"
}

# Display status
show_status() {
    echo ""
    echo "🎉 PayVendy System Started Successfully!"
    echo "======================================="
    echo ""
    echo "📊 Service Status:"
    echo "  • Backend API: http://localhost:3000"
    echo "  • AI Brain: Running in background"
    echo "  • Database: PostgreSQL on localhost:5432"
    echo ""
    echo "📋 Process IDs:"
    if [ -f logs/backend.pid ]; then
        echo "  • Backend: $(cat logs/backend.pid)"
    fi
    if [ -f logs/ai-brain.pid ]; then
        echo "  • AI Brain: $(cat logs/ai-brain.pid)"
    fi
    echo ""
    echo "📝 Log Files:"
    echo "  • Backend: logs/backend.log"
    echo "  • AI Brain: logs/ai-brain.log"
    echo ""
    echo "🔧 Management Commands:"
    echo "  • Stop all: ./stop-system.sh"
    echo "  • View logs: tail -f logs/backend.log"
    echo "  • AI Brain logs: tail -f logs/ai-brain.log"
    echo ""
    echo "🌐 API Endpoints:"
    echo "  • Health: GET http://localhost:3000/health"
    echo "  • AI Stats: GET http://localhost:3000/api/v1/ai/stats"
    echo ""
}

# Cleanup function
cleanup() {
    print_status "Cleaning up..."
    
    if [ -f logs/backend.pid ]; then
        BACKEND_PID=$(cat logs/backend.pid)
        if kill -0 $BACKEND_PID 2>/dev/null; then
            kill $BACKEND_PID
            print_status "Stopped backend (PID: $BACKEND_PID)"
        fi
        rm -f logs/backend.pid
    fi
    
    if [ -f logs/ai-brain.pid ]; then
        AI_BRAIN_PID=$(cat logs/ai-brain.pid)
        if kill -0 $AI_BRAIN_PID 2>/dev/null; then
            kill $AI_BRAIN_PID
            print_status "Stopped AI Brain (PID: $AI_BRAIN_PID)"
        fi
        rm -f logs/ai-brain.pid
    fi
}

# Trap cleanup on exit
trap cleanup EXIT

# Main execution
main() {
    case "${1:-start}" in
        "start")
            check_requirements
            setup_environment
            install_dependencies
            setup_database
            start_services
            health_check
            show_status
            
            # Keep script running
            print_status "System is running. Press Ctrl+C to stop."
            while true; do
                sleep 30
                # Optional: Add periodic health checks here
            done
            ;;
        "stop")
            cleanup
            print_success "System stopped"
            ;;
        "status")
            health_check
            show_status
            ;;
        "logs")
            if [ "$2" = "ai" ]; then
                tail -f logs/ai-brain.log
            else
                tail -f logs/backend.log
            fi
            ;;
        *)
            echo "Usage: $0 {start|stop|status|logs [ai]}"
            exit 1
            ;;
    esac
}

# Run main function
main "$@"