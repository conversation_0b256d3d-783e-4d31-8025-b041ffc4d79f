import { apiService } from '../../../infrastructure/api/apiService';
import { logger } from '../../../infrastructure/monitoring/productionLogger';

export type SetupStep = 'NameSetup' | 'PinSetup' | 'PinVerification' | 'BiometricSetup' | 'SetupComplete';

export interface SetupStatus {
  hasPinSetup: boolean;
  hasBiometricSetup: boolean;
  hasProfileSetup: boolean;
  isEmailVerified: boolean;
  isPhoneVerified: boolean;
  setupComplete: boolean;
}

export interface UserData {
  id: string;
  firstName?: string;
  lastName?: string;
  email?: string;
  phoneNumber?: string;
  isEmailVerified: boolean;
  isPhoneVerified: boolean;
  picture?: string;
}

export interface SetupStatusResponse {
  setupStatus: SetupStatus;
  user: UserData;
}

class OptimizedSetupService {
  private setupStatusCache: { data: SetupStatusResponse; timestamp: number } | null = null;
  private cacheTimeout = 2 * 60 * 1000; // 2 minutes
  private pendingRequest: Promise<SetupStatusResponse> | null = null;

  /**
   * Get current setup status for the user with optimizations
   */
  async getSetupStatus(): Promise<SetupStatusResponse> {
    try {
      // Check cache first
      if (this.setupStatusCache) {
        const age = Date.now() - this.setupStatusCache.timestamp;
        if (age < this.cacheTimeout) {
productionLogger.info('🚀 [OPTIMIZED-SETUP] Using cached setup status', { context: 'optimizedSetupService', method: 'getSetupStatus' });
          return this.setupStatusCache.data;
        } else {
productionLogger.info('🔄 [OPTIMIZED-SETUP] Cache expired, clearing', { context: 'optimizedSetupService', method: 'getSetupStatus' });
          this.setupStatusCache = null;
        }
      }

      // Check if there's already a pending request
      if (this.pendingRequest) {
productionLogger.info('🔄 [OPTIMIZED-SETUP] Request already in progress, waiting...', { context: 'optimizedSetupService', method: 'getSetupStatus' });
        return this.pendingRequest;
      }

productionLogger.info('🔍 [OPTIMIZED-SETUP] Making fresh API call to /setup/status', { context: 'optimizedSetupService', method: 'getSetupStatus' });
      
      // Create the request promise
      this.pendingRequest = this.fetchSetupStatus();
      
      try {
        const result = await this.pendingRequest;
        
        // Cache the result
        this.setupStatusCache = {
          data: result,
          timestamp: Date.now()
        };
        
productionLogger.info('✅ [OPTIMIZED-SETUP] Setup status fetched and cached', { context: 'optimizedSetupService', method: 'getSetupStatus' });
        return result;
      } finally {
        // Clear pending request
        this.pendingRequest = null;
      }
    } catch (error: any) {
productionLogger.error('❌ [OPTIMIZED-SETUP] Get setup status error', { context: 'optimizedSetupService', method: 'getSetupStatus', error });
      
      // Clear pending request on error
      this.pendingRequest = null;
      
      // Provide more specific error messages for better debugging
      if (error.name === 'AbortError' || error.message === 'Aborted') {
        throw new Error('Setup status request timed out. Please check your connection and try again.');
      } else if (error.message?.includes('Network')) {
        throw new Error('Network error while checking setup status. Please check your connection.');
      } else {
        throw new Error(error.response?.data?.message || error.message || 'Failed to get setup status');
      }
    }
  }

  /**
   * Internal method to fetch setup status from API
   */
  private async fetchSetupStatus(): Promise<SetupStatusResponse> {
    // Use optimized configuration for setup status - skip auth since it's public
    const response = await apiService.get('/setup/status', {}, {
      timeout: 15000, // Reduced timeout to 15 seconds
      retries: 1, // Reduce retries to avoid long wait times
      cache: false, // We handle caching ourselves
      skipAuth: true, // Skip authentication for public setup status endpoint
    });
    
productionLogger.debug('🔍 [OPTIMIZED-SETUP] Raw API response', { context: 'optimizedSetupService', method: 'fetchSetupStatus', response });
    
    if (response.data.status === 'success') {
productionLogger.debug('🔍 [OPTIMIZED-SETUP] Returning data', { context: 'optimizedSetupService', method: 'fetchSetupStatus', data: response.data.data });
      return response.data.data;
    } else {
      throw new Error(response.data.message || 'Failed to get setup status');
    }
  }

  /**
   * Clear the cache (useful when user data changes)
   */
  clearCache(): void {
productionLogger.info('🗑️ [OPTIMIZED-SETUP] Clearing setup status cache', { context: 'optimizedSetupService', method: 'clearCache' });
    this.setupStatusCache = null;
  }

  /**
   * Check if user needs to go through setup flow
   */
  async shouldShowSetup(): Promise<boolean> {
    try {
      const { setupStatus } = await this.getSetupStatus();
      return !setupStatus.setupComplete;
    } catch (error) {
productionLogger.error('Error checking setup status', { context: 'optimizedSetupService', method: 'shouldShowSetup', error });
      // If we can't check, assume setup is needed
      return true;
    }
  }

  /**
   * Get next setup step based on current status
   */
  async getNextSetupStep(): Promise<SetupStep | null> {
    try {
      const { setupStatus } = await this.getSetupStatus();
      
      if (setupStatus.setupComplete) {
        return null; // No setup needed
      }
      
      if (!setupStatus.hasProfileSetup) {
        return 'NameSetup';
      }
      
      if (!setupStatus.hasPinSetup) {
        return 'PinSetup'; // Create new PIN
      }
      
      // If PIN is set but setup is not complete, verify PIN first
      if (setupStatus.hasPinSetup && !setupStatus.setupComplete) {
        return 'PinVerification'; // Verify existing PIN
      }
      
      if (!setupStatus.hasBiometricSetup) {
        return 'BiometricSetup';
      }
      
      return 'SetupComplete';
    } catch (error) {
productionLogger.error('Error getting next setup step', { context: 'optimizedSetupService', method: 'getNextSetupStep', error });
      return 'NameSetup'; // Default to first step
    }
  }

  /**
   * Determine if user should see PIN setup or PIN verification
   */
  async getPinFlowType(): Promise<'setup' | 'verify' | 'complete'> {
    try {
      const { setupStatus } = await this.getSetupStatus();
      
      if (setupStatus.setupComplete) {
        return 'complete'; // No PIN flow needed
      }
      
      if (setupStatus.hasPinSetup) {
        return 'verify'; // PIN exists, show verification
      } else {
        return 'setup'; // No PIN, show setup
      }
    } catch (error) {
productionLogger.error('Error determining PIN flow type', { context: 'optimizedSetupService', method: 'getPinFlowType', error });
      return 'setup'; // Default to setup
    }
  }
}

export const optimizedSetupService = new OptimizedSetupService();