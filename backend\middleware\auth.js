/**
 * Authentication and Authorization Middleware
 * 
 * Provides token authentication and role-based access control
 * for protected routes in the PayVendy API.
 */

const jwt = require('jsonwebtoken');
const { getSupabase } = require('../config/database');
const logger = require('../utils/logger');

/**
 * Middleware to authenticate JWT tokens
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 * @param {Function} next - Express next function
 */
const authenticateToken = async (req, res, next) => {
    try {
        logger.info('🛡️ [AUTH] Token authentication middleware called', {
            method: req.method,
            url: req.originalUrl
        });
        
        // Get token from header
        let token;
        if (req.headers.authorization && req.headers.authorization.startsWith('Bearer')) {
            token = req.headers.authorization.split(' ')[1];
        }

        if (!token) {
            logger.warn('❌ [AUTH] No token provided');
            return res.status(401).json({
                success: false,
                message: 'Access denied. No token provided.'
            });
        }

        // Verify token
        const jwtSecret = process.env.JWT_SECRET;
        if (!jwtSecret) {
            logger.error('❌ [AUTH] JWT_SECRET not configured');
            return res.status(500).json({
                success: false,
                message: 'Server configuration error'
            });
        }

        const decoded = jwt.verify(token, jwtSecret);
        logger.info('🔍 [AUTH] Token verified for user:', decoded.id);

        // Check if user still exists and is active
        const supabase = getSupabase();
        const { data: user, error } = await supabase
            .from('users')
            .select('id, is_active, lock_until, role')
            .eq('id', decoded.id)
            .single();

        if (error || !user) {
            logger.warn('❌ [AUTH] User not found', { 
                error: error?.message, 
                userId: decoded.id 
            });
            return res.status(401).json({
                success: false,
                message: 'User no longer exists.'
            });
        }

        // Check if user is active
        if (!user.is_active) {
            logger.warn('❌ [AUTH] User account is deactivated', { userId: user.id });
            return res.status(401).json({
                success: false,
                message: 'User account is deactivated.'
            });
        }

        // Check if user is locked
        const isLocked = user.lock_until && new Date(user.lock_until) > new Date();
        if (isLocked) {
            logger.warn('❌ [AUTH] User account is locked', { 
                userId: user.id, 
                lockUntil: user.lock_until 
            });
            return res.status(423).json({
                success: false,
                message: 'Account is temporarily locked.'
            });
        }

        // Grant access
        logger.info('✅ [AUTH] Access granted to user:', user.id);
        req.user = user;
        req.token = token;
        next();

    } catch (error) {
        if (error.name === 'JsonWebTokenError') {
            logger.warn('❌ [AUTH] Invalid token:', error.message);
            return res.status(401).json({
                success: false,
                message: 'Invalid token.'
            });
        }
        
        if (error.name === 'TokenExpiredError') {
            logger.warn('❌ [AUTH] Token expired:', error.message);
            return res.status(401).json({
                success: false,
                message: 'Token expired.'
            });
        }

        logger.error('❌ [AUTH] Authentication error:', error);
        return res.status(500).json({
            success: false,
            message: 'Authentication failed.'
        });
    }
};

/**
 * Middleware to require specific roles
 * @param {Array} allowedRoles - Array of allowed roles
 * @returns {Function} Express middleware function
 */
const requireRole = (allowedRoles) => {
    return (req, res, next) => {
        try {
            logger.info('🔐 [AUTH] Role check middleware called', {
                userId: req.user?.id,
                userRole: req.user?.role,
                allowedRoles
            });

            if (!req.user) {
                logger.warn('❌ [AUTH] No authenticated user for role check');
                return res.status(401).json({
                    success: false,
                    message: 'Authentication required for role check.'
                });
            }

            const userRole = req.user.role || 'user'; // Default role is 'user'
            
            if (!allowedRoles.includes(userRole)) {
                logger.warn('❌ [AUTH] Insufficient permissions', {
                    userId: req.user.id,
                    userRole,
                    allowedRoles
                });
                return res.status(403).json({
                    success: false,
                    message: 'Insufficient permissions.'
                });
            }

            logger.info('✅ [AUTH] Role check passed', {
                userId: req.user.id,
                userRole
            });
            next();

        } catch (error) {
            logger.error('❌ [AUTH] Role check error:', error);
            return res.status(500).json({
                success: false,
                message: 'Authorization failed.'
            });
        }
    };
};

/**
 * Middleware to check if user has admin role
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 * @param {Function} next - Express next function
 */
const requireAdmin = requireRole(['admin']);

/**
 * Middleware to check if user has AI system role
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 * @param {Function} next - Express next function
 */
const requireAISystem = requireRole(['admin', 'ai_system']);

module.exports = {
    authenticateToken,
    requireRole,
    requireAdmin,
    requireAISystem
};