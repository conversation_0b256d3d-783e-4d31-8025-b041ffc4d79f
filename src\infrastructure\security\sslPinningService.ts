import { Platform } from 'react-native';
import CryptoJS from 'crypto-js';
import { ENV_CONFIG, SSL_PINS } from '../config/environment';
import { logger } from '../monitoring/productionLogger';
import { crashReporting } from '../monitoring/crashReportingService';

interface SSLPinningConfig {
  enabled: boolean;
  strictMode: boolean; // If true, fail on any pinning error
  allowSelfSigned: boolean; // For development
  timeout: number;
  retryAttempts: number;
}

interface PinValidationResult {
  isValid: boolean;
  matchedPin?: string;
  error?: string;
  hostname: string;
}

interface CertificateInfo {
  subject: string;
  issuer: string;
  validFrom: string;
  validTo: string;
  fingerprint: string;
  serialNumber: string;
}

class SSLPinningService {
  private config: SSLPinningConfig;
  private pinnedHosts: Map<string, string[]> = new Map();
  private certificateCache: Map<string, CertificateInfo> = new Map();
  private validationCache: Map<string, { result: boolean; timestamp: number }> = new Map();
  private readonly cacheTimeout = 5 * 60 * 1000; // 5 minutes

  constructor() {
    this.config = {
      enabled: ENV_CONFIG.ENABLE_SSL_PINNING,
      strictMode: !__DEV__, // Strict in production, lenient in dev
      allowSelfSigned: __DEV__, // Only allow in development
      timeout: 10000, // 10 seconds
      retryAttempts: 2,
    };

    this.initializePins();
    this.startCacheCleanup();
  }

  private initializePins() {
    try {
      // Load pins from configuration
      Object.entries(SSL_PINS).forEach(([hostname, pins]) => {
        this.pinnedHosts.set(hostname, pins);
      });

      logger.info('SSL pinning initialized', {
        enabled: this.config.enabled,
        hostsCount: this.pinnedHosts.size,
        strictMode: this.config.strictMode,
      }, 'ssl');

      // Add breadcrumb for debugging
      crashReporting.addBreadcrumb({
        category: 'ssl',
        message: 'SSL pinning service initialized',
        level: 'info',
        data: {
          enabled: this.config.enabled,
          hostsCount: this.pinnedHosts.size,
        },
      });
    } catch (error) {
      logger.error('Failed to initialize SSL pinning', error, 'ssl');
      crashReporting.recordNonFatalError('SSL pinning initialization failed', {
        error: error instanceof Error ? error.message : String(error)
      });
    }
  }

  /**
   * Add or update SSL pins for a hostname
   */
  addPins(hostname: string, pins: string[]) {
    try {
      this.pinnedHosts.set(hostname, pins);
      // Clear cache for this hostname
      this.clearCacheForHost(hostname);
      
      logger.info('SSL pins added/updated', { hostname, pinsCount: pins.length }, 'ssl');
    } catch (error) {
      logger.error('Failed to add SSL pins', error, 'ssl');
    }
  }

  /**
   * Remove SSL pins for a hostname
   */
  removePins(hostname: string) {
    try {
      this.pinnedHosts.delete(hostname);
      this.clearCacheForHost(hostname);
      
      logger.info('SSL pins removed', { hostname }, 'ssl');
    } catch (error) {
      logger.error('Failed to remove SSL pins', error, 'ssl');
    }
  }

  /**
   * Validate SSL certificate for a request
   * This is a simplified implementation - in production you'd want to use a native module
   */
  async validateCertificate(url: string): Promise<PinValidationResult> {
    const hostname = this.extractHostname(url);
    
    try {
      // Check if SSL pinning is enabled
      if (!this.config.enabled) {
        return {
          isValid: true,
          hostname,
          error: 'SSL pinning disabled',
        };
      }

      // Check if we have pins for this hostname
      const pins = this.pinnedHosts.get(hostname);
      if (!pins || pins.length === 0) {
        // No pins configured - allow in non-strict mode
        if (!this.config.strictMode) {
          return {
            isValid: true,
            hostname,
            error: 'No pins configured for hostname',
          };
        } else {
          return {
            isValid: false,
            hostname,
            error: 'No SSL pins configured for hostname in strict mode',
          };
        }
      }

      // Check cache first
      const cacheKey = `${hostname}_validation`;
      const cached = this.validationCache.get(cacheKey);
      if (cached && Date.now() - cached.timestamp < this.cacheTimeout) {
        return {
          isValid: cached.result,
          hostname,
          error: cached.result ? undefined : 'Cached validation failure',
        };
      }

      // Perform certificate validation
      const validationResult = await this.performCertificateValidation(hostname, pins);
      
      // Cache the result
      this.validationCache.set(cacheKey, {
        result: validationResult.isValid,
        timestamp: Date.now(),
      });

      // Log validation result
      if (validationResult.isValid) {
        logger.info('SSL certificate validation passed', {
          hostname,
          matchedPin: validationResult.matchedPin,
        }, 'ssl');
      } else {
        logger.warn('SSL certificate validation failed', {
          hostname,
          error: validationResult.error,
        }, 'ssl');
        
        // Record as non-fatal error for monitoring
        crashReporting.recordNonFatalError('SSL certificate validation failed', {
          hostname,
          error: validationResult.error,
          strictMode: this.config.strictMode,
        });
      }

      return validationResult;
    } catch (error) {
      logger.error('SSL certificate validation error', error, 'ssl');
      
      // In non-strict mode, allow the request to proceed
      if (!this.config.strictMode) {
        return {
          isValid: true,
          hostname,
          error: `Validation error (allowed in non-strict mode): ${error instanceof Error ? error.message : String(error)}`,
        };
      }

      return {
        isValid: false,
        hostname,
        error: `Validation error: ${error instanceof Error ? error.message : String(error)}`,
      };
    }
  }

  /**
   * Perform the actual certificate validation
   * Note: This is a simplified implementation. In production, you should use:
   * - react-native-ssl-pinning
   * - react-native-cert-pinner
   * - Or implement native modules for proper certificate chain validation
   */
  private async performCertificateValidation(
    hostname: string,
    pins: string[]
  ): Promise<PinValidationResult> {
    try {
      // For development/testing, we'll simulate certificate validation
      // In production, this should be replaced with actual certificate fetching and validation
      
      if (__DEV__ && this.config.allowSelfSigned) {
        // In development, allow all certificates
        return {
          isValid: true,
          hostname,
          matchedPin: pins[0], // Use first pin as matched
          error: 'Development mode - certificate validation bypassed',
        };
      }

      // Simulate certificate fetching (replace with actual implementation)
      const certificateInfo = await this.fetchCertificateInfo(hostname);
      
      // Validate against pins
      for (const pin of pins) {
        if (this.validatePin(certificateInfo, pin)) {
          return {
            isValid: true,
            hostname,
            matchedPin: pin,
          };
        }
      }

      return {
        isValid: false,
        hostname,
        error: 'Certificate does not match any configured pins',
      };
    } catch (error) {
      return {
        isValid: false,
        hostname,
        error: `Certificate validation failed: ${error instanceof Error ? error.message : String(error)}`,
      };
    }
  }

  /**
   * Simulate certificate fetching
   * In production, replace with actual certificate chain fetching
   */
  private async fetchCertificateInfo(hostname: string): Promise<CertificateInfo> {
    // Check cache first
    const cached = this.certificateCache.get(hostname);
    if (cached) {
      return cached;
    }

    // Simulate certificate info (replace with actual implementation)
    const mockCertInfo: CertificateInfo = {
      subject: `CN=${hostname}`,
      issuer: 'CN=Mock CA',
      validFrom: new Date().toISOString(),
      validTo: new Date(Date.now() + 365 * 24 * 60 * 60 * 1000).toISOString(),
      fingerprint: this.generateMockFingerprint(hostname),
      serialNumber: '123456789',
    };

    // Cache the certificate info
    this.certificateCache.set(hostname, mockCertInfo);
    
    return mockCertInfo;
  }

  /**
   * Validate a certificate against a pin
   */
  private validatePin(certInfo: CertificateInfo, pin: string): boolean {
    try {
      // Extract the hash from the pin (format: sha256/base64hash)
      const [algorithm, hash] = pin.split('/');
      
      if (algorithm !== 'sha256') {
        logger.warn('Unsupported pin algorithm', { algorithm }, 'ssl');
        return false;
      }

      // In a real implementation, you would:
      // 1. Extract the public key from the certificate
      // 2. Calculate SHA256 hash of the public key
      // 3. Compare with the provided hash
      
      // For now, we'll do a simple comparison with the mock fingerprint
      const certHash = CryptoJS.SHA256(certInfo.fingerprint).toString(CryptoJS.enc.Base64);
      return certHash === hash;
    } catch (error) {
      logger.error('Pin validation error', error, 'ssl');
      return false;
    }
  }

  /**
   * Generate a mock fingerprint for testing
   */
  private generateMockFingerprint(hostname: string): string {
    return CryptoJS.SHA256(`mock-cert-${hostname}-${Date.now()}`).toString();
  }

  /**
   * Extract hostname from URL
   */
  private extractHostname(url: string): string {
    try {
      const urlObj = new URL(url);
      return urlObj.hostname;
    } catch (error) {
      // Fallback for invalid URLs
      const match = url.match(/https?:\/\/([^\/]+)/);
      return match ? match[1] : url;
    }
  }

  /**
   * Clear cache for a specific host
   */
  private clearCacheForHost(hostname: string) {
    this.certificateCache.delete(hostname);
    this.validationCache.delete(`${hostname}_validation`);
  }

  /**
   * Start periodic cache cleanup
   */
  private startCacheCleanup() {
    setInterval(() => {
      this.cleanupCache();
    }, 10 * 60 * 1000); // Every 10 minutes
  }

  /**
   * Clean up expired cache entries
   */
  private cleanupCache() {
    try {
      const now = Date.now();
      let cleanedCount = 0;

      // Clean validation cache
      for (const [key, value] of this.validationCache.entries()) {
        if (now - value.timestamp > this.cacheTimeout) {
          this.validationCache.delete(key);
          cleanedCount++;
        }
      }

      // Clean certificate cache (longer timeout)
      // Note: CertificateInfo doesn't have timestamp, so we'll keep them longer
      // In a real implementation, you'd add timestamp to CertificateInfo
      for (const [_, __] of this.certificateCache.entries()) {
        // Certificate cache cleanup would go here if timestamps were available
      }

      if (cleanedCount > 0) {
        logger.info('SSL cache cleanup completed', { cleanedCount }, 'ssl');
      }
    } catch (error) {
      logger.error('SSL cache cleanup failed', error, 'ssl');
    }
  }

  /**
   * Create a fetch wrapper that includes SSL pinning validation
   */
  async createSecureFetch(): Promise<typeof fetch> {
    return async (input: RequestInfo | URL, init?: RequestInit): Promise<Response> => {
      const url = typeof input === 'string' ? input : input.toString();
      
      try {
        // Validate SSL certificate before making the request
        const validation = await this.validateCertificate(url);
        
        if (!validation.isValid && this.config.strictMode) {
          throw new Error(`SSL pinning validation failed: ${validation.error}`);
        }

        // Add SSL validation info to breadcrumbs
        crashReporting.addBreadcrumb({
          category: 'ssl',
          message: `SSL validation for ${validation.hostname}`,
          level: validation.isValid ? 'info' : 'warning',
          data: {
            hostname: validation.hostname,
            isValid: validation.isValid,
            error: validation.error,
          },
        });

        // Proceed with the original fetch
        return fetch(input, init);
      } catch (error) {
        logger.error('Secure fetch failed', error, 'ssl');
        crashReporting.recordNonFatalError('Secure fetch failed', {
          url,
          error: error instanceof Error ? error.message : String(error),
        });
        throw error;
      }
    };
  }

  /**
   * Get SSL pinning status for debugging
   */
  getStatus() {
    return {
      enabled: this.config.enabled,
      strictMode: this.config.strictMode,
      pinnedHostsCount: this.pinnedHosts.size,
      cacheSize: {
        certificates: this.certificateCache.size,
        validations: this.validationCache.size,
      },
      pinnedHosts: Array.from(this.pinnedHosts.keys()),
    };
  }

  /**
   * Update configuration
   */
  updateConfig(newConfig: Partial<SSLPinningConfig>) {
    this.config = { ...this.config, ...newConfig };
    logger.info('SSL pinning config updated', newConfig, 'ssl');
  }

  /**
   * Clear all caches
   */
  clearAllCaches() {
    this.certificateCache.clear();
    this.validationCache.clear();
    logger.info('SSL pinning caches cleared', null, 'ssl');
  }

  /**
   * Test SSL pinning for a specific URL (development only)
   */
  async testPinning(url: string): Promise<PinValidationResult> {
    if (!__DEV__) {
      throw new Error('SSL pinning test is only available in development mode');
    }
    
    return this.validateCertificate(url);
  }
}

// Create singleton instance
export const sslPinning = new SSLPinningService();
export default sslPinning;