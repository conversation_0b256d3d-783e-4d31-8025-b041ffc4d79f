// Centralized Firebase initialization for React Native Firebase
import { getApp } from '@react-native-firebase/app';
import auth from '@react-native-firebase/auth';
import messaging from '@react-native-firebase/messaging';
import crashlytics from '@react-native-firebase/crashlytics';

// No manual initializeApp needed for @react-native-firebase/app if native config files are present
// Use getApp() for modular API

export function getFirebaseAuth() {
  return auth();
}

export function getFirebaseMessaging() {
  return messaging();
}

export function getFirebaseCrashlytics() {
  return crashlytics();
}

export function isFirebaseInitialized() {
  // @react-native-firebase/app auto-initializes if native config is present
  // We can check if the default app exists
  try {
    try {
        getApp();
        return true;
    } catch (e) {
        return false;
    }
  } catch (e) {
    return false;
  }
}

export async function initFirebase() {
  // No-op for @react-native-firebase/app, but kept for compatibility
  // Could be used for future custom logic
  return isFirebaseInitialized();
}
