import React from 'react';
import Svg, { Path } from 'react-native-svg';

interface ThemeIconProps {
  size?: number;
  color?: string;
  isDark?: boolean;
}

const ThemeIcon: React.FC<ThemeIconProps> = ({ 
  size = 24, 
  color = '#000000',
  isDark = false
}) => {
  if (isDark) {
    // Sun icon for light mode
    return (
      <Svg width={size} height={size} viewBox="0 0 24 24" fill="none">
        <Path
          d="M12 7C9.24 7 7 9.24 7 12S9.24 17 12 17S17 14.76 17 12S14.76 7 12 7ZM2 13H4C4.55 13 5 12.55 5 12S4.55 11 4 11H2C1.45 11 1 11.45 1 12S1.45 13 2 13ZM20 13H22C22.55 13 23 12.55 23 12S22.55 11 22 11H20C19.45 11 19 11.45 19 12S19.45 13 20 13ZM11 2V4C11 4.55 11.45 5 12 5S13 4.55 13 4V2C13 1.45 12.55 1 12 1S11 1.45 11 2ZM11 20V22C11 22.55 11.45 23 12 23S13 22.55 13 22V20C13 19.45 12.55 19 12 19S11 19.45 11 20ZM5.99 4.58C5.6 4.19 4.96 4.19 4.58 4.58C4.19 4.96 4.19 5.61 4.58 5.99L5.64 7.05C6.02 7.44 6.67 7.44 7.05 7.05C7.43 6.67 7.44 6.02 7.05 5.64L5.99 4.58ZM18.36 16.95C17.98 16.56 17.33 16.56 16.95 16.95C16.56 17.33 16.56 17.98 16.95 18.36L18.01 19.42C18.39 19.81 19.04 19.81 19.42 19.42C19.81 19.04 19.81 18.39 19.42 18.01L18.36 16.95ZM19.42 5.99C19.81 5.61 19.81 4.96 19.42 4.58C19.04 4.19 18.39 4.19 18.01 4.58L16.95 5.64C16.56 6.02 16.56 6.67 16.95 7.05C17.33 7.43 17.98 7.44 18.36 7.05L19.42 5.99ZM7.05 18.36C7.44 17.98 7.44 17.33 7.05 16.95C6.67 16.56 6.02 16.56 5.64 16.95L4.58 18.01C4.19 18.39 4.19 19.04 4.58 19.42C4.96 19.81 5.61 19.81 5.99 19.42L7.05 18.36Z"
          fill={color}
        />
      </Svg>
    );
  } else {
    // Moon icon for dark mode
    return (
      <Svg width={size} height={size} viewBox="0 0 24 24" fill="none">
        <Path
          d="M12.34 2.02C6.59 1.82 2 6.42 2 12C2 17.52 6.48 22 12 22C17.58 22 22.18 17.41 21.98 11.66C21.91 10.36 21.29 9.2 20.37 8.5C19.59 7.89 18.36 7.75 17.65 8.93C16.35 11.18 14.09 12.67 11.5 12.67C8.18 12.67 5.5 9.99 5.5 6.67C5.5 4.08 6.99 1.82 9.24 0.52C10.42 -0.19 10.28 -1.42 9.67 -2.2C8.97 -3.12 7.81 -3.74 6.51 -3.81C6.17 -3.84 5.83 -3.85 5.5 -3.85C5.17 -3.85 4.83 -3.84 4.49 -3.81C3.19 -3.74 2.03 -3.12 1.33 -2.2C0.72 -1.42 0.58 -0.19 1.76 0.52C4.01 1.82 5.5 4.08 5.5 6.67C5.5 9.99 8.18 12.67 11.5 12.67C14.09 12.67 16.35 11.18 17.65 8.93C18.36 7.75 19.59 7.89 20.37 8.5C21.29 9.2 21.91 10.36 21.98 11.66Z"
          fill={color}
        />
      </Svg>
    );
  }
};

export default ThemeIcon;