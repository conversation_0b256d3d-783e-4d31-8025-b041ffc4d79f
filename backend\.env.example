# =====================================================
# VENDY BACKEND ENVIRONMENT VARIABLES
# =====================================================
# Copy this file to .env and update with your actual values

# =====================================================
# APPLICATION SETTINGS
# =====================================================
NODE_ENV=development
PORT=3000
API_VERSION=v1

# =====================================================
# SUPABASE DATABASE CONFIGURATION
# =====================================================
# Get these from your Supabase project dashboard
SUPABASE_URL=https://your-project-ref.supabase.co
SUPABASE_SERVICE_ROLE_KEY=your-supabase-service-role-key-here

# =====================================================
# JWT AUTHENTICATION
# =====================================================
# Generate strong random secrets for production
JWT_SECRET=your-super-secure-jwt-secret-key-here-minimum-32-characters
JWT_REFRESH_SECRET=your-super-secure-refresh-secret-key-here-minimum-32-characters
JWT_EXPIRES_IN=15m
JWT_REFRESH_EXPIRES_IN=7d

# =====================================================
# BREVO EMAIL CONFIGURATION
# =====================================================
# Get your API key from Brevo dashboard
BREVO_API_KEY=your-brevo-api-key-here
BREVO_FROM_EMAIL=<EMAIL>
BREVO_FROM_NAME=Vendy

# Email rate limiting
MAX_EMAIL_OTP_PER_5MIN=3

# =====================================================
# TERMII SMS CONFIGURATION
# =====================================================
# Get your API key from Termii dashboard
TERMII_API_KEY=your-termii-api-key-here
TERMII_SENDER_ID=Vendy
TERMII_BASE_URL=https://v3.api.termii.com

# SMS rate limiting
MAX_SMS_OTP_PER_5MIN=3

# =====================================================
# SECURITY SETTINGS
# =====================================================
# Password hashing rounds (higher = more secure but slower)
BCRYPT_ROUNDS=12

# Account lockout settings
MAX_LOGIN_ATTEMPTS=5
LOCKOUT_TIME=900000

# OTP expiry time in milliseconds (5 minutes = 300000)
OTP_EXPIRY_TIME=300000

# =====================================================
# RATE LIMITING
# =====================================================
# General API rate limiting
RATE_LIMIT_WINDOW_MS=900000
RATE_LIMIT_MAX_REQUESTS=100

# =====================================================
# LOGGING
# =====================================================
# Log level: error, warn, info, debug
LOG_LEVEL=info

# =====================================================
# CORS SETTINGS
# =====================================================
# Allowed origins for CORS (comma-separated)
CORS_ORIGINS=http://localhost:3000,http://localhost:8081

# =====================================================
# PRODUCTION SETTINGS
# =====================================================
# Set to true in production for enhanced security
TRUST_PROXY=false

# Database connection pool settings
DB_POOL_MIN=2
DB_POOL_MAX=10

# =====================================================
# OPTIONAL FEATURES
# =====================================================
# Enable/disable features
ENABLE_SWAGGER_DOCS=true
ENABLE_REQUEST_LOGGING=true
ENABLE_PERFORMANCE_MONITORING=false

# =====================================================
# WEBHOOK SETTINGS (Optional)
# =====================================================
# SendGrid webhook endpoint verification
SENDGRID_WEBHOOK_SECRET=your-webhook-secret-here

# Termii webhook settings
TERMII_WEBHOOK_SECRET=your-termii-webhook-secret-here

# =====================================================
# MONITORING & ANALYTICS (Optional)
# =====================================================
# Application monitoring
SENTRY_DSN=your-sentry-dsn-here

# Analytics
GOOGLE_ANALYTICS_ID=your-ga-id-here

# =====================================================
# BACKUP & MAINTENANCE (Optional)
# =====================================================
# Database backup settings
BACKUP_ENABLED=false
BACKUP_SCHEDULE=0 2 * * *
BACKUP_RETENTION_DAYS=30

# =====================================================
# DEVELOPMENT ONLY
# =====================================================
# These should not be used in production
DEBUG_MODE=false
MOCK_SMS=false
MOCK_EMAIL=false
