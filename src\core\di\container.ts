/**
 * Dependency Injection Container
 * 
 * This container manages all service dependencies across the application,
 * providing a centralized way to register, resolve, and manage service lifecycles.
 */

import { logger } from '../../infrastructure/monitoring/productionLogger';

type ServiceFactory<T = any> = () => T | Promise<T>;
type ServiceInstance<T = any> = T;

interface ServiceDefinition<T = any> {
  factory: ServiceFactory<T>;
  singleton: boolean;
  instance?: ServiceInstance<T>;
  dependencies: string[];
  initialized: boolean;
}

interface ContainerConfig {
  enableLogging: boolean;
  enableCircularDependencyDetection: boolean;
  maxResolutionDepth: number;
}

class DIContainer {
  private services = new Map<string, ServiceDefinition>();
  private resolutionStack: string[] = [];
  private config: ContainerConfig = {
    enableLogging: __DEV__,
    enableCircularDependencyDetection: true,
    maxResolutionDepth: 50
  };

  /**
   * Register a service with the container
   */
  register<T>(
    name: string,
    factory: ServiceFactory<T>,
    options: {
      singleton?: boolean;
      dependencies?: string[];
    } = {}
  ): void {
    const { singleton = true, dependencies = [] } = options;

    if (this.services.has(name)) {
      logger.warn(`Service '${name}' is already registered. Overwriting.`, null, 'di');
    }

    this.services.set(name, {
      factory,
      singleton,
      dependencies,
      initialized: false
    });

    if (this.config.enableLogging) {
      logger.debug(`Service '${name}' registered`, {
        singleton,
        dependencies: dependencies.length
      }, 'di');
    }
  }

  /**
   * Register a singleton service
   */
  registerSingleton<T>(
    name: string,
    factory: ServiceFactory<T>,
    dependencies: string[] = []
  ): void {
    this.register(name, factory, { singleton: true, dependencies });
  }

  /**
   * Register a transient service (new instance each time)
   */
  registerTransient<T>(
    name: string,
    factory: ServiceFactory<T>,
    dependencies: string[] = []
  ): void {
    this.register(name, factory, { singleton: false, dependencies });
  }

  /**
   * Register an existing instance as a singleton
   */
  registerInstance<T>(name: string, instance: T): void {
    this.services.set(name, {
      factory: () => instance,
      singleton: true,
      instance,
      dependencies: [],
      initialized: true
    });

    if (this.config.enableLogging) {
      logger.debug(`Instance '${name}' registered`, null, 'di');
    }
  }

  /**
   * Resolve a service by name
   */
  async resolve<T>(name: string): Promise<T> {
    if (this.resolutionStack.length > this.config.maxResolutionDepth) {
      throw new Error(`Maximum resolution depth exceeded. Possible circular dependency involving: ${this.resolutionStack.join(' -> ')}`);
    }

    if (this.config.enableCircularDependencyDetection && this.resolutionStack.includes(name)) {
      throw new Error(`Circular dependency detected: ${this.resolutionStack.join(' -> ')} -> ${name}`);
    }

    const serviceDefinition = this.services.get(name);
    if (!serviceDefinition) {
      throw new Error(`Service '${name}' is not registered`);
    }

    // Return existing singleton instance if available
    if (serviceDefinition.singleton && serviceDefinition.instance) {
      return serviceDefinition.instance as T;
    }

    this.resolutionStack.push(name);

    try {
      // Resolve dependencies first
      const resolvedDependencies: any[] = [];
      for (const dependency of serviceDefinition.dependencies) {
        const resolvedDependency = await this.resolve(dependency);
        resolvedDependencies.push(resolvedDependency);
      }

      // Create the service instance
      const instance = await serviceDefinition.factory();

      // Inject dependencies if the instance has an inject method
      if (instance && typeof (instance as any).inject === 'function') {
        await (instance as any).inject(...resolvedDependencies);
      }

      // Store singleton instance
      if (serviceDefinition.singleton) {
        serviceDefinition.instance = instance;
        serviceDefinition.initialized = true;
      }

      if (this.config.enableLogging) {
        logger.debug(`Service '${name}' resolved`, {
          singleton: serviceDefinition.singleton,
          dependenciesCount: serviceDefinition.dependencies.length
        }, 'di');
      }

      return instance as T;
    } finally {
      this.resolutionStack.pop();
    }
  }

  /**
   * Resolve multiple services at once
   */
  async resolveAll<T extends Record<string, any>>(names: (keyof T)[]): Promise<T> {
    const resolved = {} as T;
    
    for (const name of names) {
      resolved[name] = await this.resolve(name as string);
    }

    return resolved;
  }

  /**
   * Check if a service is registered
   */
  isRegistered(name: string): boolean {
    return this.services.has(name);
  }

  /**
   * Unregister a service
   */
  unregister(name: string): void {
    const serviceDefinition = this.services.get(name);
    if (serviceDefinition) {
      // Call cleanup if available
      if (serviceDefinition.instance && typeof (serviceDefinition.instance as any).cleanup === 'function') {
        (serviceDefinition.instance as any).cleanup();
      }
      
      this.services.delete(name);
      
      if (this.config.enableLogging) {
        logger.debug(`Service '${name}' unregistered`, null, 'di');
      }
    }
  }

  /**
   * Clear all services
   */
  clear(): void {
    // Cleanup all singleton instances
    for (const [name, definition] of this.services.entries()) {
      if (definition.instance && typeof (definition.instance as any).cleanup === 'function') {
        try {
          (definition.instance as any).cleanup();
        } catch (error) {
          logger.warn(`Failed to cleanup service '${name}'`, error, 'di');
        }
      }
    }

    this.services.clear();
    this.resolutionStack = [];

    if (this.config.enableLogging) {
      logger.debug('All services cleared', null, 'di');
    }
  }

  /**
   * Get all registered service names
   */
  getRegisteredServices(): string[] {
    return Array.from(this.services.keys());
  }

  /**
   * Get service definition (for debugging)
   */
  getServiceDefinition(name: string): ServiceDefinition | undefined {
    return this.services.get(name);
  }

  /**
   * Initialize all singleton services
   */
  async initializeAll(): Promise<void> {
    const singletonServices = Array.from(this.services.entries())
      .filter(([, definition]) => definition.singleton && !definition.initialized)
      .map(([name]) => name);

    if (this.config.enableLogging) {
      logger.info(`Initializing ${singletonServices.length} singleton services`, {
        services: singletonServices
      }, 'di');
    }

    for (const serviceName of singletonServices) {
      try {
        await this.resolve(serviceName);
      } catch (error) {
        logger.error(`Failed to initialize service '${serviceName}'`, error, 'di');
        throw error;
      }
    }

    if (this.config.enableLogging) {
      logger.info('All singleton services initialized', null, 'di');
    }
  }

  /**
   * Get container statistics
   */
  getStats(): {
    totalServices: number;
    singletonServices: number;
    transientServices: number;
    initializedServices: number;
  } {
    const definitions = Array.from(this.services.values());
    
    return {
      totalServices: definitions.length,
      singletonServices: definitions.filter(d => d.singleton).length,
      transientServices: definitions.filter(d => !d.singleton).length,
      initializedServices: definitions.filter(d => d.initialized).length
    };
  }

  /**
   * Configure the container
   */
  configure(config: Partial<ContainerConfig>): void {
    this.config = { ...this.config, ...config };
    
    if (this.config.enableLogging) {
      logger.debug('Container configured', this.config, 'di');
    }
  }

  /**
   * Create a scoped container (child container)
   */
  createScope(): DIContainer {
    const scopedContainer = new DIContainer();
    scopedContainer.config = { ...this.config };
    
    // Copy all service definitions to the scoped container
    for (const [name, definition] of this.services.entries()) {
      scopedContainer.services.set(name, {
        ...definition,
        instance: undefined, // Don't copy instances
        initialized: false
      });
    }

    if (this.config.enableLogging) {
      logger.debug('Scoped container created', {
        parentServices: this.services.size
      }, 'di');
    }

    return scopedContainer;
  }

  /**
   * Validate container configuration
   */
  validate(): { isValid: boolean; errors: string[] } {
    const errors: string[] = [];
    
    // Check for missing dependencies
    for (const [serviceName, definition] of this.services.entries()) {
      for (const dependency of definition.dependencies) {
        if (!this.services.has(dependency)) {
          errors.push(`Service '${serviceName}' depends on unregistered service '${dependency}'`);
        }
      }
    }

    // Check for circular dependencies
    for (const serviceName of this.services.keys()) {
      try {
        this.detectCircularDependencies(serviceName, new Set());
      } catch (error) {
        errors.push(error.message);
      }
    }

    return {
      isValid: errors.length === 0,
      errors
    };
  }

  /**
   * Detect circular dependencies
   */
  private detectCircularDependencies(serviceName: string, visited: Set<string>): void {
    if (visited.has(serviceName)) {
      throw new Error(`Circular dependency detected involving service '${serviceName}'`);
    }

    const definition = this.services.get(serviceName);
    if (!definition) {
      return;
    }

    visited.add(serviceName);

    for (const dependency of definition.dependencies) {
      this.detectCircularDependencies(dependency, new Set(visited));
    }
  }
}

// Create and export the global container instance
export const container = new DIContainer();

// Export the container class for creating scoped containers
export { DIContainer };

// Export types
export type { ServiceFactory, ServiceInstance, ServiceDefinition, ContainerConfig };

// Service registration helper
export const registerServices = async () => {
  // Core services
  container.registerSingleton('logger', () => import('../../infrastructure/monitoring/productionLogger').then(m => m.logger));
  container.registerSingleton('keyManagement', () => import('../../infrastructure/security/keyManagementService').then(m => m.default));
  container.registerSingleton('secureStorage', () => import('../../infrastructure/security/secureStorageService').then(m => m.default));
  container.registerSingleton('inputValidation', () => import('../../infrastructure/monitoring/inputValidationService').then(m => m.default));
  container.registerSingleton('runtimeSecurity', () => import('../../infrastructure/security/runtimeSecurityService').then(m => m.default));

  // API and network services
  container.registerSingleton('apiService', () => import('../../infrastructure/api/apiService').then(m => m.default), ['logger', 'secureStorage']);
  container.registerSingleton('sslPinning', () => import('../../infrastructure/security/sslPinningService').then(m => m.default), ['logger']);

  // Performance and monitoring
  container.registerSingleton('performanceService', () => import('../../infrastructure/monitoring/performanceService').then(m => m.default), ['logger']);
  container.registerSingleton('crashReporting', () => import('../../infrastructure/monitoring/crashReportingService').then(m => m.default), ['logger']);

  // Navigation
  container.registerSingleton('navigationHandler', () => import('../../navigation/handlers/navigationHandler').then(m => m.navigationHandler), ['logger']);

  // User services
  container.registerSingleton('userService', () => import('../../features/profile/services/userService').then(m => m.userService), ['apiService', 'secureStorage']);

  // Initialize all services
  await container.initializeAll();
};
