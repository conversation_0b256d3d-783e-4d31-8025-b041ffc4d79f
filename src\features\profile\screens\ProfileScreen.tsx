import React, { useEffect, useState } from 'react';
import { View, Text, StyleSheet, Image, TouchableOpacity, ScrollView, Switch } from 'react-native';
import { useTheme } from '../../../shared/components/layout/ThemeContext';
import { useNavigation, NavigationProp } from '@react-navigation/native';
import { useProfile } from '../components/ProfileContext';
import secureStorage from '../../../infrastructure/security/secureStorageService';
import SettingsScreen from '../../settings/screens/SettingsScreen';
import SecurityScreen from '../../settings/screens/SecurityScreen';
import AppearanceScreen from '../../settings/screens/AppearanceScreen';
import ReferEarnScreen from '../../rewards/screens/ReferEarnScreen';
import type { RootStackParamList } from '../../../navigation/navigation';
import UserIcon from '../../../shared/components/icons/UserIcon';
import ArrowRightIcon from '../../../shared/components/icons/ArrowRightIcon';
import FingerprintIcon from '../../../shared/components/icons/FingerprintIcon';
import SettingsIcon from '../../../shared/components/icons/SettingsIcon';
import ThemeIcon from '../../../shared/components/icons/ThemeIcon';
import LockIcon from '../../../shared/components/icons/LockIcon';
import LogoutIcon from '../../../shared/components/icons/LogoutIcon';
import GiftIcon from '../../../shared/components/icons/GiftIcon';
import TierIcon from '../../../shared/components/icons/TierIcon';
import { navigationHandler } from '../../../navigation/handlers/navigationHandler';
import { logger } from '../../../infrastructure/monitoring/productionLogger';

const APP_VERSION = 'v4.2.2';

const ProfileScreen = () => {
  const { theme, isDark } = useTheme();
  const navigation = useNavigation<NavigationProp<RootStackParamList>>();
  const { profile: user, loading, refreshProfile } = useProfile();
  const [biometrics, setBiometrics] = useState(true); // Example toggle

  useEffect(() => {
    // Load biometric preference from secure storage (no prompt)
    let isMounted = true;
    const loadBiometrics = async () => {
      const enabled = await secureStorage.getBiometricToggle();
      if (isMounted) setBiometrics(enabled);
    };
    loadBiometrics();
    return () => { isMounted = false; };
  }, []);

  // Save biometric preference to secure storage without biometric prompt
  const handleBiometricToggle = async (value: boolean) => {
    setBiometrics(value);
    try {
      await secureStorage.setBiometricToggle(value);
    } catch (e) {
logger.error('Failed to save biometric preference', e, { category: 'biometric' });
    }
  };

  return (
    <ScrollView style={{ flex: 1, backgroundColor: theme.colors.background }} contentContainerStyle={{ alignItems: 'center', padding: 0 }}>
      {/* Top Row */}
      <View style={styles.topRow}>
        <TouchableOpacity style={[styles.tierButton, { backgroundColor: theme.colors.card, flexDirection: 'row', alignItems: 'center' }]}> 
          <TierIcon size={20} color={theme.colors.primary} />
          <Text style={[styles.tierText, { color: theme.colors.primary, marginLeft: 6 }]}>Tier 1</Text>
        </TouchableOpacity>
      </View>
      {/* Avatar */}
      <View style={loading ? styles.avatarSkeleton : styles.avatarContainer}>
        {loading ? null : (
          user?.picture || user?.avatar ? (
            <Image source={{ uri: user?.picture || user?.avatar }} style={styles.avatar} />
          ) : null
        )}
      </View>
      {/* Name & Email */}
      <Text style={[styles.name, { color: theme.colors.text }]}> 
        {loading ? <View style={styles.textSkeleton} /> : `${user?.firstName ?? ''} ${user?.lastName ?? ''}`.trim()}
      </Text>
      <Text style={[styles.email, { color: theme.colors.muted }]}> 
        {loading ? <View style={styles.textSkeleton} /> : user?.email}
      </Text>
      {/* Info Cards */}
      <View style={styles.infoRow}>
        <View style={[styles.infoCard, { backgroundColor: theme.colors.card }]}> 
          <Text style={[styles.infoLabel, { color: theme.colors.muted }]}>Version</Text>
          <Text style={[styles.infoValue, { color: theme.colors.text }]}>{APP_VERSION}</Text>
        </View>
        <View style={[styles.infoCard, { backgroundColor: theme.colors.card }]}> 
          <View style={{ flexDirection: 'row', alignItems: 'center', marginBottom: 4 }}>
            <FingerprintIcon size={18} color={theme.colors.primary} />
            <Text style={[styles.infoLabel, { color: theme.colors.muted, marginLeft: 6 }]}>Biometrics</Text>
          </View>
          <View style={{ flexDirection: 'row', alignItems: 'center' }}>
            <Text style={[styles.infoValue, { color: theme.colors.text, marginRight: 8 }]}>{biometrics ? 'On' : 'Off'}</Text>
            <Switch value={biometrics} onValueChange={handleBiometricToggle} thumbColor={biometrics ? theme.colors.primary : theme.colors.muted} />
          </View>
        </View>
      </View>
      {/* Action List */}
      <View style={styles.actionList}>
        <TouchableOpacity style={[styles.actionItem, { backgroundColor: theme.colors.card }]} onPress={() => navigationHandler.navigateToReferEarn()}>
          <View style={styles.actionIcon}><GiftIcon size={20} color={theme.colors.primary} /></View>
          <View style={styles.actionTextWrap}>
            <Text style={[styles.actionTitle, { color: theme.colors.text }]}>Refer & Earn</Text>
            <Text style={[styles.actionSubtitle, { color: theme.colors.muted }]}>Make some extra cash</Text>
          </View>
          <ArrowRightIcon size={18} color={theme.colors.muted} />
        </TouchableOpacity>
        <TouchableOpacity style={[styles.actionItem, { backgroundColor: theme.colors.card }]} onPress={() => navigationHandler.navigateToSettings()}>
          <View style={styles.actionIcon}><SettingsIcon size={20} color={theme.colors.primary} /></View>
          <View style={styles.actionTextWrap}>
            <Text style={[styles.actionTitle, { color: theme.colors.text }]}>Settings</Text>
            <Text style={[styles.actionSubtitle, { color: theme.colors.muted }]}>Manage & modify your account</Text>
          </View>
          <ArrowRightIcon size={18} color={theme.colors.muted} />
        </TouchableOpacity>
        <TouchableOpacity style={[styles.actionItem, { backgroundColor: theme.colors.card }]} onPress={() => navigationHandler.navigateToSecurity()}>
          <View style={styles.actionIcon}><LockIcon size={20} color={theme.colors.primary} /></View>
          <View style={styles.actionTextWrap}>
            <Text style={[styles.actionTitle, { color: theme.colors.text }]}>Security & Privacy</Text>
            <Text style={[styles.actionSubtitle, { color: theme.colors.muted }]}>Change passcode & transaction pin</Text>
          </View>
          <ArrowRightIcon size={18} color={theme.colors.muted} />
        </TouchableOpacity>
        {/* Appearance */}
        <TouchableOpacity style={[styles.actionItem, { backgroundColor: theme.colors.card }]} onPress={() => navigationHandler.navigateToAppearance()}>
          <View style={styles.actionIcon}><ThemeIcon size={20} color={theme.colors.primary} /></View>
          <View style={styles.actionTextWrap}>
            <Text style={[styles.actionTitle, { color: theme.colors.text }]}>Appearance</Text>
            <Text style={[styles.actionSubtitle, { color: theme.colors.muted }]}>Theme & display options</Text>
          </View>
          <ArrowRightIcon size={18} color={theme.colors.muted} />
        </TouchableOpacity>
        {/* Logout */}
        <TouchableOpacity style={[styles.actionItem, { backgroundColor: theme.colors.card, marginTop: 16, borderColor: theme.colors.error, borderWidth: 1 }]} onPress={async () => {
          // Clear user session and navigate to PinVerification
          // Optionally clear user data, tokens, etc. here
          navigationHandler.resetToScreen('PinVerification', {});
        }}>
          <View style={styles.actionIcon}><LogoutIcon size={20} color={theme.colors.error} /></View>
          <View style={styles.actionTextWrap}>
            <Text style={[styles.actionTitle, { color: theme.colors.error }]}>Logout</Text>
          </View>
        </TouchableOpacity>
      </View>
    </ScrollView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#fff',
    alignItems: 'center',
    paddingTop: 0,
  },
  topRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    width: '100%',
    paddingHorizontal: 20,
    marginTop: 24,
    marginBottom: 12,
  },
  tierButton: {
    borderRadius: 16,
    paddingVertical: 6,
    paddingHorizontal: 16,
    alignItems: 'center',
  },
  tierText: {
    fontSize: 13,
    fontWeight: '600',
  },
  editButton: {
    borderRadius: 16,
    paddingVertical: 6,
    paddingHorizontal: 16,
    alignItems: 'center',
  },
  editText: {
    fontSize: 13,
    fontWeight: '600',
  },
  avatarContainer: {
    width: 100,
    height: 100,
    borderRadius: 50,
    backgroundColor: '#eee',
    alignItems: 'center',
    justifyContent: 'center',
    marginBottom: 12,
  },
  avatar: {
    width: 80,
    height: 80,
    borderRadius: 40,
  },
  name: {
    fontSize: 20,
    fontWeight: '700',
    marginBottom: 4,
  },
  email: {
    fontSize: 15,
    color: '#888',
    marginBottom: 12,
  },
  infoRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    width: '100%',
    paddingHorizontal: 20,
    marginBottom: 16,
  },
  infoCard: {
    flex: 1,
    backgroundColor: '#f6f6f6',
    borderRadius: 16,
    padding: 16,
    marginHorizontal: 6,
    alignItems: 'center',
  },
  infoLabel: {
    fontSize: 13,
    color: '#888',
    marginBottom: 2,
  },
  infoValue: {
    fontSize: 15,
    fontWeight: '600',
  },
  avatarSkeleton: {
    width: 80,
    height: 80,
    borderRadius: 40,
    backgroundColor: '#e0e0e0',
    marginBottom: 12,
  },
  textSkeleton: {
    width: 120,
    height: 18,
    borderRadius: 6,
    backgroundColor: '#e0e0e0',
    marginBottom: 8,
    alignSelf: 'center',
  },
  actionList: {
    width: '100%',
    marginTop: 8,
    paddingHorizontal: 0,
  },
  actionItem: {
    flexDirection: 'row',
    alignItems: 'center',
    borderRadius: 16,
    paddingVertical: 18,
    paddingHorizontal: 18,
    marginHorizontal: 16,
    marginBottom: 10,
  },
  actionIcon: {
    marginRight: 16,
  },
  actionTextWrap: {
    flex: 1,
  },
  actionTitle: {
    fontSize: 16,
    fontWeight: '600',
  },
  actionSubtitle: {
    fontSize: 13,
    marginTop: 2,
  },
});

export default ProfileScreen;