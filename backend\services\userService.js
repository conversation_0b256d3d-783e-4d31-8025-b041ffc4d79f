const bcrypt = require('bcryptjs');
const crypto = require('crypto');
const { getSupabase } = require('../config/database');
const logger = require('../utils/logger');

class UserService {
  constructor() {
    this.supabase = null;
    // Simple in-memory cache for user data (5 minute TTL)
    this.userCache = new Map();
    this.cacheTimeout = 5 * 60 * 1000; // 5 minutes
    
    // Clean cache every 10 minutes
    setInterval(() => {
      this.cleanExpiredCache();
    }, 10 * 60 * 1000);
  }

  getClient() {
    if (!this.supabase) {
      this.supabase = getSupabase();
    }
    return this.supabase;
  }

  /**
   * Clean expired cache entries
   */
  cleanExpiredCache() {
    const now = Date.now();
    for (const [key, entry] of this.userCache.entries()) {
      if (now > entry.expiresAt) {
        this.userCache.delete(key);
      }
    }
  }

  /**
   * Get user from cache
   */
  getUserFromCache(userId) {
    const entry = this.userCache.get(userId);
    if (!entry) return null;
    
    if (Date.now() > entry.expiresAt) {
      this.userCache.delete(userId);
      return null;
    }
    
    return entry.data;
  }

  /**
   * Set user in cache
   */
  setUserInCache(userId, userData) {
    // Limit cache size to prevent memory issues
    if (this.userCache.size >= 1000) {
      const firstKey = this.userCache.keys().next().value;
      if (firstKey) {
        this.userCache.delete(firstKey);
      }
    }
    
    this.userCache.set(userId, {
      data: userData,
      expiresAt: Date.now() + this.cacheTimeout
    });
  }

  /**
   * Clear user from cache
   */
  clearUserFromCache(userId) {
    this.userCache.delete(userId);
  }

  /**
   * Create a new user
   * @param {Object} userData - User data
   * @returns {Promise<Object>} - Created user
   */
  async createUser(userData) {
    try {
      const supabase = this.getClient();

      const { data, error } = await supabase
        .from('users')
        .insert([{
          phone_number: userData.phoneNumber,
          pin: null, // New users have no PIN initially
          is_phone_verified: false,
          balance: 0,
          is_active: true,
          role: 'user',
          login_attempts: 0,
          created_at: new Date().toISOString(),
          updated_at: new Date().toISOString()
        }])
        .select()
        .single();

      if (error) {
        throw error;
      }

      return this.formatUser(data);
    } catch (error) {
      logger.error('Create user error:', error);
      throw error;
    }
  }

  /**
   * Find user by phone number
   * @param {string} phoneNumber - Phone number
   * @returns {Promise<Object|null>} - User or null
   */
  async findByPhoneNumber(phoneNumber) {
    try {
      const supabase = this.getClient();

      const { data, error } = await supabase
        .from('users')
        .select('*')
        .eq('phone_number', phoneNumber)
        .eq('is_active', true)
        .single();

      if (error && error.code !== 'PGRST116') {
        throw error;
      }

      return data ? this.formatUser(data) : null;
    } catch (error) {
      logger.error('Find user by phone error:', error);
      return null;
    }
  }

  /**
   * Find user by email
   * @param {string} email - Email address
   * @returns {Promise<Object|null>} - User or null
   */
  async findByEmail(email) {
    try {
      const supabase = this.getClient();

      const { data, error } = await supabase
        .from('users')
        .select('*')
        .eq('email', email)
        .eq('is_active', true)
        .single();

      if (error && error.code !== 'PGRST116') {
        throw error;
      }

      return data ? this.formatUser(data) : null;
    } catch (error) {
      logger.error('Find user by email error:', error);
      return null;
    }
  }

  /**
   * Find user by Google UID
   * @param {string} googleUid - Google UID
   * @returns {Promise<Object|null>} - User or null
   */
  async findByGoogleUid(googleUid) {
    try {
      const supabase = this.getClient();

      const { data, error } = await supabase
        .from('users')
        .select('*')
        .eq('google_uid', googleUid)
        .eq('is_active', true)
        .single();

      if (error && error.code !== 'PGRST116') {
        throw error;
      }

      return data ? this.formatUser(data) : null;
    } catch (error) {
      logger.error('Find user by Google UID error:', error);
      return null;
    }
  }

  /**
   * Create user with email (for email verification flow)
   * @param {string} email - Email address
   * @returns {Promise<Object>} - Created user
   */
  async createUserWithEmail(email) {
    try {
      const supabase = this.getClient();

      console.log('👤 [USER-SERVICE] Creating user with email:', email);

      console.log('🔐 [USER-SERVICE] Creating email user without PIN');

      const userData = {
        email: email,
        phone_number: null, // Explicitly set to null for email-only users
        pin: null, // New users have no PIN initially
        is_phone_verified: false,
        is_email_verified: false,
        balance: 0,
        is_active: true,
        role: 'user',
        login_attempts: 0,
        ip_addresses: [],
        devices: [],
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString()
      };

      console.log('📝 [USER-SERVICE] User data prepared:', {
        email: userData.email,
        phone_number: userData.phone_number,
        role: userData.role,
        is_active: userData.is_active
      });

      const { data, error } = await supabase
        .from('users')
        .insert([userData])
        .select()
        .single();

      if (error) {
        console.log('❌ [USER-SERVICE] Database error:', error);
        throw error;
      }

      console.log('✅ [USER-SERVICE] User created successfully:', data.id);
      return this.formatUser(data);
    } catch (error) {
      console.log('💥 [USER-SERVICE] Create user with email error:', error);
      logger.error('Create user with email error:', error);
      throw error;
    }
  }

  /**
   * Create user with Google authentication
   * @param {Object} googleData - Google user data
   * @returns {Promise<Object>} - Created user
   */
  async createUserWithGoogle(googleData) {
    try {
      const supabase = this.getClient();

      console.log('👤 [USER-SERVICE] Creating user with Google:', googleData.email);

      console.log('🔐 [USER-SERVICE] Creating Google user without PIN');

      // Parse name into first and last name
      const nameParts = googleData.name ? googleData.name.split(' ') : [];
      const firstName = nameParts[0] || '';
      const lastName = nameParts.slice(1).join(' ') || '';

      const userData = {
        email: googleData.email,
        first_name: firstName,
        last_name: lastName,
        picture: googleData.picture,
        phone_number: null,
        pin: null, // New users have no PIN initially
        is_phone_verified: false,
        is_email_verified: googleData.emailVerified || false,
        google_uid: googleData.uid,
        provider_id: googleData.providerId,
        auth_provider: 'google',
        profile_source: 'google', // Track that profile data came from Google
        balance: 0,
        is_active: true,
        role: 'user',
        login_attempts: 0,
        ip_addresses: [],
        devices: [],
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString()
      };

      console.log('📝 [USER-SERVICE] Google user data prepared:', {
        email: userData.email,
        first_name: userData.first_name,
        last_name: userData.last_name,
        google_uid: userData.google_uid,
        is_email_verified: userData.is_email_verified
      });

      const { data, error } = await supabase
        .from('users')
        .insert([userData])
        .select()
        .single();

      if (error) {
        console.log('❌ [USER-SERVICE] Database error:', error);
        throw error;
      }

      console.log('✅ [USER-SERVICE] Google user created successfully:', data.id);
      return this.formatUser(data);
    } catch (error) {
      console.log('💥 [USER-SERVICE] Create user with Google error:', error);
      logger.error('Create user with Google error:', error);
      throw error;
    }
  }

  /**
   * Find user by ID
   * @param {string} userId - User ID
   * @returns {Promise<Object|null>} - User or null
   */
  async findById(userId) {
    try {
      // Check cache first
      const cachedUser = this.getUserFromCache(userId);
      if (cachedUser) {
        console.log(`💾 [USER-SERVICE] Cache hit for user ${userId}`);
        return cachedUser;
      }

      console.log(`🔍 [USER-SERVICE] Cache miss for user ${userId}, fetching from database`);
      const supabase = this.getClient();

      const { data, error } = await supabase
        .from('users')
        .select('*')
        .eq('id', userId)
        .eq('is_active', true)
        .single();

      if (error && error.code !== 'PGRST116') {
        throw error;
      }

      const user = data ? this.formatUser(data) : null;
      
      // Cache the result if user found
      if (user) {
        this.setUserInCache(userId, user);
        console.log(`💾 [USER-SERVICE] Cached user ${userId}`);
      }

      return user;
    } catch (error) {
      logger.error('Find user by ID error:', error);
      return null;
    }
  }

  /**
   * Update user
   * @param {string} userId - User ID
   * @param {Object} updateData - Data to update
   * @returns {Promise<Object>} - Updated user
   */
  async updateUser(userId, updateData) {
    try {
      const supabase = this.getClient();

      // Note: PIN should already be hashed before calling this method
      // We don't hash it here to avoid double hashing

      updateData.updated_at = new Date().toISOString();

      const { data, error } = await supabase
        .from('users')
        .update(updateData)
        .eq('id', userId)
        .select()
        .single();

      if (error) {
        throw error;
      }

      const updatedUser = this.formatUser(data);
      
      // Clear cache and update with new data
      this.clearUserFromCache(userId);
      this.setUserInCache(userId, updatedUser);
      console.log(`🔄 [USER-SERVICE] Updated cache for user ${userId}`);

      return updatedUser;
    } catch (error) {
      logger.error('Update user error:', error);
      throw error;
    }
  }

  /**
   * Compare PIN
   * @param {string} candidatePin - PIN to compare
   * @param {string} hashedPin - Hashed PIN from database
   * @returns {Promise<boolean>} - Whether PIN matches
   */
  async comparePin(candidatePin, hashedPin) {
    if (!candidatePin || !hashedPin) return false;
    return await bcrypt.compare(candidatePin, hashedPin);
  }

  /**
   * Increment login attempts
   * @param {string} userId - User ID
   * @returns {Promise<void>}
   */
  async incLoginAttempts(userId) {
    try {
      const supabase = this.getClient();
      const user = await this.findById(userId);

      if (!user) return;

      const maxAttempts = parseInt(process.env.MAX_LOGIN_ATTEMPTS) || 5;
      const lockTime = parseInt(process.env.LOCKOUT_TIME) || 15 * 60 * 1000; // 15 minutes

      let updateData = {
        login_attempts: user.loginAttempts + 1,
        updated_at: new Date().toISOString()
      };

      // If max attempts reached, lock the account
      if (user.loginAttempts + 1 >= maxAttempts) {
        updateData.lock_until = new Date(Date.now() + lockTime).toISOString();
      }

      await supabase
        .from('users')
        .update(updateData)
        .eq('id', userId);

    } catch (error) {
      logger.error('Increment login attempts error:', error);
    }
  }

  /**
   * Reset login attempts
   * @param {string} userId - User ID
   * @returns {Promise<void>}
   */
  async resetLoginAttempts(userId) {
    try {
      const supabase = this.getClient();

      await supabase
        .from('users')
        .update({
          login_attempts: 0,
          lock_until: null,
          updated_at: new Date().toISOString()
        })
        .eq('id', userId);

    } catch (error) {
      logger.error('Reset login attempts error:', error);
    }
  }

  /**
   * Create phone verification token
   * @param {string} userId - User ID
   * @returns {Promise<string>} - OTP code
   */
  async createPhoneVerificationToken(userId) {
    try {
      // Generate 6-digit OTP
      const otp = Math.floor(100000 + Math.random() * 900000).toString();

      // Hash the OTP before storing
      const hashedOTP = crypto.createHash('sha256').update(otp).digest('hex');

      // Set expiry time (5 minutes)
      const expiryTime = parseInt(process.env.OTP_EXPIRY_TIME) || 5 * 60 * 1000;
      const expiresAt = new Date(Date.now() + expiryTime).toISOString();

      const supabase = this.getClient();
      await supabase
        .from('users')
        .update({
          phone_verification_token: hashedOTP,
          phone_verification_expires: expiresAt,
          updated_at: new Date().toISOString()
        })
        .eq('id', userId);

      return otp; // Return unhashed OTP for SMS
    } catch (error) {
      logger.error('Create phone verification token error:', error);
      throw error;
    }
  }

  /**
   * Verify phone verification token
   * @param {string} userId - User ID
   * @param {string} otp - OTP to verify
   * @returns {Promise<boolean>} - Whether OTP is valid
   */
  async verifyPhoneToken(userId, otp) {
    try {
      const user = await this.findById(userId);
      if (!user || !user.phoneVerificationToken || !user.phoneVerificationExpires) {
        return false;
      }

      // Check if token has expired
      if (new Date(user.phoneVerificationExpires) < new Date()) {
        return false;
      }

      // Hash the provided OTP and compare
      const hashedOTP = crypto.createHash('sha256').update(otp).digest('hex');

      if (hashedOTP !== user.phoneVerificationToken) {
        return false;
      }

      // Mark phone as verified and clear verification token
      await this.updateUser(userId, {
        is_phone_verified: true,
        phone_verification_token: null,
        phone_verification_expires: null,
        last_login: new Date().toISOString()
      });

      return true;
    } catch (error) {
      logger.error('Verify phone token error:', error);
      return false;
    }
  }

  /**
   * Create email verification token
   * @param {string} userId - User ID
   * @returns {Promise<string>} - OTP code
   */
  async createEmailVerificationToken(userId) {
    try {
      // Generate 6-digit OTP
      const otp = Math.floor(100000 + Math.random() * 900000).toString();

      // Hash the OTP before storing
      const hashedOTP = crypto.createHash('sha256').update(otp).digest('hex');

      // Set expiry time (5 minutes)
      const expiryTime = parseInt(process.env.OTP_EXPIRY_TIME) || 5 * 60 * 1000;
      const expiresAt = new Date(Date.now() + expiryTime).toISOString();

      const supabase = this.getClient();
      await supabase
        .from('users')
        .update({
          email_verification_token: hashedOTP,
          email_verification_expires: expiresAt,
          updated_at: new Date().toISOString()
        })
        .eq('id', userId);

      return otp; // Return unhashed OTP for email
    } catch (error) {
      logger.error('Create email verification token error:', error);
      throw error;
    }
  }

  /**
   * Verify email OTP
   * @param {string} userId - User ID
   * @param {string} otp - OTP to verify
   * @returns {Promise<boolean>} - Whether OTP is valid
   */
  async verifyEmailOTP(userId, otp) {
    try {
      const user = await this.findById(userId);
      if (!user || !user.emailVerificationToken || !user.emailVerificationExpires) {
        return false;
      }

      // Check if token has expired
      if (new Date(user.emailVerificationExpires) < new Date()) {
        return false;
      }

      // Hash the provided OTP and compare
      const hashedOTP = crypto.createHash('sha256').update(otp).digest('hex');

      if (hashedOTP !== user.emailVerificationToken) {
        return false;
      }

      // Clear verification token (but don't mark as verified yet - that's done separately)
      await this.updateUser(userId, {
        email_verification_token: null,
        email_verification_expires: null,
        last_login: new Date().toISOString()
      });

      return true;
    } catch (error) {
      logger.error('Verify email OTP error:', error);
      return false;
    }
  }

  /**
   * Mark email as verified
   * @param {string} userId - User ID
   * @returns {Promise<void>}
   */
  async markEmailAsVerified(userId) {
    try {
      await this.updateUser(userId, {
        is_email_verified: true
      });
    } catch (error) {
      logger.error('Mark email as verified error:', error);
      throw error;
    }
  }

  /**
   * Increment login attempts (alias for backward compatibility)
   * @param {string} userId - User ID
   * @returns {Promise<void>}
   */
  async incrementLoginAttempts(userId) {
    return this.incLoginAttempts(userId);
  }

  /**
   * Add IP address to user's history
   * @param {string} userId - User ID
   * @param {string} ip - IP address
   * @returns {Promise<void>}
   */
  async addIpAddress(userId, ip) {
    try {
      const supabase = this.getClient();

      // Get current IP addresses
      const user = await this.findById(userId);
      let ipAddresses = user.ipAddresses || [];

      // Keep only last 10 IP addresses
      if (ipAddresses.length >= 10) {
        ipAddresses = ipAddresses.slice(-9);
      }

      ipAddresses.push({
        ip: ip,
        timestamp: new Date().toISOString()
      });

      await supabase
        .from('users')
        .update({
          ip_addresses: ipAddresses,
          updated_at: new Date().toISOString()
        })
        .eq('id', userId);

    } catch (error) {
      logger.error('Add IP address error:', error);
    }
  }

  /**
   * Add device to user's history
   * @param {string} userId - User ID
   * @param {Object} deviceInfo - Device information
   * @returns {Promise<void>}
   */
  async addDevice(userId, deviceInfo) {
    try {
      const supabase = this.getClient();

      // Get current devices
      const user = await this.findById(userId);
      let devices = user.devices || [];

      // Create device entry
      const deviceEntry = {
        ...deviceInfo,
        timestamp: new Date().toISOString(),
        id: crypto.randomUUID()
      };

      // Check if device already exists (by device ID or unique identifier)
      const existingDeviceIndex = devices.findIndex(device => 
        device.deviceId === deviceInfo.deviceId || 
        device.id === deviceInfo.id ||
        (device.platform === deviceInfo.platform && 
         device.model === deviceInfo.model && 
         device.brand === deviceInfo.brand)
      );

      if (existingDeviceIndex !== -1) {
        // Update existing device
        devices[existingDeviceIndex] = {
          ...devices[existingDeviceIndex],
          ...deviceEntry,
          lastSeen: new Date().toISOString()
        };
      } else {
        // Keep only last 5 devices
        if (devices.length >= 5) {
          devices = devices.slice(-4);
        }
        devices.push(deviceEntry);
      }

      await supabase
        .from('users')
        .update({
          devices: devices,
          updated_at: new Date().toISOString()
        })
        .eq('id', userId);

    } catch (error) {
      logger.error('Add device error:', error);
    }
  }

  /**
   * Format user data for API response
   * @param {Object} userData - Raw user data from database
   * @returns {Object} - Formatted user data
   */
  formatUser(userData) {
    if (!userData) return null;

    return {
      id: userData.id,
      phoneNumber: userData.phone_number,
      firstName: userData.first_name,
      lastName: userData.last_name,
      email: userData.email,
      picture: userData.picture,
      dateOfBirth: userData.date_of_birth,
      isPhoneVerified: userData.is_phone_verified,
      isEmailVerified: userData.is_email_verified,
      balance: userData.balance,
      isActive: userData.is_active,
      role: userData.role,
      loginAttempts: userData.login_attempts,
      lockUntil: userData.lock_until,
      lastLogin: userData.last_login,
      phoneVerificationToken: userData.phone_verification_token,
      phoneVerificationExpires: userData.phone_verification_expires,
      emailVerificationToken: userData.email_verification_token,
      emailVerificationExpires: userData.email_verification_expires,
      googleUid: userData.google_uid,
      providerId: userData.provider_id,
      authProvider: userData.auth_provider,
      profileSource: userData.profile_source || 'manual',
      ipAddresses: userData.ip_addresses || [],
      devices: userData.devices || [],
      // Setup related fields
      biometricEnabled: userData.biometric_enabled || false,
      biometricType: userData.biometric_type,
      biometricDeviceInfo: userData.biometric_device_info,
      setupCompleted: userData.setup_completed || false,
      pinSetupAt: userData.pin_setup_at,
      biometricSetupAt: userData.biometric_setup_at,
      profileSetupAt: userData.profile_setup_at,
      setupCompletedAt: userData.setup_completed_at,
      createdAt: userData.created_at,
      updatedAt: userData.updated_at,
      // Virtual properties
      isLocked: userData.lock_until && new Date(userData.lock_until) > new Date(),
      pin: userData.pin // Only include when specifically needed
    };
  }
}

module.exports = new UserService();
