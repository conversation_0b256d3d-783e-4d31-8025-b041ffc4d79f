#!/usr/bin/env node

/**
 * Database Setup Script for Vendy VTU Application
 * 
 * This script helps set up the database by checking for missing tables
 * and providing guidance on how to set them up.
 */

require('dotenv').config();
const { createClient } = require('@supabase/supabase-js');
const fs = require('fs');
const path = require('path');

const logger = {
  info: (msg) => console.log(`ℹ️  ${msg}`),
  warn: (msg) => console.log(`⚠️  ${msg}`),
  error: (msg) => console.log(`❌ ${msg}`),
  success: (msg) => console.log(`✅ ${msg}`)
};

async function checkDatabaseSetup() {
  try {
    const supabaseUrl = process.env.SUPABASE_URL;
    const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY;

    if (!supabaseUrl || !supabaseServiceKey) {
      logger.error('Missing Supabase environment variables');
      logger.info('Please ensure SUPABASE_URL and SUPABASE_SERVICE_ROLE_KEY are set in your .env file');
      process.exit(1);
    }

    logger.info('Connecting to Supabase...');
    
    const supabase = createClient(supabaseUrl, supabaseServiceKey, {
      auth: {
        autoRefreshToken: false,
        persistSession: false
      }
    });

    // Check for essential tables
    const tablesToCheck = [
      'users',
      'sessions', 
      'transactions',
      'wallet_transactions',
      'sms_logs'
    ];

    const tableStatus = {};
    
    logger.info('Checking database tables...');
    
    for (const tableName of tablesToCheck) {
      try {
        const { error } = await supabase
          .from(tableName)
          .select('count', { count: 'exact', head: true });
        
        if (error && error.code === 'PGRST116') {
          tableStatus[tableName] = 'missing';
        } else if (error) {
          tableStatus[tableName] = 'error';
          logger.warn(`Error checking ${tableName}: ${error.message}`);
        } else {
          tableStatus[tableName] = 'exists';
        }
      } catch (err) {
        tableStatus[tableName] = 'missing';
      }
    }

    // Check for essential functions
    const functionsToCheck = [
      'get_user_balance',
      'update_user_balance',
      'cleanup_expired_sessions'
    ];

    logger.info('Checking database functions...');
    
    for (const functionName of functionsToCheck) {
      try {
        // Try to call the function with dummy parameters to see if it exists
        const { error } = await supabase.rpc(functionName, {});
        
        if (error && error.code === 'PGRST202') {
          tableStatus[`function_${functionName}`] = 'missing';
        } else {
          tableStatus[`function_${functionName}`] = 'exists';
        }
      } catch (err) {
        tableStatus[`function_${functionName}`] = 'missing';
      }
    }

    // Report status
    const missingTables = Object.entries(tableStatus)
      .filter(([name, status]) => status === 'missing' && !name.startsWith('function_'))
      .map(([name]) => name);

    const missingFunctions = Object.entries(tableStatus)
      .filter(([name, status]) => status === 'missing' && name.startsWith('function_'))
      .map(([name]) => name.replace('function_', ''));

    const existingTables = Object.entries(tableStatus)
      .filter(([name, status]) => status === 'exists' && !name.startsWith('function_'))
      .map(([name]) => name);

    const existingFunctions = Object.entries(tableStatus)
      .filter(([name, status]) => status === 'exists' && name.startsWith('function_'))
      .map(([name]) => name.replace('function_', ''));

    console.log('\n📊 Database Status Report:');
    console.log('========================');
    
    if (existingTables.length > 0) {
      logger.success(`Existing tables: ${existingTables.join(', ')}`);
    }
    
    if (existingFunctions.length > 0) {
      logger.success(`Existing functions: ${existingFunctions.join(', ')}`);
    }

    if (missingTables.length > 0) {
      logger.warn(`Missing tables: ${missingTables.join(', ')}`);
    }

    if (missingFunctions.length > 0) {
      logger.warn(`Missing functions: ${missingFunctions.join(', ')}`);
    }

    if (missingTables.length > 0 || missingFunctions.length > 0) {
      console.log('\n🔧 Setup Instructions:');
      console.log('======================');
      logger.info('To set up your database, follow these steps:');
      logger.info('1. Go to your Supabase project dashboard');
      logger.info('2. Navigate to SQL Editor');
      logger.info('3. Copy and paste the contents of one of these files:');
      logger.info('   - backend/supabase-setup.sql (complete setup)');
      logger.info('   - backend/database/schema.sql (alternative)');
      logger.info('4. Run the SQL script');
      logger.info('5. Restart your server');
      
      console.log('\n📁 Available SQL Files:');
      const sqlFiles = [
        'backend/supabase-setup.sql',
        'backend/database/schema.sql',
        'backend/database/setup-functions.sql'
      ];
      
      sqlFiles.forEach(file => {
        const fullPath = path.join(process.cwd(), file);
        if (fs.existsSync(fullPath)) {
          logger.info(`✓ ${file}`);
        } else {
          logger.warn(`✗ ${file} (not found)`);
        }
      });
    } else {
      logger.success('Database is properly set up! 🎉');
    }

  } catch (error) {
    logger.error(`Database check failed: ${error.message}`);
    process.exit(1);
  }
}

// Run the check
checkDatabaseSetup();
