const express = require('express');
const rateLimit = require('express-rate-limit');
const { body, validationResult } = require('express-validator');
const userService = require('../services/userService');
const smsService = require('../services/smsService');
const authService = require('../services/authService');
const logger = require('../utils/logger');
const aiService = require('../services/aiService');

const router = express.Router();

// Rate limiting for auth endpoints
const authLimiter = rateLimit({
  windowMs: 15 * 60 * 1000, // 15 minutes
  max: 5, // limit each IP to 5 requests per windowMs
  message: {
    error: 'Too many authentication attempts, please try again later.',
    retryAfter: 900
  },
  standardHeaders: true,
  legacyHeaders: false,
});

const otpLimiter = rateLimit({
  windowMs: 60 * 1000, // 1 minute
  max: 1, // limit each IP to 1 OTP request per minute
  message: {
    error: 'Please wait before requesting another OTP.',
    retryAfter: 60
  },
  standardHeaders: true,
  legacyHeaders: false,
});

// Validation middleware
const validatePhoneNumber = [
  body('phoneNumber')
    .matches(/^0[789][01]\d{8}$/)
    .withMessage('Please provide a valid Nigerian phone number (e.g., 08012345678)')
    .normalizeEmail()
];

const validateOTP = [
  body('otp')
    .isLength({ min: 6, max: 6 })
    .isNumeric()
    .withMessage('OTP must be a 6-digit number')
];

const validatePIN = [
  body('pin')
    .isLength({ min: 4, max: 6 })
    .isNumeric()
    .withMessage('PIN must be 4-6 digits')
];

/**
 * @route   GET /api/auth/test
 * @desc    Test endpoint to check if API is working
 * @access  Public
 */
router.get('/test', (req, res) => {
  console.log('🧪 [AUTH] Test endpoint called');
  console.log('🌐 Request IP:', req.ip);
  console.log('📋 Request headers:', JSON.stringify(req.headers, null, 2));

  res.status(200).json({
    status: 'success',
    message: 'Auth API is working!',
    timestamp: new Date().toISOString(),
    ip: req.ip
  });
});

/**
 * @route   GET /api/auth/send-otp
 * @desc    Test endpoint for OTP (should use POST instead)
 * @access  Public
 */
router.get('/send-otp', (req, res) => {
  console.log('⚠️ [AUTH] GET request to send-otp endpoint');
  console.log('📱 Query params:', JSON.stringify(req.query, null, 2));

  res.status(405).json({
    status: 'error',
    message: 'Method not allowed. Please use POST request with phone number in body.',
    correctMethod: 'POST',
    correctBody: {
      phoneNumber: '08012345678'
    },
    example: 'curl -X POST http://localhost:3002/api/auth/send-otp -H "Content-Type: application/json" -d \'{"phoneNumber":"08012345678"}\''
  });
});

/**
 * @route   POST /api/v1/auth/send-otp
 * @desc    Send OTP to phone number for verification
 * @access  Public
 */
router.post('/send-otp',
  otpLimiter,
  validatePhoneNumber,
  async (req, res) => {
    try {
      // Check for validation errors
      const errors = validationResult(req);
      if (!errors.isEmpty()) {
        return res.status(400).json({
          status: 'error',
          message: 'Validation failed',
          errors: errors.array()
        });
      }

      const { phoneNumber } = req.body;
      const clientIP = req.ip || req.connection.remoteAddress;

      console.log('🚀 [AUTH] OTP Request Started');
      console.log('📱 Phone Number:', phoneNumber);
      console.log('🌐 Client IP:', clientIP);
      console.log('📝 Request Body:', JSON.stringify(req.body, null, 2));

      logger.info(`OTP request from ${clientIP} for phone: ${phoneNumber}`);

      // Check if user exists
      console.log('👤 [AUTH] Checking if user exists...');
      let user = await userService.findByPhoneNumber(phoneNumber);

      if (!user) {
        console.log('➕ [AUTH] User not found, creating new user...');
        // Create new user if doesn't exist
        user = await userService.createUser({
          phoneNumber
          // No PIN - new users will set it up during onboarding
        });
        console.log('✅ [AUTH] New user created:', user.id);
      } else {
        console.log('✅ [AUTH] Existing user found:', user.id);
      }

      // Check if user is locked
      if (user.isLocked) {
        console.log('🔒 [AUTH] User account is locked');
        return res.status(423).json({
          status: 'error',
          message: 'Account is temporarily locked due to too many failed attempts. Please try again later.'
        });
      }

      // Generate OTP
      console.log('🔢 [AUTH] Generating OTP...');
      const otp = await userService.createPhoneVerificationToken(user.id);
      console.log('✅ [AUTH] OTP generated:', otp);

      // Send OTP via SMS
      console.log('📨 [AUTH] Sending OTP via SMS...');
      console.log('📞 Target phone:', phoneNumber);
      console.log('🔢 OTP code:', otp);

      const smsResult = await smsService.sendOTP(phoneNumber, otp, 'verification');

      console.log('📨 [AUTH] SMS Result:', JSON.stringify(smsResult, null, 2));

      if (!smsResult.success) {
        console.log('❌ [AUTH] SMS sending failed:', smsResult.error);
        logger.error(`Failed to send OTP to ${phoneNumber}: ${smsResult.error}`);
        return res.status(500).json({
          status: 'error',
          message: 'Failed to send verification code. Please try again.'
        });
      }

      console.log('✅ [AUTH] SMS sent successfully!');
      console.log('📧 Message ID:', smsResult.messageId);

      logger.info(`OTP sent successfully to ${phoneNumber}. Message ID: ${smsResult.messageId}`);

      res.status(200).json({
        status: 'success',
        message: 'Verification code sent to your phone number',
        data: {
          phoneNumber,
          expiresIn: 300 // 5 minutes in seconds
        }
      });

    } catch (error) {
      console.log('💥 [AUTH] Unexpected error occurred:');
      console.log('❌ Error message:', error.message);
      console.log('📚 Error stack:', error.stack);
      console.log('🔍 Full error:', error);

      logger.error('Send OTP error:', error);
      res.status(500).json({
        status: 'error',
        message: 'Internal server error'
      });
    }
  }
);

/**
 * @route   POST /api/v1/auth/verify-otp
 * @desc    Verify OTP and create/login user
 * @access  Public
 */
router.post('/verify-otp',
  authLimiter,
  [...validatePhoneNumber, ...validateOTP],
  async (req, res) => {
    try {
      // Check for validation errors
      const errors = validationResult(req);
      if (!errors.isEmpty()) {
        return res.status(400).json({
          status: 'error',
          message: 'Validation failed',
          errors: errors.array()
        });
      }

      const { phoneNumber, otp } = req.body;
      const clientIP = req.ip || req.connection.remoteAddress;

      logger.info(`OTP verification attempt from ${clientIP} for phone: ${phoneNumber}`);

      // Find user
      const user = await userService.findByPhoneNumber(phoneNumber);

      if (!user) {
        return res.status(404).json({
          status: 'error',
          message: 'User not found. Please request a new verification code.'
        });
      }

      // Check if user is locked
      if (user.isLocked) {
        return res.status(423).json({
          status: 'error',
          message: 'Account is temporarily locked. Please try again later.'
        });
      }

      // Check if OTP token exists and hasn't expired
      if (!user.phoneVerificationToken || !user.phoneVerificationExpires) {
        await userService.incLoginAttempts(user.id);
        return res.status(400).json({
          status: 'error',
          message: 'No verification code found. Please request a new one.'
        });
      }

      if (new Date(user.phoneVerificationExpires) < new Date()) {
        await userService.incLoginAttempts(user.id);
        return res.status(400).json({
          status: 'error',
          message: 'Verification code has expired. Please request a new one.'
        });
      }

      // Verify OTP
      const isValidOTP = await userService.verifyPhoneToken(user.id, otp);

      if (!isValidOTP) {
        await userService.incLoginAttempts(user.id);
        logger.warn(`Invalid OTP attempt for ${phoneNumber} from ${clientIP}`);
        return res.status(400).json({
          status: 'error',
          message: 'Invalid verification code. Please try again.'
        });
      }

      // OTP is valid - add IP address and reset login attempts
      await userService.addIpAddress(user.id, clientIP);
      await userService.resetLoginAttempts(user.id);

      // Generate JWT tokens
      const tokens = await authService.generateTokens(user.id, {
        ipAddress: clientIP,
        userAgent: req.headers['user-agent']
      });

      // Trigger AI analysis for successful login
      await aiService.triggerLoginAnalysis(user.id, {
        method: 'otp_verification',
        ip: clientIP,
        userAgent: req.headers['user-agent'],
        location: null // Could be enhanced with IP geolocation
      });

      logger.info(`Successful OTP verification for ${phoneNumber} from ${clientIP}`);

      res.status(200).json({
        status: 'success',
        message: 'Phone number verified successfully',
        data: {
          user: {
            id: user.id,
            phoneNumber: user.phoneNumber,
            isPhoneVerified: user.isPhoneVerified,
            balance: user.balance,
            createdAt: user.createdAt
          },
          tokens
        }
      });

    } catch (error) {
      logger.error('Verify OTP error:', error);
      res.status(500).json({
        status: 'error',
        message: 'Internal server error'
      });
    }
  }
);

/**
 * @route   POST /api/v1/auth/set-pin
 * @desc    Set transaction PIN for new users
 * @access  Private (requires valid JWT)
 */
router.post('/set-pin',
  authService.protect,
  validatePIN,
  async (req, res) => {
    try {
      const errors = validationResult(req);
      if (!errors.isEmpty()) {
        return res.status(400).json({
          status: 'error',
          message: 'Validation failed',
          errors: errors.array()
        });
      }

      const { pin } = req.body;
      const userId = req.user.id;

      const user = await userService.findById(userId);
      if (!user) {
        return res.status(404).json({
          status: 'error',
          message: 'User not found'
        });
      }

      // Set the new PIN
      await userService.updateUser(userId, { pin });

      logger.info(`PIN set successfully for user ${userId}`);

      res.status(200).json({
        status: 'success',
        message: 'Transaction PIN set successfully'
      });

    } catch (error) {
      logger.error('Set PIN error:', error);
      res.status(500).json({
        status: 'error',
        message: 'Internal server error'
      });
    }
  }
);

/**
 * @route   POST /api/v1/auth/refresh-token
 * @desc    Refresh access token using refresh token
 * @access  Public
 */
router.post('/refresh-token', async (req, res) => {
  try {
    const { refreshToken } = req.body;

    if (!refreshToken) {
      return res.status(400).json({
        status: 'error',
        message: 'Refresh token is required'
      });
    }

    const tokens = await authService.refreshAccessToken(refreshToken);

    if (!tokens) {
      return res.status(401).json({
        status: 'error',
        message: 'Invalid or expired refresh token'
      });
    }

    res.status(200).json({
      status: 'success',
      message: 'Token refreshed successfully',
      data: tokens
    });

  } catch (error) {
    logger.error('Refresh token error:', error);
    res.status(500).json({
      status: 'error',
      message: 'Internal server error'
    });
  }
});

/**
 * @route   POST /api/v1/auth/logout
 * @desc    Logout user and blacklist tokens
 * @access  Private
 */
router.post('/logout', authService.protect, async (req, res) => {
  try {
    const { refreshToken } = req.body;
    const accessToken = req.token;

    await authService.logout(accessToken, refreshToken);

    logger.info(`User ${req.user.id} logged out successfully`);

    res.status(200).json({
      status: 'success',
      message: 'Logged out successfully'
    });

  } catch (error) {
    logger.error('Logout error:', error);
    res.status(500).json({
      status: 'error',
      message: 'Logout failed'
    });
  }
});

module.exports = router;
