const admin = require('firebase-admin');
const logger = require('../utils/logger');

class FirebaseService {
  constructor() {
    this.initialized = false;
    this.init();
  }

  /**
   * Initialize Firebase Admin SDK
   */
  init() {
    try {
      if (!this.initialized) {
        // Check if Firebase project ID is configured
        if (!process.env.FIREBASE_PROJECT_ID || process.env.FIREBASE_PROJECT_ID === 'your-firebase-project-id') {
          logger.warn('Firebase not configured - Google authentication will not work');
          console.log('⚠️ [FIREBASE] Firebase not configured. Set FIREBASE_PROJECT_ID in .env file');
          return;
        }

        // Check if Firebase is already initialized
        if (admin.apps.length === 0) {
          // Initialize with service account key or default credentials
          if (process.env.FIREBASE_SERVICE_ACCOUNT_KEY) {
            // Use service account key from environment
            const serviceAccount = JSON.parse(process.env.FIREBASE_SERVICE_ACCOUNT_KEY);
            admin.initializeApp({
              credential: admin.credential.cert(serviceAccount),
              projectId: process.env.FIREBASE_PROJECT_ID,
              // Add additional configuration to handle token verification
              databaseURL: `https://${process.env.FIREBASE_PROJECT_ID}-default-rtdb.firebaseio.com`
            });
          } else {
            // Use default credentials (for development)
            admin.initializeApp({
              projectId: process.env.FIREBASE_PROJECT_ID,
              databaseURL: `https://${process.env.FIREBASE_PROJECT_ID}-default-rtdb.firebaseio.com`
            });
          }
        }

        this.auth = admin.auth();
        this.initialized = true;
        logger.info('Firebase Admin SDK initialized successfully');
        console.log('✅ [FIREBASE] Firebase Admin SDK initialized');
      }
    } catch (error) {
      logger.error('Firebase initialization error:', error);
      console.log('❌ [FIREBASE] Firebase initialization failed:', error.message);
      console.log('⚠️ [FIREBASE] Google authentication will not work');
      // Don't throw error to allow app to start without Firebase
    }
  }

  /**
   * Verify Google ID token from client
   * @param {string} idToken - Google ID token from client
   * @returns {Promise<Object>} - Decoded token with user info
   */
  async verifyGoogleToken(idToken) {
    try {
      console.log('🔍 [FIREBASE] Verifying Google ID token...');

      if (!this.initialized || !this.auth) {
        throw new Error('Firebase not configured. Please set up Firebase to use Google authentication.');
      }

      if (!idToken) {
        throw new Error('ID token is required');
      }

      // For React Native Google Sign-In tokens, we need to use Google's OAuth2 verification
      // because they have web client ID as audience instead of Firebase project ID
      console.log('🔄 [FIREBASE] Using Google OAuth2 token verification for React Native...');
      return await this.verifyGoogleTokenAlternative(idToken);

    } catch (error) {
      console.log('❌ [FIREBASE] Token verification failed:', error.message);
      logger.error('Google token verification error:', error);
      throw new Error('Invalid Google token');
    }
  }

  /**
   * Alternative Google token verification using Google OAuth2 verification
   * @param {string} idToken - Google ID token from client
   * @returns {Promise<Object>} - Decoded token with user info
   */
  async verifyGoogleTokenAlternative(idToken) {
    try {
      console.log('🔄 [FIREBASE] Using alternative Google token verification...');
      
      // Use Google's OAuth2 token verification endpoint
      const https = require('https');
      const { promisify } = require('util');
      
      return new Promise((resolve, reject) => {
        const options = {
          hostname: 'oauth2.googleapis.com',
          port: 443,
          path: `/tokeninfo?id_token=${idToken}`,
          method: 'GET',
        };

        const req = https.request(options, (res) => {
          let data = '';
          
          res.on('data', (chunk) => {
            data += chunk;
          });
          
          res.on('end', () => {
            try {
              const tokenInfo = JSON.parse(data);
              
              // Check if token is valid and from our app
              if (tokenInfo.error) {
                console.log('❌ [FIREBASE] Google token verification failed:', tokenInfo.error);
                reject(new Error('Invalid Google token'));
                return;
              }
              
              // Verify audience is our web client ID
              const expectedAudiences = [
                '1200340808-0ve3ueehjceva3v1ijofnppr7bbv5hhc.apps.googleusercontent.com',
                'vendy-511fb'
              ];
              
              if (!expectedAudiences.includes(tokenInfo.aud)) {
                console.log('❌ [FIREBASE] Invalid audience:', tokenInfo.aud);
                reject(new Error('Invalid token audience'));
                return;
              }
              
              // Check if token is expired
              const now = Math.floor(Date.now() / 1000);
              if (parseInt(tokenInfo.exp) < now) {
                console.log('❌ [FIREBASE] Token expired');
                reject(new Error('Token expired'));
                return;
              }
              
              console.log('✅ [FIREBASE] Alternative token verification successful');
              console.log('👤 [FIREBASE] User info:', {
                sub: tokenInfo.sub,
                email: tokenInfo.email,
                name: tokenInfo.name,
                picture: tokenInfo.picture,
                email_verified: tokenInfo.email_verified
              });
              
              resolve({
                uid: tokenInfo.sub,
                email: tokenInfo.email,
                name: tokenInfo.name,
                picture: tokenInfo.picture,
                emailVerified: tokenInfo.email_verified === 'true',
                provider: 'google',
                providerId: tokenInfo.sub
              });
              
            } catch (parseError) {
              console.log('❌ [FIREBASE] Error parsing token response:', parseError.message);
              reject(new Error('Invalid token response'));
            }
          });
        });

        req.on('error', (error) => {
          console.log('❌ [FIREBASE] Network error during token verification:', error.message);
          reject(new Error('Token verification network error'));
        });

        req.end();
      });
      
    } catch (error) {
      console.log('❌ [FIREBASE] Alternative token verification failed:', error.message);
      logger.error('Alternative Google token verification error:', error);
      throw new Error('Invalid Google token');
    }
  }

  /**
   * Create custom token for user
   * @param {string} uid - User UID
   * @param {Object} additionalClaims - Additional claims to include
   * @returns {Promise<string>} - Custom token
   */
  async createCustomToken(uid, additionalClaims = {}) {
    try {
      console.log('🎫 [FIREBASE] Creating custom token for UID:', uid);

      const customToken = await this.auth.createCustomToken(uid, additionalClaims);

      console.log('✅ [FIREBASE] Custom token created successfully');
      return customToken;

    } catch (error) {
      console.log('❌ [FIREBASE] Custom token creation failed:', error.message);
      logger.error('Custom token creation error:', error);
      throw new Error('Failed to create custom token');
    }
  }

  /**
   * Get user by UID
   * @param {string} uid - User UID
   * @returns {Promise<Object>} - User record
   */
  async getUserByUID(uid) {
    try {
      console.log('👤 [FIREBASE] Getting user by UID:', uid);

      const userRecord = await this.auth.getUser(uid);

      console.log('✅ [FIREBASE] User found:', userRecord.email);
      return userRecord;

    } catch (error) {
      console.log('❌ [FIREBASE] User not found:', error.message);
      logger.error('Get user by UID error:', error);
      throw new Error('User not found');
    }
  }

  /**
   * Delete user by UID
   * @param {string} uid - User UID
   * @returns {Promise<void>}
   */
  async deleteUser(uid) {
    try {
      console.log('🗑️ [FIREBASE] Deleting user:', uid);

      await this.auth.deleteUser(uid);

      console.log('✅ [FIREBASE] User deleted successfully');
      logger.info(`Firebase user deleted: ${uid}`);

    } catch (error) {
      console.log('❌ [FIREBASE] User deletion failed:', error.message);
      logger.error('Delete user error:', error);
      throw new Error('Failed to delete user');
    }
  }

  /**
   * Update user claims
   * @param {string} uid - User UID
   * @param {Object} customClaims - Custom claims to set
   * @returns {Promise<void>}
   */
  async setCustomUserClaims(uid, customClaims) {
    try {
      console.log('🏷️ [FIREBASE] Setting custom claims for UID:', uid);

      await this.auth.setCustomUserClaims(uid, customClaims);

      console.log('✅ [FIREBASE] Custom claims set successfully');
      logger.info(`Custom claims set for user: ${uid}`);

    } catch (error) {
      console.log('❌ [FIREBASE] Setting custom claims failed:', error.message);
      logger.error('Set custom claims error:', error);
      throw new Error('Failed to set custom claims');
    }
  }

  /**
   * Revoke refresh tokens for user
   * @param {string} uid - User UID
   * @returns {Promise<void>}
   */
  async revokeRefreshTokens(uid) {
    try {
      console.log('🔄 [FIREBASE] Revoking refresh tokens for UID:', uid);

      await this.auth.revokeRefreshTokens(uid);

      console.log('✅ [FIREBASE] Refresh tokens revoked successfully');
      logger.info(`Refresh tokens revoked for user: ${uid}`);

    } catch (error) {
      console.log('❌ [FIREBASE] Revoking refresh tokens failed:', error.message);
      logger.error('Revoke refresh tokens error:', error);
      throw new Error('Failed to revoke refresh tokens');
    }
  }
}

module.exports = new FirebaseService();
