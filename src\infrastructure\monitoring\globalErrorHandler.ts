import { logger } from './productionLogger';
import crashReporting from './crashReportingService';

/**
 * Global Error Handler Service
 * 
 * Provides centralized error handling for the entire application.
 * Integrates with crash reporting and logging services to ensure
 * all errors are properly tracked and reported.
 */

interface GlobalErrorConfig {
  enableGlobalHandler: boolean;
  enableUnhandledPromiseRejection: boolean;
  enableJSErrorHandler: boolean;
  logToConsole: boolean;
  reportToCrashlytics: boolean;
}

class GlobalErrorHandler {
  private isInitialized = false;
  private originalErrorHandler: any = null;
  private originalConsoleError: any = null;
  private originalConsoleWarn: any = null;

  private config: GlobalErrorConfig = {
    enableGlobalHandler: true,
    enableUnhandledPromiseRejection: true,
    enableJSErrorHandler: true,
    logToConsole: __DEV__, // Only log to console in development
    reportToCrashlytics: !__DEV__, // Only report to Crashlytics in production
  };

  /**
   * Initialize the global error handler
   */
  initialize() {
    if (this.isInitialized) {
      logger.warn('Global error handler already initialized', null, 'error');
      return;
    }

    try {
      // Set up JavaScript error handler
      if (this.config.enableJSErrorHandler) {
        this.setupJSErrorHandler();
      }

      // Set up unhandled promise rejection handler
      if (this.config.enableUnhandledPromiseRejection) {
        this.setupUnhandledPromiseRejectionHandler();
      }

      // Set up global error handler
      if (this.config.enableGlobalHandler) {
        this.setupGlobalErrorHandler();
      }

      // Override console methods for better error tracking
      this.setupConsoleOverrides();

      this.isInitialized = true;
      logger.info('Global error handler initialized successfully', null, 'error');

    } catch (error) {
      logger.error('Failed to initialize global error handler', error, 'error');
    }
  }

  /**
   * Set up JavaScript error handler using ErrorUtils
   */
  private setupJSErrorHandler() {
    try {
      // Store original error handler
      this.originalErrorHandler = ErrorUtils.getGlobalHandler();

      // Set custom error handler
      ErrorUtils.setGlobalHandler((error: Error, isFatal?: boolean) => {
        this.handleError(error, {
          isFatal: isFatal || false,
          source: 'JSError',
          context: 'JavaScript Runtime Error'
        });

        // Call original handler if it exists
        if (this.originalErrorHandler) {
          this.originalErrorHandler(error, isFatal);
        }
      });

      logger.info('JavaScript error handler set up', null, 'error');
    } catch (error) {
      logger.error('Failed to set up JavaScript error handler', error, 'error');
    }
  }

  /**
   * Set up unhandled promise rejection handler
   */
  private setupUnhandledPromiseRejectionHandler() {
    try {
      // Handle unhandled promise rejections
      const handleUnhandledRejection = (event: any) => {
        const error = event.reason instanceof Error 
          ? event.reason 
          : new Error(String(event.reason));

        this.handleError(error, {
          isFatal: false,
          source: 'UnhandledPromiseRejection',
          context: 'Unhandled Promise Rejection',
          additionalData: {
            promise: event.promise?.toString?.() || 'Unknown promise',
            reason: event.reason
          }
        });
      };

      // Add event listener for unhandled rejections
      if (typeof global !== 'undefined' && 'addEventListener' in global && typeof (global as any).addEventListener === 'function') {
        (global as any).addEventListener('unhandledrejection', handleUnhandledRejection);
      } else if (typeof process !== 'undefined' && process.on) {
        process.on('unhandledRejection', (reason: any, promise: Promise<any>) => {
          handleUnhandledRejection({ reason, promise });
        });
      }

      logger.info('Unhandled promise rejection handler set up', null, 'error');
    } catch (error) {
      logger.error('Failed to set up unhandled promise rejection handler', error, 'error');
    }
  }

  /**
   * Set up global error handler for React Native
   */
  private setupGlobalErrorHandler() {
    try {
      // Handle global errors that might not be caught by other handlers
      if (typeof global !== 'undefined') {
        const globalWithOnError = global as typeof global & {
          onerror?: ((message: any, source?: string, lineno?: number, colno?: number, error?: Error) => any) | null;
        };
        
        const originalOnError = globalWithOnError.onerror;
        
        globalWithOnError.onerror = (message: any, source?: string, lineno?: number, colno?: number, error?: Error) => {
          const errorObj = error || new Error(String(message));
          
          this.handleError(errorObj, {
            isFatal: false,
            source: 'GlobalError',
            context: 'Global Error Handler',
            additionalData: {
              message: String(message),
              source,
              lineno,
              colno
            }
          });

          // Call original handler if it exists
          if (originalOnError) {
            return originalOnError(message, source, lineno, colno, error);
          }
          
          return false;
        };
      }

      logger.info('Global error handler set up', null, 'error');
    } catch (error) {
      logger.error('Failed to set up global error handler', error, 'error');
    }
  }

  /**
   * Set up console method overrides for better error tracking
   */
  private setupConsoleOverrides() {
    try {
      // Store original console methods
      this.originalConsoleError = console.error;
      this.originalConsoleWarn = console.warn;

      // Override console.error
      console.error = (...args: any[]) => {
        // Log to our system
        const message = args.map(arg => 
          typeof arg === 'object' ? JSON.stringify(arg) : String(arg)
        ).join(' ');

        logger.error('Console Error', new Error(message), 'console');

        // Add breadcrumb
        crashReporting.addBreadcrumb({
          category: 'console',
          message: `console.error: ${message}`,
          level: 'error',
          data: { args: args.slice(0, 3) } // Limit data size
        });

        // Call original console.error if logging is enabled
        if (this.config.logToConsole && this.originalConsoleError) {
          this.originalConsoleError.apply(console, args);
        }
      };

      // Override console.warn
      console.warn = (...args: any[]) => {
        // Log to our system
        const message = args.map(arg => 
          typeof arg === 'object' ? JSON.stringify(arg) : String(arg)
        ).join(' ');

        logger.warn('Console Warning', message, 'console');

        // Add breadcrumb
        crashReporting.addBreadcrumb({
          category: 'console',
          message: `console.warn: ${message}`,
          level: 'warning',
          data: { args: args.slice(0, 3) } // Limit data size
        });

        // Call original console.warn if logging is enabled
        if (this.config.logToConsole && this.originalConsoleWarn) {
          this.originalConsoleWarn.apply(console, args);
        }
      };

      logger.info('Console method overrides set up', null, 'error');
    } catch (error) {
      logger.error('Failed to set up console overrides', error, 'error');
    }
  }

  /**
   * Handle an error with context information
   */
  private handleError(error: Error, context: {
    isFatal: boolean;
    source: string;
    context: string;
    additionalData?: Record<string, any>;
  }) {
    try {
      // Add breadcrumb for error context
      crashReporting.addBreadcrumb({
        category: 'error',
        message: `${context.source}: ${error.message}`,
        level: context.isFatal ? 'error' : 'warning',
        data: {
          source: context.source,
          context: context.context,
          isFatal: context.isFatal,
          ...context.additionalData
        }
      });

      // Log the error
      if (context.isFatal) {
        logger.error(`Fatal Error [${context.source}]`, error, 'error');
      } else {
        logger.warn(`Non-Fatal Error [${context.source}]`, error, 'error');
      }

      // Report to crash reporting service
      if (this.config.reportToCrashlytics) {
        crashReporting.recordError(error, context.context, context.isFatal);
      }

      // Log additional context if available
      if (context.additionalData) {
        logger.info('Error context data', context.additionalData, 'error');
      }

    } catch (handlingError) {
      // If error handling itself fails, use fallback logging
      if (this.originalConsoleError) {
        this.originalConsoleError('Error in error handler:', handlingError);
        this.originalConsoleError('Original error:', error);
      }
    }
  }

  /**
   * Manually report an error
   */
  reportError(error: Error, context?: string, isFatal: boolean = false) {
    this.handleError(error, {
      isFatal,
      source: 'Manual',
      context: context || 'Manually reported error'
    });
  }

  /**
   * Report a custom error with message
   */
  reportCustomError(message: string, data?: Record<string, any>, isFatal: boolean = false) {
    const error = new Error(message);
    this.handleError(error, {
      isFatal,
      source: 'Custom',
      context: 'Custom error report',
      additionalData: data
    });
  }

  /**
   * Clean up and restore original handlers
   */
  cleanup() {
    try {
      // Restore original error handler
      if (this.originalErrorHandler) {
        ErrorUtils.setGlobalHandler(this.originalErrorHandler);
      }

      // Restore original console methods
      if (this.originalConsoleError) {
        console.error = this.originalConsoleError;
      }
      if (this.originalConsoleWarn) {
        console.warn = this.originalConsoleWarn;
      }

      this.isInitialized = false;
      logger.info('Global error handler cleaned up', null, 'error');
    } catch (error) {
      logger.error('Failed to cleanup global error handler', error, 'error');
    }
  }

  /**
   * Check if the error handler is initialized
   */
  isReady(): boolean {
    return this.isInitialized;
  }

  /**
   * Update configuration
   */
  updateConfig(newConfig: Partial<GlobalErrorConfig>) {
    this.config = { ...this.config, ...newConfig };
    logger.info('Global error handler config updated', newConfig, 'error');
  }
}

// Export singleton instance
export const globalErrorHandler = new GlobalErrorHandler();
export default globalErrorHandler;