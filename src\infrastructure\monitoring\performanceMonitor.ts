/**
 * Advanced Performance Monitoring Service
 * Tracks app performance metrics, user interactions, and system health
 */

import { logger } from './productionLogger';

interface PerformanceMetric {
  name: string;
  value: number;
  timestamp: number;
  tags?: Record<string, string>;
}

interface UserInteraction {
  action: string;
  screen: string;
  timestamp: number;
  duration?: number;
  metadata?: Record<string, any>;
}

interface MemoryMetrics {
  used: number;
  total: number;
  jsHeapSizeUsed?: number;
  jsHeapSizeTotal?: number;
  percentage: number;
}

interface NetworkMetrics {
  requestCount: number;
  averageResponseTime: number;
  errorRate: number;
  cacheHitRate: number;
}

class PerformanceMonitor {
  private metrics: PerformanceMetric[] = [];
  private interactions: UserInteraction[] = [];
  private screenLoadTimes: Map<string, number> = new Map();
  private networkMetrics: NetworkMetrics = {
    requestCount: 0,
    averageResponseTime: 0,
    errorRate: 0,
    cacheHitRate: 0,
  };
  private monitoringInterval: NodeJS.Timeout | null = null;
  private maxMetrics = 1000; // Keep last 1000 metrics
  private maxInteractions = 500; // Keep last 500 interactions

  /**
   * Initialize performance monitoring
   */
  initialize(): void {
    try {
      logger.info('Initializing performance monitoring', {
        service: 'performanceMonitor',
        method: 'initialize'
      });

      // Start periodic monitoring
      this.startPeriodicMonitoring();

      // Track app launch performance
      this.trackAppLaunch();

      logger.info('Performance monitoring initialized successfully', {
        service: 'performanceMonitor',
        method: 'initialize'
      });
    } catch (error) {
      logger.error('Failed to initialize performance monitoring', {
        service: 'performanceMonitor',
        method: 'initialize',
        error: error instanceof Error ? error.message : String(error)
      });
    }
  }

  /**
   * Start periodic monitoring of system metrics
   */
  private startPeriodicMonitoring(): void {
    if (this.monitoringInterval) {
      clearInterval(this.monitoringInterval);
    }

    this.monitoringInterval = setInterval(() => {
      this.collectSystemMetrics();
    }, 30000); // Collect metrics every 30 seconds
  }

  /**
   * Track app launch performance
   */
  private trackAppLaunch(): void {
    const launchTime = Date.now();
    this.recordMetric('app_launch_time', launchTime, {
      platform: require('react-native').Platform.OS
    });
  }

  /**
   * Collect system performance metrics
   */
  private collectSystemMetrics(): void {
    try {
      // Memory metrics
      const memoryMetrics = this.getMemoryMetrics();
      this.recordMetric('memory_usage_percentage', memoryMetrics.percentage, {
        type: 'memory'
      });

      // Network metrics summary
      this.recordMetric('network_request_count', this.networkMetrics.requestCount, {
        type: 'network'
      });
      this.recordMetric('network_average_response_time', this.networkMetrics.averageResponseTime, {
        type: 'network'
      });
      this.recordMetric('network_error_rate', this.networkMetrics.errorRate, {
        type: 'network'
      });
      this.recordMetric('network_cache_hit_rate', this.networkMetrics.cacheHitRate, {
        type: 'network'
      });

    } catch (error) {
      logger.warn('Error collecting system metrics', {
        service: 'performanceMonitor',
        method: 'collectSystemMetrics',
        error: error instanceof Error ? error.message : String(error)
      });
    }
  }

  /**
   * Get memory usage metrics
   */
  private getMemoryMetrics(): MemoryMetrics {
    try {
      // React Native doesn't provide direct memory access
      // This is a placeholder for platform-specific implementations
      const mockMetrics: MemoryMetrics = {
        used: 0,
        total: 0,
        percentage: 0
      };

      // In a real implementation, you would use:
      // - react-native-device-info for device memory
      // - Performance API for JS heap (if available)
      // - Native modules for more detailed metrics

      return mockMetrics;
    } catch (error) {
      logger.warn('Error getting memory metrics', {
        service: 'performanceMonitor',
        method: 'getMemoryMetrics',
        error: error instanceof Error ? error.message : String(error)
      });
      return { used: 0, total: 0, percentage: 0 };
    }
  }

  /**
   * Record a performance metric
   */
  recordMetric(name: string, value: number, tags?: Record<string, string>): void {
    const metric: PerformanceMetric = {
      name,
      value,
      timestamp: Date.now(),
      tags
    };

    this.metrics.push(metric);

    // Keep only the most recent metrics
    if (this.metrics.length > this.maxMetrics) {
      this.metrics = this.metrics.slice(-this.maxMetrics);
    }

    // Log significant metrics
    if (this.isSignificantMetric(name, value)) {
      logger.performance(name, value, tags);
    }
  }

  /**
   * Check if a metric is significant enough to log
   */
  private isSignificantMetric(name: string, value: number): boolean {
    // Define thresholds for significant metrics
    const thresholds: Record<string, number> = {
      'screen_load_time': 2000, // 2 seconds
      'api_response_time': 5000, // 5 seconds
      'memory_usage_percentage': 80, // 80%
      'network_error_rate': 0.1, // 10%
    };

    const threshold = thresholds[name];
    return threshold !== undefined && value > threshold;
  }

  /**
   * Track user interaction
   */
  trackInteraction(action: string, screen: string, metadata?: Record<string, any>): string {
    const interactionId = `${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
    
    const interaction: UserInteraction = {
      action,
      screen,
      timestamp: Date.now(),
      metadata: {
        ...metadata,
        interactionId
      }
    };

    this.interactions.push(interaction);

    // Keep only recent interactions
    if (this.interactions.length > this.maxInteractions) {
      this.interactions = this.interactions.slice(-this.maxInteractions);
    }

    logger.userAction(action, {
      screen,
      interactionId,
      ...metadata
    });

    return interactionId;
  }

  /**
   * End user interaction and record duration
   */
  endInteraction(interactionId: string): void {
    const interaction = this.interactions.find(i => 
      i.metadata?.interactionId === interactionId
    );

    if (interaction) {
      const duration = Date.now() - interaction.timestamp;
      interaction.duration = duration;

      // Record interaction duration as a metric
      this.recordMetric('interaction_duration', duration, {
        action: interaction.action,
        screen: interaction.screen
      });
    }
  }

  /**
   * Track screen load time
   */
  trackScreenLoad(screenName: string): () => void {
    const startTime = Date.now();
    
    return () => {
      const loadTime = Date.now() - startTime;
      this.screenLoadTimes.set(screenName, loadTime);
      
      this.recordMetric('screen_load_time', loadTime, {
        screen: screenName
      });

      logger.performance(`${screenName}_load_time`, loadTime, {
        screen: screenName
      });
    };
  }

  /**
   * Track API call performance
   */
  trackApiCall(method: string, endpoint: string, responseTime: number, status: number): void {
    // Update network metrics
    this.networkMetrics.requestCount++;
    
    // Update average response time
    const currentAvg = this.networkMetrics.averageResponseTime;
    const count = this.networkMetrics.requestCount;
    this.networkMetrics.averageResponseTime = 
      ((currentAvg * (count - 1)) + responseTime) / count;

    // Update error rate
    if (status >= 400) {
      const errorCount = Math.floor(this.networkMetrics.errorRate * (count - 1)) + 1;
      this.networkMetrics.errorRate = errorCount / count;
    } else {
      const errorCount = Math.floor(this.networkMetrics.errorRate * (count - 1));
      this.networkMetrics.errorRate = errorCount / count;
    }

    // Record API call metric
    this.recordMetric('api_response_time', responseTime, {
      method,
      endpoint: endpoint.split('?')[0], // Remove query params
      status: status.toString()
    });

    logger.api(method, endpoint, status, responseTime);
  }

  /**
   * Track cache performance
   */
  trackCacheHit(hit: boolean): void {
    // Update cache hit rate
    const total = this.networkMetrics.requestCount || 1;
    const currentHits = Math.floor(this.networkMetrics.cacheHitRate * total);
    const newHits = hit ? currentHits + 1 : currentHits;
    this.networkMetrics.cacheHitRate = newHits / (total + 1);

    this.recordMetric('cache_hit', hit ? 1 : 0, {
      type: 'cache_performance'
    });
  }

  /**
   * Get performance summary
   */
  getPerformanceSummary(): {
    metrics: Record<string, number>;
    interactions: number;
    screenLoadTimes: Record<string, number>;
    networkMetrics: NetworkMetrics;
  } {
    // Calculate metric averages
    const metricAverages: Record<string, number> = {};
    const metricGroups: Record<string, number[]> = {};

    this.metrics.forEach(metric => {
      if (!metricGroups[metric.name]) {
        metricGroups[metric.name] = [];
      }
      metricGroups[metric.name].push(metric.value);
    });

    Object.keys(metricGroups).forEach(name => {
      const values = metricGroups[name];
      metricAverages[name] = values.reduce((sum, val) => sum + val, 0) / values.length;
    });

    return {
      metrics: metricAverages,
      interactions: this.interactions.length,
      screenLoadTimes: Object.fromEntries(this.screenLoadTimes),
      networkMetrics: { ...this.networkMetrics }
    };
  }

  /**
   * Export performance data for analytics
   */
  exportPerformanceData(): {
    timestamp: number;
    summary: any;
    recentMetrics: PerformanceMetric[];
    recentInteractions: UserInteraction[];
  } {
    return {
      timestamp: Date.now(),
      summary: this.getPerformanceSummary(),
      recentMetrics: this.metrics.slice(-100), // Last 100 metrics
      recentInteractions: this.interactions.slice(-50) // Last 50 interactions
    };
  }

  /**
   * Clear old performance data
   */
  cleanup(): void {
    const now = Date.now();
    const maxAge = 24 * 60 * 60 * 1000; // 24 hours

    // Clean old metrics
    this.metrics = this.metrics.filter(metric => 
      (now - metric.timestamp) < maxAge
    );

    // Clean old interactions
    this.interactions = this.interactions.filter(interaction => 
      (now - interaction.timestamp) < maxAge
    );

    logger.info('Performance data cleanup completed', {
      service: 'performanceMonitor',
      method: 'cleanup',
      metricsRemaining: this.metrics.length,
      interactionsRemaining: this.interactions.length
    });
  }

  /**
   * Stop monitoring
   */
  stop(): void {
    if (this.monitoringInterval) {
      clearInterval(this.monitoringInterval);
      this.monitoringInterval = null;
    }

    logger.info('Performance monitoring stopped', {
      service: 'performanceMonitor',
      method: 'stop'
    });
  }
}

// Export singleton instance
const performanceMonitor = new PerformanceMonitor();
export default performanceMonitor;
