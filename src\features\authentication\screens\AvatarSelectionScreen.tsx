import { useState, useRef } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  StatusBar,
  SafeAreaView,
  ScrollView,
  Animated,
} from 'react-native';
import type { NativeStackScreenProps } from '@react-navigation/native-stack';
import type { RootStackParamList } from '../../../navigation/navigation';
import { useTheme } from '../../../shared/components/layout/ThemeContext';
import { navigationHandler } from '../../../navigation/handlers/navigationHandler';

type Props = NativeStackScreenProps<RootStackParamList, 'AvatarSelection'>;

// Predefined avatar options (using emojis for simplicity)
const AVATAR_OPTIONS = [
  '👤', '👨', '👩', '🧑', '👦', '👧',
  '👨‍💼', '👩‍💼', '👨‍🎓', '👩‍🎓', '👨‍💻', '👩‍💻',
  '👨‍🎨', '👩‍🎨', '👨‍🔬', '👩‍🔬', '👨‍⚕️', '👩‍⚕️',
  '🧑‍🎤', '👨‍🎤', '👩‍🎤', '🧑‍🍳', '👨‍🍳', '👩‍🍳',
  '🧑‍🌾', '👨‍🌾', '👩‍🌾', '🧑‍🏫', '👨‍🏫', '👩‍🏫',
  '🧑‍🎯', '🧑‍🎪', '🧑‍🎭', '🧑‍🎨', '🧑‍🎬', '🧑‍🎮'
];

const AvatarSelectionScreen = ({ navigation, route }: Props) => {
  const { theme, isDark } = useTheme();
  const [selectedAvatar, setSelectedAvatar] = useState<string | null>(null);
  const [isLoading, setIsLoading] = useState(false);
  const buttonScaleAnim = useRef(new Animated.Value(1)).current;

  // Get user data from previous screen (if any)
  const { userData } = route.params || {};

  const handleAvatarSelect = (avatar: string) => {
    setSelectedAvatar(avatar);
  };

  const handleContinue = async () => {
    if (!selectedAvatar) return;

    setIsLoading(true);
    
    Animated.sequence([
      Animated.timing(buttonScaleAnim, {
        toValue: 0.95,
        duration: 100,
        useNativeDriver: true,
      }),
      Animated.timing(buttonScaleAnim, {
        toValue: 1,
        duration: 100,
        useNativeDriver: true,
      }),
    ]).start(() => {
      // Navigate to name setup with selected avatar
      navigationHandler.navigateToNameSetup({
        ...userData,
        avatar: selectedAvatar,
      });
      setIsLoading(false);
    });
  };

  const handleSkip = () => {
    // Navigate to name setup without avatar
    navigationHandler.navigateToNameSetup({
      ...userData,
      avatar: null,
    });
  };

  const styles = StyleSheet.create({
    container: {
      flex: 1,
      backgroundColor: theme.colors.background,
    },
    header: {
      paddingHorizontal: 20,
      paddingTop: 20,
      paddingBottom: 20,
      flexDirection: 'row',
      alignItems: 'center',
      justifyContent: 'space-between',
    },
    backButton: {
      padding: 8,
    },
    backButtonText: {
      fontSize: 20,
      color: theme.colors.text,
      fontWeight: '400',
    },
    skipButton: {
      padding: 8,
    },
    skipButtonText: {
      fontSize: 16,
      color: theme.colors.primary,
      fontWeight: '500',
    },
    content: {
      flex: 1,
      paddingHorizontal: 24,
      paddingTop: 20,
    },
    title: {
      fontSize: 28,
      fontWeight: '700',
      color: theme.colors.text,
      marginBottom: 8,
      textAlign: 'center',
    },
    subtitle: {
      fontSize: 16,
      color: theme.colors.muted,
      lineHeight: 24,
      marginBottom: 40,
      textAlign: 'center',
      maxWidth: 300,
      alignSelf: 'center',
    },
    avatarGrid: {
      flexDirection: 'row',
      flexWrap: 'wrap',
      justifyContent: 'space-between',
      paddingHorizontal: 10,
    },
    avatarOption: {
      width: '22%',
      aspectRatio: 1,
      backgroundColor: theme.colors.card,
      borderRadius: 20,
      alignItems: 'center',
      justifyContent: 'center',
      marginBottom: 16,
      borderWidth: 2,
      borderColor: 'transparent',
    },
    avatarOptionSelected: {
      borderColor: theme.colors.primary,
      backgroundColor: theme.colors.primary + '20',
    },
    avatarEmoji: {
      fontSize: 32,
    },
    bottomContainer: {
      paddingHorizontal: 24,
      paddingBottom: 32,
      paddingTop: 16,
    },
    continueButton: {
      backgroundColor: selectedAvatar ? theme.colors.primary : theme.colors.muted,
      borderRadius: 12,
      paddingVertical: 16,
      alignItems: 'center',
      justifyContent: 'center',
      opacity: selectedAvatar ? 1 : 0.6,
      marginBottom: 16,
    },
    continueText: {
      color: '#FFFFFF',
      fontSize: 16,
      fontWeight: '600',
    },
    orText: {
      textAlign: 'center',
      color: theme.colors.muted,
      fontSize: 14,
      marginBottom: 16,
    },
    skipFullButton: {
      backgroundColor: 'transparent',
      borderRadius: 12,
      paddingVertical: 16,
      alignItems: 'center',
      justifyContent: 'center',
      borderWidth: 1,
      borderColor: theme.colors.border,
    },
    skipFullButtonText: {
      color: theme.colors.text,
      fontSize: 16,
      fontWeight: '500',
    },
  });

  return (
    <SafeAreaView style={styles.container}>
      <StatusBar
        barStyle={isDark ? 'light-content' : 'dark-content'}
        backgroundColor={theme.colors.background}
      />

      {/* Header */}
      <View style={styles.header}>
        <TouchableOpacity
          style={styles.backButton}
          onPress={() => navigationHandler.goBack()}
          activeOpacity={0.7}
        >
          <Text style={styles.backButtonText}>←</Text>
        </TouchableOpacity>
        
        <TouchableOpacity
          style={styles.skipButton}
          onPress={handleSkip}
          activeOpacity={0.7}
        >
          <Text style={styles.skipButtonText}>Skip</Text>
        </TouchableOpacity>
      </View>

      {/* Content */}
      <ScrollView style={styles.content} showsVerticalScrollIndicator={false}>
        <Text style={styles.title}>Choose Your Avatar</Text>
        <Text style={styles.subtitle}>
          Pick an avatar that represents you. You can always change it later.
        </Text>

        <View style={styles.avatarGrid}>
          {AVATAR_OPTIONS.map((avatar, index) => (
            <TouchableOpacity
              key={index}
              style={[
                styles.avatarOption,
                selectedAvatar === avatar && styles.avatarOptionSelected,
              ]}
              onPress={() => handleAvatarSelect(avatar)}
              activeOpacity={0.7}
            >
              <Text style={styles.avatarEmoji}>{avatar}</Text>
            </TouchableOpacity>
          ))}
        </View>
      </ScrollView>

      {/* Bottom Buttons */}
      <View style={styles.bottomContainer}>
        <Animated.View
          style={[
            styles.continueButton,
            {
              transform: [{ scale: buttonScaleAnim }],
            },
          ]}
        >
          <TouchableOpacity
            onPress={handleContinue}
            disabled={!selectedAvatar || isLoading}
            activeOpacity={0.85}
            style={{ width: '100%', alignItems: 'center' }}
          >
            <Text style={styles.continueText}>
              {isLoading ? 'Continuing...' : 'Continue'}
            </Text>
          </TouchableOpacity>
        </Animated.View>

        <Text style={styles.orText}>or</Text>

        <TouchableOpacity
          style={styles.skipFullButton}
          onPress={handleSkip}
          activeOpacity={0.7}
        >
          <Text style={styles.skipFullButtonText}>Continue without avatar</Text>
        </TouchableOpacity>
      </View>
    </SafeAreaView>
  );
};

export default AvatarSelectionScreen;