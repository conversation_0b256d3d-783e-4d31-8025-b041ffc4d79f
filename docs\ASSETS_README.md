# Assets Directory

This directory contains all the static assets for the Vendy app.

## Structure

```
assets/
├── images/          # App images, backgrounds, illustrations
├── icons/           # App icons, logos, UI icons
├── fonts/           # Custom fonts (if any)
└── README.md        # This file
```

## Usage

### Images
Place your images in the `images/` folder. Common formats: `.png`, `.jpg`, `.jpeg`, `.webp`

Example usage in React Native:
```javascript
import { Image } from 'react-native';

// For local images
<Image source={require('../assets/images/logo.png')} />

// Or using import
import logoImage from '../assets/images/logo.png';
<Image source={logoImage} />
```

### Icons
Place your icon files in the `icons/` folder. Common formats: `.png`, `.svg`

### Fonts
Place custom font files in the `fonts/` folder. Common formats: `.ttf`, `.otf`

## Best Practices

1. **Naming**: Use descriptive, lowercase names with hyphens
   - ✅ `startup-background.png`
   - ❌ `StartupBackground.PNG`

2. **Optimization**: Optimize images for mobile
   - Use appropriate resolutions
   - Compress images to reduce app size

3. **Multiple Densities**: For React Native, consider providing multiple densities
   - `logo.png` (1x)
   - `<EMAIL>` (2x)
   - `<EMAIL>` (3x)

## File Organization Examples

```
images/
├── backgrounds/
│   ├── startup-bg.png
│   └── home-bg.png
├── illustrations/
│   ├── welcome-illustration.png
│   └── success-illustration.png
└── logos/
    ├── vendy-logo.png
    └── vendy-logo-white.png

icons/
├── ui/
│   ├── arrow-right.png
│   ├── check-circle.png
│   └── user-profile.png
└── services/
    ├── airtime-icon.png
    ├── data-icon.png
    └── bills-icon.png
```
