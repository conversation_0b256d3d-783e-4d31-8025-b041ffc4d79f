const jwt = require('jsonwebtoken');
const { promisify } = require('util');
const { getSupabase } = require('../config/database');
const logger = require('../utils/logger');

class AuthService {
  constructor() {
    this.jwtSecret = process.env.JWT_SECRET;
    this.jwtExpiresIn = process.env.JWT_EXPIRES_IN || '7d';
    this.jwtRefreshSecret = process.env.JWT_REFRESH_SECRET;
    this.jwtRefreshExpiresIn = process.env.JWT_REFRESH_EXPIRES_IN || '30d';

    if (!this.jwtSecret || !this.jwtRefreshSecret) {
      throw new Error('JWT secrets are required');
    }
  }

  /**
   * Generate access and refresh tokens and create session
   * @param {string} userId - User ID
   * @param {Object} sessionInfo - Optional session info (deviceInfo, ipAddress, userAgent)
   * @returns {Object} - Token pair
   */
  async generateTokens(userId, sessionInfo = {}) {
    console.log('🔐 [AUTH] Generating tokens for user:', userId);
    console.log('🔐 [AUTH] Token expiration settings:', {
      accessTokenExpiry: this.jwtExpiresIn,
      refreshTokenExpiry: this.jwtRefreshExpiresIn
    });
    
    const payload = { id: userId };
    const now = Math.floor(Date.now() / 1000);

    const accessToken = jwt.sign(payload, this.jwtSecret, {
      expiresIn: this.jwtExpiresIn,
      issuer: 'vendy-api',
      audience: 'vendy-app'
    });

    const refreshToken = jwt.sign(payload, this.jwtRefreshSecret, {
      expiresIn: this.jwtRefreshExpiresIn,
      issuer: 'vendy-api',
      audience: 'vendy-app'
    });

    // Decode the token to see what was actually created
    const decodedAccess = jwt.decode(accessToken);
    console.log('✅ [AUTH] Access token generated:', {
      userId: decodedAccess.id,
      issuedAt: new Date(decodedAccess.iat * 1000).toISOString(),
      expiresAt: new Date(decodedAccess.exp * 1000).toISOString(),
      validFor: Math.round((decodedAccess.exp - decodedAccess.iat) / 60) + ' minutes'
    });

    // Create session record
    try {
      await this.createSession(userId, accessToken, refreshToken, sessionInfo);
    } catch (error) {
      logger.warn('Failed to create session record:', error.message);
      // Don't fail token generation if session creation fails
    }

    return {
      accessToken,
      refreshToken,
      expiresIn: this.jwtExpiresIn
    };
  }

  /**
   * Create session record in database
   * @param {string} userId - User ID
   * @param {string} accessToken - Access token
   * @param {string} refreshToken - Refresh token
   * @param {Object} sessionInfo - Session information
   */
  async createSession(userId, accessToken, refreshToken, sessionInfo = {}) {
    try {
      const supabase = getSupabase();
      const crypto = require('crypto');

      // Hash tokens for storage
      const accessTokenHash = crypto.createHash('sha256').update(accessToken).digest('hex');
      const refreshTokenHash = crypto.createHash('sha256').update(refreshToken).digest('hex');

      // Calculate expiry times
      const refreshTokenExpiry = new Date(Date.now() + (30 * 24 * 60 * 60 * 1000)); // 30 days

      // Allow multiple active sessions per user (do not deactivate previous sessions)
      // If you want to limit sessions per device, add device-specific logic here.

      // Create new session
      const { error } = await supabase
        .from('sessions')
        .insert([{
          user_id: userId,
          access_token_hash: accessTokenHash,
          refresh_token_hash: refreshTokenHash,
          device_info: sessionInfo.deviceInfo || null,
          ip_address: sessionInfo.ipAddress || null,
          user_agent: sessionInfo.userAgent || null,
          is_active: true,
          expires_at: refreshTokenExpiry.toISOString(),
          created_at: new Date().toISOString(),
          updated_at: new Date().toISOString()
        }]);

      if (error) {
        throw error;
      }

      logger.info('Session created successfully', { 
        userId, 
        tokenHash: refreshTokenHash.substring(0, 8) 
      });
    } catch (error) {
      logger.error('Failed to create session:', error.message);
      throw error;
    }
  }

  /**
   * Verify access token
   * @param {string} token - JWT token
   * @returns {Object} - Decoded token
   */
  async verifyAccessToken(token) {
    try {
      console.log('🔍 [AUTH] Verifying access token...');
      
      const decoded = await promisify(jwt.verify)(token, this.jwtSecret, {
        issuer: 'vendy-api',
        audience: 'vendy-app'
      });

      console.log('✅ [AUTH] Token decoded successfully:', {
        userId: decoded.id,
        issuedAt: new Date(decoded.iat * 1000).toISOString(),
        expiresAt: new Date(decoded.exp * 1000).toISOString(),
        currentTime: new Date().toISOString(),
        timeUntilExpiry: Math.round((decoded.exp * 1000 - Date.now()) / 1000 / 60) + ' minutes'
      });

      // Check if token is blacklisted
      const isBlacklisted = await this.isTokenBlacklisted(token);
      if (isBlacklisted) {
        console.log('❌ [AUTH] Token is blacklisted');
        throw new Error('Token has been revoked');
      }

      return decoded;
    } catch (error) {
      console.log('❌ [AUTH] Token verification failed:', {
        error: error.message,
        tokenPreview: token ? token.substring(0, 20) + '...' : 'No token'
      });
      
      if (error.name === 'TokenExpiredError') {
        throw new Error('Token has expired');
      } else if (error.name === 'JsonWebTokenError') {
        throw new Error('Invalid token format');
      } else {
        throw new Error('Invalid or expired token');
      }
    }
  }

  /**
   * Verify refresh token
   * @param {string} token - Refresh token
   * @returns {Object} - Decoded token
   */
  async verifyRefreshToken(token) {
    try {
      const decoded = await promisify(jwt.verify)(token, this.jwtRefreshSecret, {
        issuer: 'vendy-api',
        audience: 'vendy-app'
      });

      // Check if token is blacklisted
      const isBlacklisted = await this.isTokenBlacklisted(token, 'refresh');
      if (isBlacklisted) {
        throw new Error('Refresh token has been revoked');
      }

      return decoded;
    } catch (error) {
      throw new Error('Invalid or expired refresh token');
    }
  }

  /**
   * Refresh access token
   * @param {string} refreshToken - Refresh token
   * @returns {Object|null} - New token pair or null if invalid
   */
  async refreshAccessToken(refreshToken) {
    try {
      const decoded = await this.verifyRefreshToken(refreshToken);

      // Verify user still exists and is active
      const supabase = getSupabase();
      const { data: user, error } = await supabase
        .from('users')
        .select('id, is_active')
        .eq('id', decoded.id)
        .single();

      if (error || !user || !user.is_active) {
        logger.warn('Token refresh failed: User not found or inactive', { 
          userId: decoded.id, 
          error: error?.message 
        });
        return null;
      }

      // Generate new tokens
      const tokens = await this.generateTokens(user.id);

      // Only blacklist the old refresh token after successful generation
      // This prevents issues if token generation fails
      try {
        await this.blacklistRefreshToken(refreshToken);
      } catch (blacklistError) {
        logger.warn('Failed to blacklist old refresh token, but continuing', { error: blacklistError.message });
        // Don't fail the entire refresh process if blacklisting fails
      }

      logger.info('Token refresh successful', { userId: decoded.id });
      return tokens;
    } catch (error) {
      logger.warn('Token refresh failed:', error.message);
      return null;
    }
  }

  /**
   * Blacklist an access token by marking session as inactive
   * @param {string} token - Access token to blacklist
   * @returns {Promise<void>}
   */
  async blacklistToken(token) {
    try {
      const supabase = getSupabase();

      // Hash the token for lookup
      const crypto = require('crypto');
      const tokenHash = crypto.createHash('sha256').update(token).digest('hex');

      // Mark session as inactive
      await supabase
        .from('sessions')
        .update({ is_active: false, updated_at: new Date().toISOString() })
        .eq('access_token_hash', tokenHash);

    } catch (error) {
      logger.error('Failed to blacklist access token:', error.message);
    }
  }

  /**
   * Blacklist a refresh token by marking session as inactive
   * @param {string} token - Refresh token to blacklist
   * @returns {Promise<void>}
   */
  async blacklistRefreshToken(token) {
    try {
      const supabase = getSupabase();

      // Hash the token for lookup
      const crypto = require('crypto');
      const tokenHash = crypto.createHash('sha256').update(token).digest('hex');

      // Mark session as inactive using refresh token hash
      await supabase
        .from('sessions')
        .update({ is_active: false, updated_at: new Date().toISOString() })
        .eq('refresh_token_hash', tokenHash);

    } catch (error) {
      logger.error('Failed to blacklist refresh token:', error.message);
    }
  }

  /**
   * Check if token is blacklisted
   * @param {string} token - Token to check
   * @param {string} tokenType - 'access' or 'refresh'
   * @returns {Promise<boolean>} - Whether token is blacklisted
   */
  async isTokenBlacklisted(token, tokenType = 'access') {
    try {
      const supabase = getSupabase();

      // Hash the token for lookup
      const crypto = require('crypto');
      const tokenHash = crypto.createHash('sha256').update(token).digest('hex');

      // Choose the correct column based on token type
      const hashColumn = tokenType === 'refresh' ? 'refresh_token_hash' : 'access_token_hash';

      // Check if session exists and is active
      const { data, error } = await supabase
        .from('sessions')
        .select('is_active')
        .eq(hashColumn, tokenHash)
        .single();

      if (error || !data) {
        // For refresh tokens, if session not found, don't consider it blacklisted
        // This allows for more graceful handling of missing sessions
        if (tokenType === 'refresh') {
          logger.warn('Refresh token session not found, allowing refresh', { 
            tokenHash: tokenHash.substring(0, 8),
            error: error?.message 
          });
          return false;
        }
        return true; // For access tokens, consider blacklisted if not found
      }

      return !data.is_active; // Return true if session is inactive (blacklisted)
    } catch (error) {
      logger.error('Failed to check token blacklist:', error.message);
      return false; // If check fails, assume not blacklisted
    }
  }

  /**
   * Logout user by blacklisting tokens
   * @param {string} accessToken - Access token
   * @param {string} refreshToken - Refresh token
   * @returns {Promise<void>}
   */
  async logout(accessToken, refreshToken) {
    try {
      await Promise.all([
        this.blacklistToken(accessToken),
        this.blacklistRefreshToken(refreshToken)
      ]);
    } catch (error) {
      logger.error('Failed to logout user:', error.message);
      throw new Error('Logout failed');
    }
  }

  /**
   * Optional authentication middleware - sets req.user if token is valid, but doesn't block if no token
   * @param {Object} req - Express request object
   * @param {Object} res - Express response object
   * @param {Function} next - Express next function
   */
  async optionalAuth(req, res, next) {
    try {
      console.log('🔓 [AUTH] Optional auth middleware called for:', req.method, req.originalUrl);
      
      // Get token from header
      let token;
      if (req.headers.authorization && req.headers.authorization.startsWith('Bearer')) {
        token = req.headers.authorization.split(' ')[1];
      }

      console.log('🔑 [AUTH] Token present:', !!token);

      if (!token) {
        console.log('ℹ️ [AUTH] No token provided - continuing without authentication');
        return next();
      }

      // Get auth service instance
      const authServiceInstance = require('../services/authService');
      
      // Verify token
      console.log('🔍 [AUTH] Verifying token...');
      const decoded = await authServiceInstance.verifyAccessToken(token);

      // Check if user still exists
      console.log('👤 [AUTH] Checking user exists for ID:', decoded.id);
      const supabase = getSupabase();
      const { data: user, error } = await supabase
        .from('users')
        .select('id, is_active, lock_until')
        .eq('id', decoded.id)
        .single();

      if (error || !user) {
        console.log('⚠️ [AUTH] User not found - continuing without authentication:', { error: error?.message, userId: decoded.id });
        return next();
      }

      // Check if user is active
      if (!user.is_active) {
        console.log('⚠️ [AUTH] User is inactive - continuing without authentication:', user.id);
        return next();
      }

      // Check if user is locked
      if (user.lock_until && new Date(user.lock_until) > new Date()) {
        console.log('⚠️ [AUTH] User is locked - continuing without authentication:', user.id);
        return next();
      }

      // Set user in request for authenticated access
      console.log('✅ [AUTH] Optional auth successful for user:', user.id);
      req.user = user;
      req.token = token;
      next();

    } catch (error) {
      console.log('⚠️ [AUTH] Optional auth error - continuing without authentication:', error.message);
      // Don't block the request, just continue without authentication
      next();
    }
  }

  /**
   * Middleware to protect routes
   * @param {Object} req - Express request object
   * @param {Object} res - Express response object
   * @param {Function} next - Express next function
   */
  async protect(req, res, next) {
    try {
      console.log('🛡️ [AUTH] Protect middleware called for:', req.method, req.originalUrl);
      
      // Get token from header
      let token;
      if (req.headers.authorization && req.headers.authorization.startsWith('Bearer')) {
        token = req.headers.authorization.split(' ')[1];
      }

      console.log('🔑 [AUTH] Token present:', !!token);

      if (!token) {
        console.log('❌ [AUTH] No token provided');
        return res.status(401).json({
          status: 'error',
          message: 'Access denied. No token provided.'
        });
      }

      // Get auth service instance
      const authServiceInstance = require('../services/authService');
      
      // Verify token
      console.log('🔍 [AUTH] Verifying token...');
      const decoded = await authServiceInstance.verifyAccessToken(token);

      // Check if user still exists
      console.log('👤 [AUTH] Checking user exists for ID:', decoded.id);
      const supabase = getSupabase();
      const { data: user, error } = await supabase
        .from('users')
        .select('id, is_active, lock_until')
        .eq('id', decoded.id)
        .single();

      if (error || !user) {
        console.log('❌ [AUTH] User not found:', { error: error?.message, userId: decoded.id });
        return res.status(401).json({
          status: 'error',
          message: 'User no longer exists.'
        });
      }

      console.log('✅ [AUTH] User found:', { id: user.id, isActive: user.is_active });

      // Check if user is active
      if (!user.is_active) {
        console.log('❌ [AUTH] User account is deactivated');
        return res.status(401).json({
          status: 'error',
          message: 'User account is deactivated.'
        });
      }

      // Check if user is locked
      const isLocked = user.lock_until && new Date(user.lock_until) > new Date();
      if (isLocked) {
        console.log('❌ [AUTH] User account is locked until:', user.lock_until);
        return res.status(423).json({
          status: 'error',
          message: 'Account is temporarily locked.'
        });
      }

      // Grant access to protected route
      console.log('✅ [AUTH] Access granted to user:', user.id);
      req.user = user;
      req.token = token;
      next();

    } catch (error) {
      console.log('❌ [AUTH] Protection error:', error.message);
      logger.error('Auth protection error:', error.message);
      return res.status(401).json({
        status: 'error',
        message: 'Invalid token.'
      });
    }
  }

  /**
   * Middleware to restrict access to specific roles
   * @param {...string} roles - Allowed roles
   * @returns {Function} - Express middleware
   */
  restrictTo(...roles) {
    return (req, res, next) => {
      if (!roles.includes(req.user.role)) {
        return res.status(403).json({
          status: 'error',
          message: 'Access denied. Insufficient permissions.'
        });
      }
      next();
    };
  }

  /**
   * Extract user ID from token without verification (for logging)
   * @param {string} token - JWT token
   * @returns {string|null} - User ID or null
   */
  extractUserIdFromToken(token) {
    try {
      const decoded = jwt.decode(token);
      return decoded?.id || null;
    } catch (error) {
      return null;
    }
  }
}

const authService = new AuthService();
module.exports = authService;
