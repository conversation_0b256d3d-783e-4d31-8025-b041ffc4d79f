import { logger } from './productionLogger';

interface ValidationRule {
  required?: boolean;
  minLength?: number;
  maxLength?: number;
  pattern?: RegExp;
  customValidator?: (value: string) => boolean;
  sanitizer?: (value: string) => string;
}

interface ValidationResult {
  isValid: boolean;
  sanitizedValue: string;
  errors: string[];
  warnings: string[];
}

interface SecurityValidationResult extends ValidationResult {
  riskLevel: 'low' | 'medium' | 'high';
  securityFlags: string[];
}

class InputValidationService {
  private readonly commonPatterns = {
    email: /^[a-zA-Z0-9.!#$%&'*+/=?^_`{|}~-]+@[a-zA-Z0-9](?:[a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?(?:\.[a-zA-Z0-9](?:[a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?)*$/,
    phone: /^\+?[1-9]\d{1,14}$/,
    pin: /^\d{4,6}$/,
    name: /^[a-zA-Z\s'-]{1,50}$/,
    alphanumeric: /^[a-zA-Z0-9]+$/,
    noSpecialChars: /^[a-zA-Z0-9\s]+$/,
    url: /^https?:\/\/(www\.)?[-a-zA-Z0-9@:%._\+~#=]{1,256}\.[a-zA-Z0-9()]{1,6}\b([-a-zA-Z0-9()@:%_\+.~#?&//=]*)$/,
  };

  private readonly securityPatterns = {
    sqlInjection: /(\b(SELECT|INSERT|UPDATE|DELETE|DROP|CREATE|ALTER|EXEC|UNION|SCRIPT)\b|[';]|--|\*|\/\*|\*\/)/i,
    xss: /(<script|<iframe|<object|<embed|<link|<meta|javascript:|vbscript:|onload|onerror|onclick)/i,
    pathTraversal: /(\.\.\/|\.\.\\|%2e%2e%2f|%2e%2e%5c)/i,
    commandInjection: /(\||&|;|`|\$\(|\${|<|>)/,
    htmlTags: /<[^>]*>/g,
  };

  /**
   * Validate email address with enhanced security checks
   */
  validateEmail(email: string): SecurityValidationResult {
    const sanitized = this.sanitizeInput(email.toLowerCase().trim());
    const errors: string[] = [];
    const warnings: string[] = [];
    const securityFlags: string[] = [];
    let riskLevel: 'low' | 'medium' | 'high' = 'low';

    // Basic format validation
    if (!this.commonPatterns.email.test(sanitized)) {
      errors.push('Invalid email format');
    }

    // Length validation
    if (sanitized.length > 254) {
      errors.push('Email address too long');
      riskLevel = 'medium';
    }

    // Security checks
    if (this.containsSuspiciousPatterns(sanitized)) {
      securityFlags.push('Contains suspicious patterns');
      riskLevel = 'high';
    }

    // Domain validation
    const domain = sanitized.split('@')[1];
    if (domain && domain.length > 63) {
      warnings.push('Domain name is unusually long');
      riskLevel = 'medium';
    }

    // Check for disposable email domains (basic check)
    const disposableDomains = ['tempmail.org', '10minutemail.com', 'guerrillamail.com'];
    if (domain && disposableDomains.includes(domain)) {
      warnings.push('Disposable email domain detected');
      securityFlags.push('Disposable email');
    }

    return {
      isValid: errors.length === 0,
      sanitizedValue: sanitized,
      errors,
      warnings,
      riskLevel,
      securityFlags
    };
  }

  /**
   * Validate phone number with international format support
   */
  validatePhone(phone: string): SecurityValidationResult {
    const sanitized = this.sanitizePhoneNumber(phone);
    const errors: string[] = [];
    const warnings: string[] = [];
    const securityFlags: string[] = [];
    let riskLevel: 'low' | 'medium' | 'high' = 'low';

    // Remove all non-digit characters except +
    const cleanPhone = sanitized.replace(/[^\d+]/g, '');

    // Basic format validation
    if (!this.commonPatterns.phone.test(cleanPhone)) {
      errors.push('Invalid phone number format');
    }

    // Length validation (E.164 format)
    if (cleanPhone.length < 7 || cleanPhone.length > 15) {
      errors.push('Phone number length invalid');
    }

    // Security checks
    if (this.containsSuspiciousPatterns(sanitized)) {
      securityFlags.push('Contains suspicious patterns');
      riskLevel = 'high';
    }

    // Check for repeated digits (potential fake number)
    const digitCounts = this.analyzeDigitPattern(cleanPhone);
    if (digitCounts.maxRepeated > 5) {
      warnings.push('Unusual digit pattern detected');
      securityFlags.push('Suspicious pattern');
      riskLevel = 'medium';
    }

    return {
      isValid: errors.length === 0,
      sanitizedValue: cleanPhone,
      errors,
      warnings,
      riskLevel,
      securityFlags
    };
  }

  /**
   * Validate PIN with security considerations
   */
  validatePin(pin: string): SecurityValidationResult {
    const sanitized = pin.trim();
    const errors: string[] = [];
    const warnings: string[] = [];
    const securityFlags: string[] = [];
    let riskLevel: 'low' | 'medium' | 'high' = 'low';

    // Basic format validation
    if (!this.commonPatterns.pin.test(sanitized)) {
      errors.push('PIN must be 4-6 digits only');
    }

    // Security checks for weak PINs
    const weakPatterns = [
      /^(\d)\1+$/, // All same digits (1111, 2222, etc.)
      /^1234$|^4321$/, // Sequential
      /^0000$|^1111$|^2222$|^3333$|^4444$|^5555$|^6666$|^7777$|^8888$|^9999$/, // Common weak PINs
      /^0123$|^1234$|^2345$|^3456$|^4567$|^5678$|^6789$/, // Sequential ascending
      /^9876$|^8765$|^7654$|^6543$|^5432$|^4321$|^3210$/, // Sequential descending
    ];

    for (const pattern of weakPatterns) {
      if (pattern.test(sanitized)) {
        warnings.push('PIN is commonly used and may be easily guessed');
        securityFlags.push('Weak PIN pattern');
        riskLevel = 'medium';
        break;
      }
    }

    // Check for birth year patterns (19xx, 20xx)
    if (/^(19|20)\d{2}$/.test(sanitized)) {
      warnings.push('PIN appears to be a year, consider using a more secure PIN');
      securityFlags.push('Potential birth year');
      riskLevel = 'medium';
    }

    return {
      isValid: errors.length === 0,
      sanitizedValue: sanitized,
      errors,
      warnings,
      riskLevel,
      securityFlags
    };
  }

  /**
   * Validate name with cultural considerations
   */
  validateName(name: string): SecurityValidationResult {
    const sanitized = this.sanitizeName(name);
    const errors: string[] = [];
    const warnings: string[] = [];
    const securityFlags: string[] = [];
    let riskLevel: 'low' | 'medium' | 'high' = 'low';

    // Basic format validation
    if (!this.commonPatterns.name.test(sanitized)) {
      errors.push('Name contains invalid characters');
    }

    // Length validation
    if (sanitized.length < 1) {
      errors.push('Name is required');
    } else if (sanitized.length > 50) {
      errors.push('Name is too long');
    }

    // Security checks
    if (this.containsSuspiciousPatterns(sanitized)) {
      securityFlags.push('Contains suspicious patterns');
      riskLevel = 'high';
    }

    // Check for excessive special characters
    const specialCharCount = (sanitized.match(/['-]/g) || []).length;
    if (specialCharCount > 3) {
      warnings.push('Unusual number of special characters');
      securityFlags.push('Excessive special chars');
      riskLevel = 'medium';
    }

    return {
      isValid: errors.length === 0,
      sanitizedValue: sanitized,
      errors,
      warnings,
      riskLevel,
      securityFlags
    };
  }

  /**
   * Generic validation with custom rules
   */
  validateWithRules(value: string, rules: ValidationRule): ValidationResult {
    let sanitized = value;
    const errors: string[] = [];
    const warnings: string[] = [];

    // Apply sanitizer if provided
    if (rules.sanitizer) {
      sanitized = rules.sanitizer(sanitized);
    } else {
      sanitized = this.sanitizeInput(sanitized);
    }

    // Required validation
    if (rules.required && (!sanitized || sanitized.trim().length === 0)) {
      errors.push('This field is required');
    }

    // Length validation
    if (rules.minLength && sanitized.length < rules.minLength) {
      errors.push(`Minimum length is ${rules.minLength} characters`);
    }

    if (rules.maxLength && sanitized.length > rules.maxLength) {
      errors.push(`Maximum length is ${rules.maxLength} characters`);
    }

    // Pattern validation
    if (rules.pattern && !rules.pattern.test(sanitized)) {
      errors.push('Invalid format');
    }

    // Custom validation
    if (rules.customValidator && !rules.customValidator(sanitized)) {
      errors.push('Custom validation failed');
    }

    return {
      isValid: errors.length === 0,
      sanitizedValue: sanitized,
      errors,
      warnings
    };
  }

  /**
   * Sanitize general input
   */
  private sanitizeInput(input: string): string {
    return input
      .trim()
      .replace(this.securityPatterns.htmlTags, '') // Remove HTML tags
      .replace(/[\x00-\x1F\x7F]/g, ''); // Remove control characters
  }

  /**
   * Sanitize phone number
   */
  private sanitizePhoneNumber(phone: string): string {
    return phone
      .trim()
      .replace(/[^\d+\-\s()]/g, ''); // Keep only digits, +, -, spaces, and parentheses
  }

  /**
   * Sanitize name input
   */
  private sanitizeName(name: string): string {
    return name
      .trim()
      .replace(/[^\p{L}\s'-]/gu, '') // Keep only letters, spaces, apostrophes, and hyphens (Unicode-aware)
      .replace(/\s+/g, ' '); // Normalize multiple spaces
  }

  /**
   * Check for suspicious security patterns
   */
  private containsSuspiciousPatterns(input: string): boolean {
    for (const pattern of Object.values(this.securityPatterns)) {
      if (pattern.test(input)) {
        logger.security('Suspicious pattern detected in input', {
          pattern: pattern.source,
          inputLength: input.length
        });
        return true;
      }
    }
    return false;
  }

  /**
   * Analyze digit patterns in phone numbers
   */
  private analyzeDigitPattern(phone: string): { maxRepeated: number; entropy: number } {
    const digits = phone.replace(/\D/g, '');
    const digitCounts: { [key: string]: number } = {};
    let maxRepeated = 0;
    let currentDigit = '';
    let currentCount = 0;

    // Count digit frequencies and find max repeated sequence
    for (const digit of digits) {
      digitCounts[digit] = (digitCounts[digit] || 0) + 1;
      
      if (digit === currentDigit) {
        currentCount++;
        maxRepeated = Math.max(maxRepeated, currentCount);
      } else {
        currentDigit = digit;
        currentCount = 1;
      }
    }

    // Calculate entropy (simplified)
    const uniqueDigits = Object.keys(digitCounts).length;
    const entropy = uniqueDigits / 10; // 0-1 scale

    return { maxRepeated, entropy };
  }

  /**
   * Batch validate multiple inputs
   */
  validateBatch(inputs: { [key: string]: { value: string; type: 'email' | 'phone' | 'pin' | 'name' } }): { [key: string]: SecurityValidationResult } {
    const results: { [key: string]: SecurityValidationResult } = {};

    for (const [key, input] of Object.entries(inputs)) {
      switch (input.type) {
        case 'email':
          results[key] = this.validateEmail(input.value);
          break;
        case 'phone':
          results[key] = this.validatePhone(input.value);
          break;
        case 'pin':
          results[key] = this.validatePin(input.value);
          break;
        case 'name':
          results[key] = this.validateName(input.value);
          break;
        default:
          results[key] = {
            isValid: false,
            sanitizedValue: input.value,
            errors: ['Unknown validation type'],
            warnings: [],
            riskLevel: 'high',
            securityFlags: ['Unknown type']
          };
      }
    }

    return results;
  }
}

// Export singleton instance
export const inputValidation = new InputValidationService();
export default inputValidation;
