const { createClient } = require('@supabase/supabase-js');
const logger = require('../utils/logger');

let supabase = null;

const connectDB = async () => {
  try {
    const supabaseUrl = process.env.SUPABASE_URL;
    const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY;

    if (!supabaseUrl || !supabaseServiceKey) {
      throw new Error('Supabase URL and Service Role Key are required in environment variables');
    }

    // Create Supabase client with service role key for admin operations
    supabase = createClient(supabaseUrl, supabaseServiceKey, {
      auth: {
        autoRefreshToken: false,
        persistSession: false
      }
    });

    // Test the connection
    const { data, error } = await supabase
      .from('users')
      .select('count', { count: 'exact', head: true });

    if (error && error.code !== 'PGRST116') { // PGRST116 is "table not found" which is ok for initial setup
      throw error;
    }

    logger.info('Supabase connected successfully');

    // Create tables if they don't exist
    await createTables();

  } catch (error) {
    logger.error('Supabase connection failed:', error.message);
    process.exit(1);
  }
};

const createTables = async () => {
  try {
    // Check if essential tables exist by trying to query them
    const tablesToCheck = ['users', 'sessions', 'transactions'];
    const missingTables = [];

    for (const tableName of tablesToCheck) {
      try {
        const { error } = await supabase
          .from(tableName)
          .select('count', { count: 'exact', head: true });

        if (error && error.code === 'PGRST116') {
          // Table doesn't exist
          missingTables.push(tableName);
        }
      } catch (err) {
        // Table doesn't exist or other error
        missingTables.push(tableName);
      }
    }

    if (missingTables.length > 0) {
      logger.warn(`Missing database tables: ${missingTables.join(', ')}`);
      logger.warn('Please run the SQL setup script in your Supabase dashboard:');
      logger.warn('1. Go to your Supabase project dashboard');
      logger.warn('2. Navigate to SQL Editor');
      logger.warn('3. Run the script from: backend/supabase-setup.sql');
      logger.warn('4. Restart the server after running the script');
    } else {
      logger.info('Database tables initialized');
    }
  } catch (error) {
    logger.warn('Table verification skipped:', error.message);
    logger.warn('Please ensure your database is properly set up using the SQL scripts');
  }
};

const getSupabase = () => {
  if (!supabase) {
    throw new Error('Supabase not initialized. Call connectDB first.');
  }
  return supabase;
};

module.exports = { connectDB, getSupabase };
