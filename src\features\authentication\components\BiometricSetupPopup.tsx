import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  Modal,
  Animated,
  Platform,
  Alert,
} from 'react-native';
import { useTheme } from '../../../shared/components/layout/ThemeContext';
import { FingerprintIcon, FaceIdIcon, SecurityIcon, CheckIcon } from '../../../shared/components/icons';
import { logger } from '../../../infrastructure/monitoring/productionLogger';
import ApiService from '../../../infrastructure/api/apiService';
import AsyncStorage from '@react-native-async-storage/async-storage';
import {
  checkBiometricCapability,
  authenticateWithBiometric,
  BiometricCapability
} from '../services/biometricUtils';

// Import biometric libraries
let ReactNativeBiometrics: any;
let TouchID: any;

try {
  ReactNativeBiometrics = require('react-native-biometrics').default;
} catch (e) {
  console.log('ReactNativeBiometrics not available');
}

try {
  TouchID = require('react-native-touch-id').default;
} catch (e) {
  console.log('TouchID not available');
}

interface BiometricSetupPopupProps {
  visible: boolean;
  onComplete: (enabled: boolean) => void;
  userData?: any;
}

const BiometricSetupPopup: React.FC<BiometricSetupPopupProps> = ({
  visible,
  onComplete,
  userData
}) => {
  const { theme, isDark } = useTheme();
  const [loading, setLoading] = useState(false);
  const [biometricCapability, setBiometricCapability] = useState<BiometricCapability>({
    available: false,
    type: 'none',
    error: null,
  });
  const [setupComplete, setSetupComplete] = useState(false);

  // Animation values
  const scaleAnimation = new Animated.Value(0);
  const fadeAnimation = new Animated.Value(0);

  useEffect(() => {
    if (visible) {
      checkBiometricAvailability();
      // Start entrance animation
      Animated.parallel([
        Animated.spring(scaleAnimation, {
          toValue: 1,
          useNativeDriver: true,
          tension: 100,
          friction: 8,
        }),
        Animated.timing(fadeAnimation, {
          toValue: 1,
          duration: 300,
          useNativeDriver: true,
        }),
      ]).start();
    } else {
      // Reset animations
      scaleAnimation.setValue(0);
      fadeAnimation.setValue(0);
      setSetupComplete(false);
    }
  }, [visible]);

  const checkBiometricAvailability = async () => {
    try {
      const capability = await checkBiometricCapability();
      setBiometricCapability(capability);
      logger.info('Biometric capability checked in popup', { 
        available: capability.available, 
        type: capability.type 
      }, 'biometric');
    } catch (error) {
      logger.error('Biometric capability check failed in popup', error, 'biometric');
      setBiometricCapability({
        available: false,
        type: 'none',
        error: 'Unable to check biometric capability',
      });
    }
  };

  const handleEnableBiometric = async () => {
    if (!biometricCapability.available) {
      Alert.alert('Not Available', 'Biometric authentication is not available on this device.');
      return;
    }

    setLoading(true);

    try {
      let authSuccess = false;

      // Try ReactNativeBiometrics first
      if (ReactNativeBiometrics) {
        const result = await ReactNativeBiometrics.simplePrompt({
          promptMessage: 'Enable biometric authentication for Vendy',
          cancelButtonText: 'Cancel',
        });
        authSuccess = result.success;
      }
      // Fallback to TouchID
      else if (TouchID) {
        const biometricOptions = {
          title: 'Enable Biometric Authentication',
          subtitle: 'Use your biometric to secure your Vendy account',
          description: 'Place your finger on the sensor or look at the camera',
          fallbackLabel: 'Use PIN instead',
          cancelLabel: 'Cancel',
        };
        await TouchID.authenticate('Enable biometric authentication for Vendy', biometricOptions);
        authSuccess = true;
      } else {
        throw new Error('Biometric authentication not available');
      }

      if (!authSuccess) {
        throw new Error('Biometric authentication failed');
      }

      // If authentication successful, save to backend
      const response = await ApiService.post('/setup/biometric', {
        enabled: true,
        biometricType: biometricCapability.type,
        deviceInfo: {
          platform: Platform.OS,
          version: Platform.Version,
          model: Platform.OS === 'ios' ? 'iPhone' : 'Android Device',
        },
      });

      if (response.data.status === 'success') {
        setSetupComplete(true);
        
        // Store biometric enabled status locally
        try {
          await AsyncStorage.setItem('biometricEnabled', 'true');
        } catch (error) {
          logger.error('Error saving biometric enabled status', error, 'biometric');
        }
        
        // Show success briefly then complete
        setTimeout(() => {
          onComplete(true);
        }, 1500);
      } else {
        throw new Error(response.data.message || 'Failed to enable biometric authentication');
      }

    } catch (error: any) {
      logger.error('Biometric setup error in popup', error, 'biometric');
      
      if (error.message === 'User canceled' || error.message === 'Authentication was canceled by the user') {
        // User canceled - treat as skip
        handleSkip();
      } else {
        Alert.alert('Setup Failed', error.message || 'Failed to enable biometric authentication');
      }
    } finally {
      setLoading(false);
    }
  };

  const handleSkip = () => {
    logger.userAction('BIOMETRIC_SETUP_SKIPPED_FROM_POPUP');
    onComplete(false);
  };

  const getBiometricIcon = () => {
    if (biometricCapability.type === 'face') {
      return <FaceIdIcon size={48} color={theme.colors.primary} />;
    } else if (biometricCapability.type === 'fingerprint') {
      return <FingerprintIcon size={48} color={theme.colors.primary} />;
    } else {
      return <SecurityIcon size={48} color={theme.colors.primary} />;
    }
  };

  const getBiometricTitle = () => {
    if (biometricCapability.type === 'face') {
      return 'Enable Face ID';
    } else if (biometricCapability.type === 'fingerprint') {
      return 'Enable Fingerprint';
    } else {
      return 'Enable Biometric';
    }
  };

  const styles = StyleSheet.create({
    overlay: {
      flex: 1,
      backgroundColor: 'rgba(0, 0, 0, 0.5)',
      justifyContent: 'center',
      alignItems: 'center',
      padding: 20,
    },
    container: {
      backgroundColor: theme.colors.background,
      borderRadius: 20,
      padding: 24,
      width: '100%',
      maxWidth: 340,
      alignItems: 'center',
    },
    iconContainer: {
      width: 80,
      height: 80,
      borderRadius: 40,
      backgroundColor: isDark ? 'rgba(255, 255, 255, 0.1)' : 'rgba(0, 0, 0, 0.05)',
      justifyContent: 'center',
      alignItems: 'center',
      marginBottom: 20,
    },
    title: {
      fontSize: 20,
      fontWeight: '700',
      color: theme.colors.text,
      textAlign: 'center',
      marginBottom: 8,
    },
    subtitle: {
      fontSize: 16,
      color: theme.colors.text,
      textAlign: 'center',
      marginBottom: 24,
      opacity: 0.8,
      lineHeight: 22,
    },
    buttonContainer: {
      width: '100%',
      gap: 12,
    },
    enableButton: {
      backgroundColor: theme.colors.primary,
      borderRadius: 12,
      paddingVertical: 16,
      paddingHorizontal: 24,
      alignItems: 'center',
      justifyContent: 'center',
    },
    enableButtonText: {
      color: '#FFFFFF',
      fontSize: 16,
      fontWeight: '600',
    },
    skipButton: {
      backgroundColor: 'transparent',
      borderRadius: 12,
      paddingVertical: 16,
      paddingHorizontal: 24,
      alignItems: 'center',
      justifyContent: 'center',
      borderWidth: 1,
      borderColor: isDark ? 'rgba(255, 255, 255, 0.2)' : 'rgba(0, 0, 0, 0.2)',
    },
    skipButtonText: {
      color: theme.colors.text,
      fontSize: 16,
      fontWeight: '500',
      opacity: 0.8,
    },
    successContainer: {
      alignItems: 'center',
    },
    successIcon: {
      width: 80,
      height: 80,
      borderRadius: 40,
      backgroundColor: '#34C759',
      justifyContent: 'center',
      alignItems: 'center',
      marginBottom: 20,
    },
    successTitle: {
      fontSize: 20,
      fontWeight: '700',
      color: theme.colors.text,
      textAlign: 'center',
      marginBottom: 8,
    },
    successDescription: {
      fontSize: 16,
      color: theme.colors.text,
      textAlign: 'center',
      opacity: 0.8,
    },
  });

  return (
    <Modal
      visible={visible}
      transparent
      animationType="none"
      onRequestClose={handleSkip}
    >
      <View style={styles.overlay}>
        <Animated.View
          style={[
            styles.container,
            {
              transform: [{ scale: scaleAnimation }],
              opacity: fadeAnimation,
            },
          ]}
        >
          {setupComplete ? (
            <View style={styles.successContainer}>
              <View style={styles.successIcon}>
                <CheckIcon size={40} color="#FFFFFF" />
              </View>
              <Text style={styles.successTitle}>Biometric Enabled!</Text>
              <Text style={styles.successDescription}>
                Your account is now secured with biometric authentication
              </Text>
            </View>
          ) : biometricCapability.available ? (
            <>
              <View style={styles.iconContainer}>
                {getBiometricIcon()}
              </View>
              
              <Text style={styles.title}>{getBiometricTitle()}</Text>
              <Text style={styles.subtitle}>
                Setup biometric to login safer and make payments faster
              </Text>
              
              <View style={styles.buttonContainer}>
                <TouchableOpacity
                  style={styles.enableButton}
                  onPress={handleEnableBiometric}
                  disabled={loading}
                >
                  <Text style={styles.enableButtonText}>
                    {loading ? 'Setting up...' : 'Enable'}
                  </Text>
                </TouchableOpacity>
                
                <TouchableOpacity
                  style={styles.skipButton}
                  onPress={handleSkip}
                  disabled={loading}
                >
                  <Text style={styles.skipButtonText}>Not Now</Text>
                </TouchableOpacity>
              </View>
            </>
          ) : (
            <>
              <View style={styles.iconContainer}>
                <SecurityIcon size={48} color={theme.colors.text} />
              </View>
              
              <Text style={styles.title}>Biometric Not Available</Text>
              <Text style={styles.subtitle}>
                Biometric authentication is not available on this device
              </Text>
              
              <View style={styles.buttonContainer}>
                <TouchableOpacity
                  style={styles.skipButton}
                  onPress={handleSkip}
                >
                  <Text style={styles.skipButtonText}>Continue</Text>
                </TouchableOpacity>
              </View>
            </>
          )}
        </Animated.View>
      </View>
    </Modal>
  );
};

export default BiometricSetupPopup;
