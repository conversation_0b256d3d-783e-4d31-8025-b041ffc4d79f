import React from 'react';
import Svg, { Path } from 'react-native-svg';

interface ArrowUpIconProps {
  size?: number;
  color?: string;
}

const ArrowUpIcon: React.FC<ArrowUpIconProps> = ({
  size = 24,
  color = '#000000',
}) => {
  return (
    <Svg width={size} height={size} viewBox="0 0 24 24" fill="none">
      <Path
        d="M12 19V5M5 12l7-7 7 7"
        stroke={color}
        strokeWidth="2"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
    </Svg>
  );
};

export default ArrowUpIcon;