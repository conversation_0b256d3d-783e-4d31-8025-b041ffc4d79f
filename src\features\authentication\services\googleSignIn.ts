import { GoogleSignin, statusCodes } from '@react-native-google-signin/google-signin';
import { Platform } from 'react-native';
import apiService from '../../../infrastructure/api/apiService';

// Call this once in your app (e.g. in StartupScreen or App.tsx)
export const configureGoogleSignIn = () => {
  GoogleSignin.configure({
    webClientId: '1200340808-0ve3ueehjceva3v1ijofnppr7bbv5hhc.apps.googleusercontent.com', // from google-services.json
    iosClientId: '1200340808-2ieb7umolma3f43chobahpcdsbbkv27r.apps.googleusercontent.com', // from google-services.json
  });
};

export const signInWithGoogle = async () => {
  try {
    await GoogleSignin.hasPlayServices({ showPlayServicesUpdateDialog: true });
    const userInfo = await GoogleSignin.signIn();
    const idToken = userInfo.idToken;
    if (!idToken) throw new Error('No ID token returned from Google');

    // Send the idToken to your backend for verification and login
    const response = await apiService.post('/auth/google', { idToken });
    if (response.data?.status === 'success') {
      // Store tokens if needed
      if (response.data.data?.tokens) {
        const { accessToken, refreshToken } = response.data.data.tokens;
        await apiService.storeTokens(accessToken, refreshToken);
      }
      return { success: true, data: response.data.data };
    } else {
      throw new Error(response.data?.message || 'Google sign-in failed');
    }
  } catch (error: any) {
    if (error.code === statusCodes.SIGN_IN_CANCELLED) {
      return { success: false, error: 'Sign in cancelled' };
    } else if (error.code === statusCodes.IN_PROGRESS) {
      return { success: false, error: 'Sign in already in progress' };
    } else if (error.code === statusCodes.PLAY_SERVICES_NOT_AVAILABLE) {
      return { success: false, error: 'Play services not available or outdated' };
    } else {
      return { success: false, error: error.message || 'Google sign-in failed' };
    }
  }
};
