#!/usr/bin/env node

/**
 * Firebase Refactoring Validation Script
 * 
 * This script validates that all deprecated Firebase usage patterns have been
 * properly refactored to use the modular pattern with getApp().
 * 
 * Usage: node scripts/validateFirebaseRefactor.js
 */

const fs = require('fs');
const path = require('path');

// Colors for console output
const colors = {
  red: '\x1b[31m',
  green: '\x1b[32m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  magenta: '\x1b[35m',
  cyan: '\x1b[36m',
  white: '\x1b[37m',
  reset: '\x1b[0m'
};

const log = {
  error: (msg) => console.log(`${colors.red}❌ ${msg}${colors.reset}`),
  success: (msg) => console.log(`${colors.green}✅ ${msg}${colors.reset}`),
  warning: (msg) => console.log(`${colors.yellow}⚠️ ${msg}${colors.reset}`),
  info: (msg) => console.log(`${colors.blue}ℹ️ ${msg}${colors.reset}`),
  title: (msg) => console.log(`${colors.cyan}🔍 ${msg}${colors.reset}`)
};

// Patterns to check for deprecated usage
const deprecatedPatterns = [
  {
    pattern: /auth\(\)/g,
    description: 'Direct auth() call without getApp()',
    replacement: 'getAuth(getApp())'
  },
  {
    pattern: /messaging\(\)/g,
    description: 'Direct messaging() call without getApp()',
    replacement: 'getMessaging(getApp())'
  },
  {
    pattern: /import\s+auth\s+from\s+'@react-native-firebase\/auth'/g,
    description: 'Default import of auth (should use getAuth)',
    replacement: "import { getAuth } from '@react-native-firebase/auth'"
  },
  {
    pattern: /import\s+messaging\s+from\s+'@react-native-firebase\/messaging'/g,
    description: 'Default import of messaging (should use getMessaging)',
    replacement: "import { getMessaging } from '@react-native-firebase/messaging'"
  }
];

// Required modern patterns
const requiredPatterns = [
  {
    pattern: /getAuth\(.*getApp\(\).*\)/g,
    description: 'Modular auth usage with getApp()',
    file: 'src/config/initFirebase.ts'
  },
  {
    pattern: /getMessaging\(.*getApp\(\).*\)/g,
    description: 'Modular messaging usage with getApp()',
    file: 'src/config/initFirebase.ts'
  }
];

// Files to check (relative to project root)
const filesToCheck = [
  'src/config/initFirebase.ts',
  'src/services/notificationService.ts',
  'src/services/googleAuthService.ts',
  'src/components/GoogleSignInButton.tsx'
];

// Directories to scan for Firebase usage
const directoriesToScan = [
  'src/services',
  'src/components',
  'src/config',
  'src/utils'
];

function findFilesInDirectory(dir, extensions = ['.ts', '.tsx', '.js', '.jsx']) {
  const results = [];
  
  try {
    const files = fs.readdirSync(dir);
    
    for (const file of files) {
      const filePath = path.join(dir, file);
      const stat = fs.statSync(filePath);
      
      if (stat.isDirectory()) {
        results.push(...findFilesInDirectory(filePath, extensions));
      } else if (extensions.some(ext => file.endsWith(ext))) {
        results.push(filePath);
      }
    }
  } catch (error) {
    log.warning(`Could not read directory: ${dir}`);
  }
  
  return results;
}

function checkFileForPatterns(filePath, patterns, type = 'deprecated') {
  const issues = [];
  
  try {
    const content = fs.readFileSync(filePath, 'utf8');
    
    for (const patternInfo of patterns) {
      const matches = content.match(patternInfo.pattern);
      if (matches) {
        issues.push({
          file: filePath,
          pattern: patternInfo.pattern,
          description: patternInfo.description,
          replacement: patternInfo.replacement,
          matches: matches,
          type: type
        });
      }
    }
  } catch (error) {
    log.error(`Could not read file: ${filePath}`);
  }
  
  return issues;
}

function validateFirebaseInitialization() {
  log.title('Validating Firebase Initialization Timing Fixes');
  
  const initFirebasePath = 'src/config/initFirebase.ts';
  let content = '';
  
  try {
    content = fs.readFileSync(initFirebasePath, 'utf8');
  } catch (error) {
    log.error(`Could not read ${initFirebasePath}`);
    return false;
  }
  
  const checks = [
    {
      pattern: /await new Promise\(resolve => setTimeout\(resolve, 300\)\)/,
      description: 'Firebase initialization delay for timing issues'
    },
    {
      pattern: /getAuth\(app\)/,
      description: 'Modular auth usage'
    },
    {
      pattern: /getMessaging\(app\)/,
      description: 'Modular messaging usage'
    }
  ];
  
  let allChecksPass = true;
  
  for (const check of checks) {
    if (content.match(check.pattern)) {
      log.success(`Found: ${check.description}`);
    } else {
      log.error(`Missing: ${check.description}`);
      allChecksPass = false;
    }
  }
  
  return allChecksPass;
}

function generateReport(issues, checkedFiles) {
  console.log('\n' + '='.repeat(80));
  log.title('FIREBASE REFACTORING VALIDATION REPORT');
  console.log('='.repeat(80));
  
  log.info(`Checked ${checkedFiles.length} files`);
  
  const deprecatedIssues = issues.filter(issue => issue.type === 'deprecated');
  const missingModernIssues = issues.filter(issue => issue.type === 'missing');
  
  if (deprecatedIssues.length === 0) {
    log.success('No deprecated Firebase usage patterns found!');
  } else {
    log.error(`Found ${deprecatedIssues.length} deprecated usage patterns:`);
    
    deprecatedIssues.forEach((issue, index) => {
      console.log(`\n${index + 1}. ${issue.file}`);
      console.log(`   Pattern: ${issue.description}`);
      console.log(`   Replace with: ${issue.replacement || 'See documentation'}`);
      console.log(`   Matches: ${issue.matches.length}`);
    });
  }
  
  // Validate Firebase initialization
  const initValidation = validateFirebaseInitialization();
  
  console.log('\n' + '-'.repeat(40));
  log.title('SUMMARY');
  console.log('-'.repeat(40));
  
  if (deprecatedIssues.length === 0 && initValidation) {
    log.success('✅ ALL CHECKS PASSED! Firebase refactoring is complete.');
    console.log(`
${colors.green}🎉 REFACTORING SUCCESSFUL!${colors.reset}

The following improvements have been implemented:
- ✅ Replaced deprecated auth() with getAuth(getApp())
- ✅ Replaced deprecated messaging() with getMessaging(getApp())
- ✅ Added Firebase initialization timing delays (300ms)
- ✅ Updated all import statements to use modular pattern
- ✅ Enhanced error handling for Firebase initialization
- ✅ Added proper Firebase app instance management

Expected benefits:
- 🚀 No more deprecation warnings
- 🚀 Better Firebase initialization timing
- 🚀 Improved reliability and error handling
- 🚀 Future-proof Firebase usage
    `);
  } else {
    log.error('❌ ISSUES FOUND! Please review and fix the above issues.');
    
    if (deprecatedIssues.length > 0) {
      console.log(`\n${colors.yellow}Next steps:${colors.reset}`);
      console.log('1. Replace deprecated patterns with modern alternatives');
      console.log('2. Ensure all Firebase calls use getApp() pattern');
      console.log('3. Add initialization delays where needed');
      console.log('4. Test the app thoroughly after changes');
    }
  }
  
  return deprecatedIssues.length === 0 && initValidation;
}

function main() {
  console.log(`${colors.cyan}
╔═══════════════════════════════════════════════════════════════╗
║                                                               ║
║        🔥 FIREBASE REFACTORING VALIDATION SCRIPT 🔥          ║
║                                                               ║
║  This script validates the Firebase modular pattern          ║
║  refactoring to ensure no deprecated usage remains.          ║
║                                                               ║
╚═══════════════════════════════════════════════════════════════╝
${colors.reset}`);

  let allFiles = [];
  let allIssues = [];
  
  // Check specific files
  log.title('Checking specific critical files...');
  for (const file of filesToCheck) {
    if (fs.existsSync(file)) {
      log.info(`Checking: ${file}`);
      allFiles.push(file);
      allIssues.push(...checkFileForPatterns(file, deprecatedPatterns, 'deprecated'));
    } else {
      log.warning(`File not found: ${file}`);
    }
  }
  
  // Scan directories for additional Firebase usage
  log.title('Scanning directories for Firebase usage...');
  for (const dir of directoriesToScan) {
    if (fs.existsSync(dir)) {
      const files = findFilesInDirectory(dir);
      for (const file of files) {
        if (!allFiles.includes(file)) {
          const content = fs.readFileSync(file, 'utf8');
          if (content.includes('@react-native-firebase')) {
            log.info(`Found Firebase usage in: ${file}`);
            allFiles.push(file);
            allIssues.push(...checkFileForPatterns(file, deprecatedPatterns, 'deprecated'));
          }
        }
      }
    }
  }
  
  // Generate final report
  const success = generateReport(allIssues, allFiles);
  
  process.exit(success ? 0 : 1);
}

if (require.main === module) {
  main();
}

module.exports = {
  checkFileForPatterns,
  deprecatedPatterns,
  requiredPatterns
};
