"""
PayVendy AI Brain Configuration Settings

Manages all configuration for the AI Brain system including database connection,
AI parameters, and operational settings.
"""

import os
from dataclasses import dataclass
from typing import Dict, Any
from pathlib import Path
from dotenv import load_dotenv


@dataclass
class DatabaseConfig:
    """Database connection configuration."""
    url: str
    min_pool_size: int = 5
    max_pool_size: int = 20
    pool_timeout: int = 30
    statement_timeout: int = 60


@dataclass
class AIModelConfig:
    """AI model configuration parameters."""
    # Behavioral analysis parameters
    transaction_analysis_window_days: int = 30
    suspicious_threshold: float = 0.8
    engagement_threshold: float = 0.6
    
    # Reward system parameters
    base_reward_amount: float = 10.0
    max_daily_rewards: int = 5
    reward_cooldown_hours: int = 24
    
    # Segmentation parameters
    high_value_threshold: float = 1000.0
    frequent_user_tx_threshold: int = 10
    
    # Risk assessment parameters
    risk_velocity_threshold: float = 500.0
    risk_pattern_threshold: int = 3


@dataclass
class OperationalConfig:
    """Operational configuration for the AI Brain."""
    # Processing intervals
    analysis_interval_seconds: int = 300  # 5 minutes
    reward_processing_interval_seconds: int = 600  # 10 minutes
    cleanup_interval_hours: int = 24
    
    # Data retention
    log_retention_days: int = 90
    analytics_retention_days: int = 365
    
    # Concurrency
    max_concurrent_operations: int = 10
    batch_size: int = 100
    
    # Monitoring
    enable_performance_monitoring: bool = True
    enable_detailed_logging: bool = False


class AIBrainConfig:
    """Central configuration manager for PayVendy AI Brain."""
    
    def __init__(self):
        self._load_env_file()
        self._load_config()
    
    def _load_env_file(self) -> None:
        """Load environment variables from .env file."""
        # Get the directory where this config file is located
        config_dir = Path(__file__).parent
        # Look for .env file in the ai_brain directory (parent of config)
        env_file = config_dir.parent / '.env'
        
        if env_file.exists():
            load_dotenv(env_file)
        else:
            # Also try looking in the current working directory
            cwd_env = Path.cwd() / '.env'
            if cwd_env.exists():
                load_dotenv(cwd_env)
    
    def _load_config(self) -> None:
        """Load configuration from environment variables."""
        # Database configuration
        self.database = DatabaseConfig(
            url=self._get_env_required('DATABASE_URL'),
            min_pool_size=int(self._get_env('DB_MIN_POOL_SIZE', '5')),
            max_pool_size=int(self._get_env('DB_MAX_POOL_SIZE', '20')),
            pool_timeout=int(self._get_env('DB_POOL_TIMEOUT', '30')),
            statement_timeout=int(self._get_env('DB_STATEMENT_TIMEOUT', '60'))
        )
        
        # AI model configuration
        self.ai_model = AIModelConfig(
            transaction_analysis_window_days=int(self._get_env('AI_TRANSACTION_WINDOW_DAYS', '30')),
            suspicious_threshold=float(self._get_env('AI_SUSPICIOUS_THRESHOLD', '0.8')),
            engagement_threshold=float(self._get_env('AI_ENGAGEMENT_THRESHOLD', '0.6')),
            base_reward_amount=float(self._get_env('AI_BASE_REWARD_AMOUNT', '10.0')),
            max_daily_rewards=int(self._get_env('AI_MAX_DAILY_REWARDS', '5')),
            reward_cooldown_hours=int(self._get_env('AI_REWARD_COOLDOWN_HOURS', '24')),
            high_value_threshold=float(self._get_env('AI_HIGH_VALUE_THRESHOLD', '1000.0')),
            frequent_user_tx_threshold=int(self._get_env('AI_FREQUENT_USER_TX_THRESHOLD', '10')),
            risk_velocity_threshold=float(self._get_env('AI_RISK_VELOCITY_THRESHOLD', '500.0')),
            risk_pattern_threshold=int(self._get_env('AI_RISK_PATTERN_THRESHOLD', '3'))
        )
        
        # Operational configuration
        self.operational = OperationalConfig(
            analysis_interval_seconds=int(self._get_env('AI_ANALYSIS_INTERVAL_SECONDS', '300')),
            reward_processing_interval_seconds=int(self._get_env('AI_REWARD_INTERVAL_SECONDS', '600')),
            cleanup_interval_hours=int(self._get_env('AI_CLEANUP_INTERVAL_HOURS', '24')),
            log_retention_days=int(self._get_env('AI_LOG_RETENTION_DAYS', '90')),
            analytics_retention_days=int(self._get_env('AI_ANALYTICS_RETENTION_DAYS', '365')),
            max_concurrent_operations=int(self._get_env('AI_MAX_CONCURRENT_OPS', '10')),
            batch_size=int(self._get_env('AI_BATCH_SIZE', '100')),
            enable_performance_monitoring=self._get_env('AI_ENABLE_PERFORMANCE_MONITORING', 'true').lower() == 'true',
            enable_detailed_logging=self._get_env('AI_ENABLE_DETAILED_LOGGING', 'false').lower() == 'true'
        )
        
        # Environment settings
        self.environment = self._get_env('NODE_ENV', 'development')
        self.is_production = self.environment == 'production'
        self.is_development = self.environment == 'development'
        
        # Logging configuration
        self.log_level = self._get_env('AI_LOG_LEVEL', 'INFO' if self.is_production else 'DEBUG')
        self.enable_file_logging = self._get_env('AI_ENABLE_FILE_LOGGING', 'true').lower() == 'true'
        self.log_file_path = self._get_env('AI_LOG_FILE_PATH', 'logs/ai_brain.log')
        
        # Security settings
        self.enable_encryption = self._get_env('AI_ENABLE_ENCRYPTION', 'true').lower() == 'true'
        self.encryption_key = self._get_env('AI_ENCRYPTION_KEY', '')
        
        # Feature flags
        self.feature_flags = self._parse_feature_flags()
    
    def _get_env(self, key: str, default: str = '') -> str:
        """Get environment variable with default value."""
        return os.getenv(key, default)
    
    def _get_env_required(self, key: str) -> str:
        """Get required environment variable, raise error if missing."""
        value = os.getenv(key)
        if not value:
            raise ValueError(f"Required environment variable '{key}' is not set")
        return value
    
    def _parse_feature_flags(self) -> Dict[str, bool]:
        """Parse feature flags from environment."""
        flags = {}
        feature_flags_str = self._get_env('AI_FEATURE_FLAGS', '')
        
        if feature_flags_str:
            for flag in feature_flags_str.split(','):
                flag = flag.strip()
                if '=' in flag:
                    key, value = flag.split('=', 1)
                    flags[key.strip()] = value.strip().lower() == 'true'
                else:
                    flags[flag] = True
        
        return flags
    
    def get_feature_flag(self, flag_name: str, default: bool = False) -> bool:
        """Get feature flag value."""
        return self.feature_flags.get(flag_name, default)
    
    def validate_config(self) -> None:
        """Validate configuration settings."""
        errors = []
        
        # Validate database URL
        if not self.database.url:
            errors.append("DATABASE_URL is required")
        
        # Validate AI parameters
        if self.ai_model.suspicious_threshold < 0 or self.ai_model.suspicious_threshold > 1:
            errors.append("AI_SUSPICIOUS_THRESHOLD must be between 0 and 1")
        
        if self.ai_model.engagement_threshold < 0 or self.ai_model.engagement_threshold > 1:
            errors.append("AI_ENGAGEMENT_THRESHOLD must be between 0 and 1")
        
        if self.ai_model.base_reward_amount <= 0:
            errors.append("AI_BASE_REWARD_AMOUNT must be positive")
        
        # Validate operational parameters
        if self.operational.analysis_interval_seconds < 60:
            errors.append("AI_ANALYSIS_INTERVAL_SECONDS must be at least 60 seconds")
        
        if self.operational.batch_size <= 0:
            errors.append("AI_BATCH_SIZE must be positive")
        
        if errors:
            raise ValueError(f"Configuration validation failed: {'; '.join(errors)}")
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert configuration to dictionary for logging/debugging."""
        return {
            'database': {
                'url': '***REDACTED***' if self.database.url else None,
                'min_pool_size': self.database.min_pool_size,
                'max_pool_size': self.database.max_pool_size,
                'pool_timeout': self.database.pool_timeout,
                'statement_timeout': self.database.statement_timeout
            },
            'ai_model': {
                'transaction_analysis_window_days': self.ai_model.transaction_analysis_window_days,
                'suspicious_threshold': self.ai_model.suspicious_threshold,
                'engagement_threshold': self.ai_model.engagement_threshold,
                'base_reward_amount': self.ai_model.base_reward_amount,
                'max_daily_rewards': self.ai_model.max_daily_rewards,
                'reward_cooldown_hours': self.ai_model.reward_cooldown_hours,
                'high_value_threshold': self.ai_model.high_value_threshold,
                'frequent_user_tx_threshold': self.ai_model.frequent_user_tx_threshold,
                'risk_velocity_threshold': self.ai_model.risk_velocity_threshold,
                'risk_pattern_threshold': self.ai_model.risk_pattern_threshold
            },
            'operational': {
                'analysis_interval_seconds': self.operational.analysis_interval_seconds,
                'reward_processing_interval_seconds': self.operational.reward_processing_interval_seconds,
                'cleanup_interval_hours': self.operational.cleanup_interval_hours,
                'log_retention_days': self.operational.log_retention_days,
                'analytics_retention_days': self.operational.analytics_retention_days,
                'max_concurrent_operations': self.operational.max_concurrent_operations,
                'batch_size': self.operational.batch_size,
                'enable_performance_monitoring': self.operational.enable_performance_monitoring,
                'enable_detailed_logging': self.operational.enable_detailed_logging
            },
            'environment': self.environment,
            'log_level': self.log_level,
            'feature_flags': self.feature_flags
        }


# Global configuration instance - will be initialized when first accessed
_config_instance = None

def get_config() -> AIBrainConfig:
    """Get the global configuration instance, creating it if necessary."""
    global _config_instance
    if _config_instance is None:
        _config_instance = AIBrainConfig()
    return _config_instance

# For backward compatibility
config = None
