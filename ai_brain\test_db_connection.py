#!/usr/bin/env python3
"""
Database Connection Test Script

Simple script to test database connectivity before starting the AI Brain.
"""

import asyncio
import asyncpg
import sys
from pathlib import Path

# Add the ai_brain directory to Python path
sys.path.insert(0, str(Path(__file__).parent))

from config.settings import get_config


async def test_database_connection():
    """Test database connection with detailed diagnostics."""
    print("Testing database connection...")
    print("=" * 50)
    
    try:
        # Load configuration
        config = get_config()
        db_url = config.database.url
        
        print(f"Database URL: {db_url[:50]}...")
        print(f"Min Pool Size: {config.database.min_pool_size}")
        print(f"Max Pool Size: {config.database.max_pool_size}")
        print(f"Pool Timeout: {config.database.pool_timeout}")
        print(f"Statement Timeout: {config.database.statement_timeout}")
        print()
        
        # Test basic connection
        print("Step 1: Testing basic connection...")
        try:
            conn = await asyncpg.connect(db_url)
            print("✓ Basic connection successful")
            
            # Test simple query
            print("Step 2: Testing simple query...")
            result = await conn.fetchval('SELECT 1')
            print(f"✓ Simple query successful: {result}")
            
            # Test database version
            print("Step 3: Getting database version...")
            version = await conn.fetchval('SELECT version()')
            print(f"✓ Database version: {version[:100]}...")
            
            # Test if required tables exist
            print("Step 4: Checking required tables...")
            tables_query = """
                SELECT table_name 
                FROM information_schema.tables 
                WHERE table_schema = 'public' 
                AND table_name IN ('users', 'transactions', 'behavior_analytics', 'user_segments', 'reward_queue', 'ai_logs')
                ORDER BY table_name
            """
            tables = await conn.fetch(tables_query)
            
            required_tables = {'users', 'transactions', 'behavior_analytics', 'user_segments', 'reward_queue', 'ai_logs'}
            existing_tables = {row['table_name'] for row in tables}
            
            print(f"✓ Found tables: {', '.join(sorted(existing_tables))}")
            
            missing_tables = required_tables - existing_tables
            if missing_tables:
                print(f"⚠ Missing tables: {', '.join(sorted(missing_tables))}")
                print("  Note: Some AI Brain features may not work without these tables")
            else:
                print("✓ All required tables found")
            
            await conn.close()
            print()
            print("✓ Database connection test PASSED")
            return True
            
        except Exception as e:
            print(f"✗ Connection failed: {str(e)}")
            return False
            
    except Exception as e:
        print(f"✗ Configuration error: {str(e)}")
        return False


async def test_connection_pool():
    """Test connection pool functionality."""
    print("\nTesting connection pool...")
    print("=" * 50)
    
    try:
        config = get_config()
        
        print("Step 1: Creating connection pool...")
        pool = await asyncpg.create_pool(
            config.database.url,
            min_size=2,
            max_size=5,
            command_timeout=30
        )
        print("✓ Connection pool created")
        
        print("Step 2: Testing pool acquisition...")
        async with pool.acquire() as conn:
            result = await conn.fetchval('SELECT 1')
            print(f"✓ Pool connection test successful: {result}")
        
        print("Step 3: Testing concurrent connections...")
        async def test_concurrent_query(query_id):
            async with pool.acquire() as conn:
                result = await conn.fetchval(f'SELECT {query_id}')
                return result
        
        tasks = [test_concurrent_query(i) for i in range(1, 4)]
        results = await asyncio.gather(*tasks)
        print(f"✓ Concurrent queries successful: {results}")
        
        await pool.close()
        print("✓ Connection pool test PASSED")
        return True
        
    except Exception as e:
        print(f"✗ Connection pool test failed: {str(e)}")
        return False


async def main():
    """Main test function."""
    print("PayVendy AI Brain - Database Connection Test")
    print("=" * 60)
    
    # Test basic connection
    basic_test = await test_database_connection()
    
    if basic_test:
        # Test connection pool
        pool_test = await test_connection_pool()
        
        if pool_test:
            print("\n" + "=" * 60)
            print("✓ ALL TESTS PASSED - Database is ready for AI Brain")
            print("=" * 60)
            return True
    
    print("\n" + "=" * 60)
    print("✗ TESTS FAILED - Please fix database issues before starting AI Brain")
    print("=" * 60)
    print("\nTroubleshooting tips:")
    print("1. Check if your database server is running")
    print("2. Verify DATABASE_URL in .env file")
    print("3. Ensure network connectivity to the database")
    print("4. Check database credentials")
    print("5. For Supabase: ensure your project is not paused")
    return False


if __name__ == "__main__":
    try:
        success = asyncio.run(main())
        sys.exit(0 if success else 1)
    except KeyboardInterrupt:
        print("\nTest interrupted by user")
        sys.exit(0)
    except Exception as e:
        print(f"Test failed with error: {str(e)}")
        sys.exit(1)