const express = require('express');
const helmet = require('helmet');
const cors = require('cors');
const compression = require('compression');
const morgan = require('morgan');
const rateLimit = require('express-rate-limit');
const mongoSanitize = require('express-mongo-sanitize');
const xss = require('xss');
const hpp = require('hpp');
require('dotenv').config();

const { connectDB } = require('./config/database');
const errorHandler = require('./middleware/errorHandler');
const logger = require('./utils/logger');
const realtimeService = require('./services/realtimeService');
const userExistenceMiddleware = require('./middleware/userExistenceMiddleware');
const aiService = require('./services/aiService');

// Import routes
const authRoutes = require('./routes/auth');
const googleAuthRoutes = require('./routes/googleAuth');
const userRoutes = require('./routes/user');
const smsRoutes = require('./routes/sms');
const emailRoutes = require('./routes/email');
const setupRoutes = require('./routes/setup');
const featureFlagRoutes = require('./routes/featureFlags');
const networkDetect = require('./routes/networkDetect');
const notificationRoutes = require('./routes/notification');
const healthRoutes = require('./routes/health');
const aiRoutes = require('./routes/ai');

const app = express();

// Connect to database
connectDB();

// Initialize realtime service for user deletion detection
realtimeService.initialize().then(() => {
  logger.info('Realtime service initialized successfully');
}).catch(error => {
  logger.error('Failed to initialize realtime service:', error);
  logger.warn('Server will continue running with polling fallback for user deletion detection');
});

// Initialize AI service
aiService.initialize().then(() => {
  logger.info('AI service initialized successfully');
}).catch(error => {
  logger.error('Failed to initialize AI service:', error);
  logger.warn('Server will continue running without AI integration');
});

// Add request logging middleware
app.use((req, res, next) => {
  console.log(`🌐 [SERVER] ${req.method} ${req.originalUrl}`);
  console.log('📋 Headers:', JSON.stringify(req.headers, null, 2));
  if (req.body && Object.keys(req.body).length > 0) {
    console.log('📦 Body:', JSON.stringify(req.body, null, 2));
  }
  next();
});

// Security middleware
app.use(helmet({
  contentSecurityPolicy: {
    directives: {
      defaultSrc: ["'self'"],
      styleSrc: ["'self'", "'unsafe-inline'"],
      scriptSrc: ["'self'"],
      imgSrc: ["'self'", "data:", "https:"],
    },
  },
  hsts: {
    maxAge: 31536000,
    includeSubDomains: true,
    preload: true
  }
}));

// CORS configuration
const corsOptions = {
  origin: function (origin, callback) {
    const allowedOrigins = process.env.ALLOWED_ORIGINS?.split(',') || ['http://localhost:3000'];
    if (!origin || allowedOrigins.indexOf(origin) !== -1) {
      callback(null, true);
    } else {
      callback(new Error('Not allowed by CORS'));
    }
  },
  credentials: true,
  optionsSuccessStatus: 200
};
app.use(cors(corsOptions));

// Trust proxy for rate limiting (required for tunnels and proxies)
// Use specific proxy configuration for better security
app.set('trust proxy', 1);

// Rate limiting
const limiter = rateLimit({
  windowMs: parseInt(process.env.RATE_LIMIT_WINDOW_MS) || 15 * 60 * 1000, // 15 minutes
  max: parseInt(process.env.RATE_LIMIT_MAX_REQUESTS) || 100, // limit each IP to 100 requests per windowMs
  message: {
    error: 'Too many requests from this IP, please try again later.',
    retryAfter: Math.ceil((parseInt(process.env.RATE_LIMIT_WINDOW_MS) || 900000) / 1000)
  },
  standardHeaders: true,
  legacyHeaders: false,
});
app.use('/api/', limiter);

// Body parsing middleware
app.use(express.json({ limit: '10mb' }));
app.use(express.urlencoded({ extended: true, limit: '10mb' }));

// Data sanitization
app.use(mongoSanitize()); // Against NoSQL injection
app.use(hpp()); // Prevent parameter pollution

// Compression middleware
app.use(compression());

// Logging middleware
if (process.env.NODE_ENV === 'development') {
  app.use(morgan('dev'));
} else {
  app.use(morgan('combined', {
    stream: {
      write: (message) => logger.info(message.trim())
    }
  }));
}

// Health check endpoints
app.use('/health', healthRoutes);
app.use(`/api/${process.env.API_VERSION || 'v1'}/health`, healthRoutes);

// API routes
app.use(`/api/${process.env.API_VERSION || 'v1'}/auth`, authRoutes);
app.use(`/api/${process.env.API_VERSION || 'v1'}/auth`, googleAuthRoutes);

// Apply user existence middleware to authenticated routes
app.use(`/api/${process.env.API_VERSION || 'v1'}/user`, userExistenceMiddleware.checkUserExists, userRoutes);
app.use(`/api/${process.env.API_VERSION || 'v1'}/sms`, userExistenceMiddleware.checkUserExists, smsRoutes);
app.use(`/api/${process.env.API_VERSION || 'v1'}/email`, emailRoutes); // Email routes handle their own auth
app.use(`/api/${process.env.API_VERSION || 'v1'}/setup`, userExistenceMiddleware.checkUserExists, setupRoutes);
app.use(`/api/${process.env.API_VERSION || 'v1'}`, userExistenceMiddleware.checkUserExists, featureFlagRoutes);
app.use('/api/network-detect', networkDetect);
app.use(`/api/${process.env.API_VERSION || 'v1'}/notification`, userExistenceMiddleware.checkUserExists, notificationRoutes);
app.use(`/api/${process.env.API_VERSION || 'v1'}/ai`, aiRoutes);

// Also add routes without version for backward compatibility
app.use(`/api/auth`, authRoutes);
app.use(`/api/auth`, googleAuthRoutes);
app.use(`/api/user`, userExistenceMiddleware.checkUserExists, userRoutes);
app.use(`/api/sms`, userExistenceMiddleware.checkUserExists, smsRoutes);
app.use(`/api/email`, emailRoutes); // Email routes handle their own auth
app.use(`/api/setup`, userExistenceMiddleware.checkUserExists, setupRoutes);

// 404 handler
app.all('*', (req, res) => {
  res.status(404).json({
    status: 'error',
    message: `Route ${req.originalUrl} not found`
  });
});

// Global error handler
app.use(errorHandler);

const PORT = process.env.PORT || 3000;

const server = app.listen(PORT, () => {
  logger.info(`Vendy API server running on port ${PORT} in ${process.env.NODE_ENV} mode`);
});

// Graceful shutdown
process.on('SIGTERM', () => {
  logger.info('SIGTERM received. Shutting down gracefully...');
  server.close(async () => {
    // Cleanup realtime service
    await realtimeService.cleanup();
    logger.info('Process terminated');
    process.exit(0);
  });
});

process.on('SIGINT', () => {
  logger.info('SIGINT received. Shutting down gracefully...');
  server.close(async () => {
    // Cleanup realtime service
    await realtimeService.cleanup();
    logger.info('Process terminated');
    process.exit(0);
  });
});

module.exports = app;
