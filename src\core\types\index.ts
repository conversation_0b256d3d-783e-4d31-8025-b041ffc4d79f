/**
 * Core Types for Micro-Frontend Architecture
 * 
 * This file contains all the shared types and interfaces used across
 * different feature modules in the application.
 */

// ==================== DOMAIN ENTITIES ====================

export interface User {
  id: string;
  email?: string;
  phoneNumber?: string;
  firstName?: string;
  lastName?: string;
  avatar?: string;
  isVerified: boolean;
  isEmailVerified: boolean;
  isPhoneVerified: boolean;
  createdAt: string;
  updatedAt: string;
  preferences: UserPreferences;
  profile: UserProfile;
  securitySettings: SecuritySettings;
}

export interface UserPreferences {
  theme: 'light' | 'dark' | 'system';
  language: string;
  currency: string;
  notifications: NotificationSettings;
  privacy: PrivacySettings;
}

export interface NotificationSettings {
  push: boolean;
  email: boolean;
  sms: boolean;
  marketing: boolean;
  security: boolean;
  transactional: boolean;
}

export interface PrivacySettings {
  profileVisibility: 'public' | 'private';
  showOnlineStatus: boolean;
  allowDataCollection: boolean;
  shareAnalytics: boolean;
}

export interface UserProfile {
  bio?: string;
  dateOfBirth?: string;
  gender?: string;
  location?: string;
  occupation?: string;
  website?: string;
  socialLinks?: SocialLinks;
}

export interface SocialLinks {
  twitter?: string;
  linkedin?: string;
  instagram?: string;
  facebook?: string;
}

export interface SecuritySettings {
  biometricEnabled: boolean;
  pinEnabled: boolean;
  twoFactorEnabled: boolean;
  sessionTimeout: number;
  loginNotifications: boolean;
  deviceTrust: DeviceTrustSettings;
}

export interface DeviceTrustSettings {
  trustCurrentDevice: boolean;
  trustedDevices: TrustedDevice[];
  requireApprovalForNewDevices: boolean;
}

export interface TrustedDevice {
  id: string;
  name: string;
  type: 'mobile' | 'desktop' | 'tablet';
  lastUsed: string;
  location?: string;
  isCurrentDevice: boolean;
}

// ==================== FINANCIAL ENTITIES ====================

export interface Transaction {
  id: string;
  type: TransactionType;
  amount: number;
  currency: string;
  status: TransactionStatus;
  description: string;
  reference: string;
  metadata: TransactionMetadata;
  createdAt: string;
  updatedAt: string;
  completedAt?: string;
  failedAt?: string;
  recipient?: TransactionParticipant;
  sender?: TransactionParticipant;
}

export type TransactionType = 
  | 'airtime' 
  | 'data' 
  | 'bills' 
  | 'transfer' 
  | 'deposit' 
  | 'withdrawal'
  | 'payment'
  | 'refund';

export type TransactionStatus = 
  | 'pending' 
  | 'processing'
  | 'success' 
  | 'failed' 
  | 'cancelled'
  | 'expired'
  | 'refunded';

export interface TransactionMetadata {
  provider?: string;
  phoneNumber?: string;
  accountNumber?: string;
  billerCode?: string;
  paymentMethod?: string;
  fees?: TransactionFee[];
  exchangeRate?: number;
  originalAmount?: number;
  originalCurrency?: string;
  [key: string]: any;
}

export interface TransactionFee {
  type: 'processing' | 'service' | 'network' | 'exchange';
  amount: number;
  currency: string;
  description: string;
}

export interface TransactionParticipant {
  id: string;
  name: string;
  type: 'user' | 'merchant' | 'system' | 'external';
  identifier: string; // phone, email, account number, etc.
}

export interface Balance {
  available: number;
  pending: number;
  currency: string;
  lastUpdated: string;
  breakdown?: BalanceBreakdown;
}

export interface BalanceBreakdown {
  main: number;
  bonus: number;
  cashback: number;
  referral: number;
  promotional: number;
}

// ==================== SERVICE INTERFACES ====================

export interface ApiResponse<T = any> {
  data: T;
  status: number;
  message?: string;
  errors?: string[];
  metadata?: {
    pagination?: PaginationInfo;
    timestamp: string;
    requestId: string;
  };
}

export interface PaginationInfo {
  page: number;
  limit: number;
  total: number;
  totalPages: number;
  hasNext: boolean;
  hasPrev: boolean;
}

export interface ApiError extends Error {
  status?: number;
  code?: string;
  response?: any;
  userDeleted?: boolean;
  redirectToSignup?: boolean;
}

// ==================== UI STATE TYPES ====================

export interface LoadingState {
  [key: string]: boolean;
}

export interface ErrorState {
  [key: string]: string | null;
}

export interface UIState {
  theme: 'light' | 'dark' | 'system';
  isLoading: boolean;
  loadingStates: LoadingState;
  errors: ErrorState;
  currentScreen: string;
  navigationHistory: string[];
  animationsEnabled: boolean;
  cacheEnabled: boolean;
  offlineMode: boolean;
}

// ==================== NAVIGATION TYPES ====================

export type RootStackParamList = {
  // Authentication Flow
  Splash: undefined;
  Startup: undefined;
  PhoneInput: undefined;
  EmailInput: undefined;
  EmailVerification: { email: string };
  Verification: { phoneNumber?: string };
  
  // Setup Flow
  AvatarSelection: { userData?: any };
  NameSetup: { userData?: any };
  PinSetup: { userData?: any };
  PinVerification: { user?: any };

  SetupComplete: { userData?: any };
  SetupLoading: { next?: keyof RootStackParamList } | undefined;

  // Main App
  MainTabs: undefined;
  Home: undefined;
  Profile: { userId: string };
  Settings: undefined;
  Security: undefined;
  Appearance: undefined;
  ReferEarn: undefined;
  
  // Services
  Airtime: undefined;
  DataPurchase: undefined;
  BillPayment: undefined;
  Transfer: undefined;
  
  // History & Transactions
  TransactionDetails: { transactionId: string };
  TransactionHistory: undefined;
};

// ==================== FEATURE MODULE INTERFACES ====================

export interface FeatureModule {
  name: string;
  version: string;
  dependencies: string[];
  initialize(): Promise<void>;
  cleanup(): Promise<void>;
  getRoutes(): any[];
  getServices(): any[];
}

export interface ModuleRegistry {
  register(module: FeatureModule): void;
  unregister(moduleName: string): void;
  getModule(moduleName: string): FeatureModule | undefined;
  getAllModules(): FeatureModule[];
  initializeAll(): Promise<void>;
  cleanupAll(): Promise<void>;
}

// ==================== CACHE TYPES ====================

export interface CacheEntry<T = any> {
  data: T;
  timestamp: number;
  expiresAt: number;
  version: string;
}

export interface CacheConfig {
  defaultTTL: number;
  maxSize: number;
  enableCompression: boolean;
  enableEncryption: boolean;
}

// ==================== PERFORMANCE TYPES ====================

export interface PerformanceMetric {
  id: string;
  name: string;
  startTime: number;
  endTime?: number;
  duration?: number;
  metadata?: Record<string, any>;
  timestamp: string;
}

export interface MemoryMetric {
  timestamp: string;
  jsHeapSizeUsed?: number;
  jsHeapSizeTotal?: number;
  jsHeapSizeLimit?: number;
}

// ==================== SECURITY TYPES ====================

export interface SecurityThreat {
  type: 'debugging' | 'rooting' | 'tampering' | 'hooking' | 'emulator' | 'suspicious_behavior';
  severity: 'low' | 'medium' | 'high' | 'critical';
  description: string;
  timestamp: string;
  metadata?: Record<string, any>;
}

export interface ValidationResult {
  isValid: boolean;
  sanitizedValue: string;
  errors: string[];
  warnings: string[];
}

export interface SecurityValidationResult extends ValidationResult {
  riskLevel: 'low' | 'medium' | 'high';
  securityFlags: string[];
}

// ==================== UTILITY TYPES ====================

export type DeepPartial<T> = {
  [P in keyof T]?: T[P] extends object ? DeepPartial<T[P]> : T[P];
};

export type RequiredFields<T, K extends keyof T> = T & Required<Pick<T, K>>;

export type OptionalFields<T, K extends keyof T> = Omit<T, K> & Partial<Pick<T, K>>;

export type Nullable<T> = T | null;

export type Optional<T> = T | undefined;

// ==================== EVENT TYPES ====================

export interface AppEvent {
  type: string;
  payload?: any;
  timestamp: string;
  source: string;
}

export interface EventSubscription {
  unsubscribe(): void;
}

export interface EventEmitter {
  emit(event: AppEvent): void;
  on(eventType: string, handler: (event: AppEvent) => void): EventSubscription;
  off(eventType: string, handler: (event: AppEvent) => void): void;
  removeAllListeners(eventType?: string): void;
}

// ==================== EXPORTS ====================

export * from '../../navigation/navigation';
