"""
PayVendy AI Brain Intelligence Engine

Core AI logic for autonomous user analysis, behavior prediction,
reward allocation, and fraud detection.
"""

import asyncio
import json
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Tuple, Any
from dataclasses import dataclass, asdict
import logging
from enum import Enum

from config.settings import get_config
from services.database import DatabaseService


logger = logging.getLogger(__name__)


class RiskLevel(Enum):
    """Risk assessment levels."""
    LOW = "low"
    MEDIUM = "medium"
    HIGH = "high"
    CRITICAL = "critical"


class UserSegment(Enum):
    """User segmentation categories."""
    NEW_USER = "new_user"
    CASUAL_USER = "casual_user"
    ACTIVE_USER = "active_user"
    HIGH_VALUE_USER = "high_value_user"
    VIP_USER = "vip_user"
    DORMANT_USER = "dormant_user"
    AT_RISK_USER = "at_risk_user"


class RewardType(Enum):
    """Types of rewards the AI can allocate."""
    WELCOME_BONUS = "welcome_bonus"
    ENGAGEMENT_REWARD = "engagement_reward"
    LOYALTY_BONUS = "loyalty_bonus"
    ACHIEVEMENT_UNLOCK = "achievement_unlock"
    RECOVERY_INCENTIVE = "recovery_incentive"
    HIGH_VALUE_BONUS = "high_value_bonus"


@dataclass
class UserAnalysis:
    """Result of user behavioral analysis."""
    user_id: str
    segment: UserSegment
    risk_level: RiskLevel
    engagement_score: float
    transaction_velocity: float
    lifetime_value: float
    days_since_last_activity: int
    suspicious_patterns: List[str]
    recommended_actions: List[Dict[str, Any]]
    confidence_score: float


@dataclass
class RewardDecision:
    """AI decision for reward allocation."""
    user_id: str
    reward_type: RewardType
    amount: float
    reason: str
    confidence: float
    expires_at: Optional[datetime] = None


class AIIntelligenceEngine:
    """Core AI intelligence engine for autonomous decision making."""
    
    def __init__(self, db_service: DatabaseService):
        self.db = db_service
        self.logger = logging.getLogger(f"{__name__}.{self.__class__.__name__}")
    
    async def analyze_user_behavior(self, user_id: str) -> UserAnalysis:
        """
        Comprehensive analysis of user behavior patterns.
        
        Args:
            user_id: Target user ID for analysis
            
        Returns:
            UserAnalysis object with insights and recommendations
        """
        try:
            self.logger.debug(f"Starting behavioral analysis for user {user_id}")
            
            # Gather user data
            user_data = await self._gather_user_data(user_id)
            if not user_data:
                raise ValueError(f"No data found for user {user_id}")
            
            # Calculate engagement metrics
            engagement_score = await self._calculate_engagement_score(user_id, user_data)
            
            # Analyze transaction patterns
            transaction_analysis = await self._analyze_transaction_patterns(user_id)
            
            # Risk assessment
            risk_assessment = await self._assess_user_risk(user_id, user_data, transaction_analysis)
            
            # Segment classification
            segment = await self._classify_user_segment(user_id, user_data, engagement_score)
            
            # Generate recommendations
            recommendations = await self._generate_recommendations(
                user_id, segment, engagement_score, risk_assessment
            )
            
            analysis = UserAnalysis(
                user_id=user_id,
                segment=segment,
                risk_level=risk_assessment['level'],
                engagement_score=engagement_score,
                transaction_velocity=transaction_analysis['velocity'],
                lifetime_value=transaction_analysis['lifetime_value'],
                days_since_last_activity=user_data['days_since_last_activity'],
                suspicious_patterns=risk_assessment['patterns'],
                recommended_actions=recommendations,
                confidence_score=self._calculate_analysis_confidence(user_data, transaction_analysis)
            )
            
            # Log analysis to database
            await self._log_analysis(analysis)
            
            self.logger.info(f"Completed behavioral analysis for user {user_id}: "
                           f"segment={segment.value}, risk={risk_assessment['level'].value}, "
                           f"engagement={engagement_score:.2f}")
            
            return analysis
            
        except Exception as e:
            self.logger.error(f"Error analyzing user {user_id}: {str(e)}")
            raise
    
    async def decide_rewards(self, user_id: str, analysis: Optional[UserAnalysis] = None) -> List[RewardDecision]:
        """
        Decide on reward allocation for a user based on analysis.
        
        Args:
            user_id: Target user ID
            analysis: Optional pre-computed analysis (will compute if not provided)
            
        Returns:
            List of reward decisions
        """
        try:
            if analysis is None:
                analysis = await self.analyze_user_behavior(user_id)
            
            rewards = []
            
            # Check if user is eligible for rewards (not suspended, etc.)
            if not await self._is_reward_eligible(user_id):
                self.logger.info(f"User {user_id} is not eligible for rewards")
                return rewards
            
            # Check daily reward limits
            daily_rewards_count = await self._get_daily_rewards_count(user_id)
            if daily_rewards_count >= get_config().ai_model.max_daily_rewards:
                self.logger.info(f"User {user_id} has reached daily reward limit")
                return rewards
            
            # Welcome bonus for new users
            if analysis.segment == UserSegment.NEW_USER:
                if await self._should_give_welcome_bonus(user_id):
                    rewards.append(RewardDecision(
                        user_id=user_id,
                        reward_type=RewardType.WELCOME_BONUS,
                        amount=get_config().ai_model.base_reward_amount * 2,
                        reason="Welcome bonus for new user",
                        confidence=0.9
                    ))
            
            # Engagement rewards
            if analysis.engagement_score >= get_config().ai_model.engagement_threshold:
                if await self._should_give_engagement_reward(user_id):
                    amount = self._calculate_engagement_reward_amount(analysis.engagement_score)
                    rewards.append(RewardDecision(
                        user_id=user_id,
                        reward_type=RewardType.ENGAGEMENT_REWARD,
                        amount=amount,
                        reason=f"High engagement score: {analysis.engagement_score:.2f}",
                        confidence=analysis.engagement_score
                    ))
            
            # Recovery incentives for dormant users
            if analysis.segment == UserSegment.DORMANT_USER:
                if analysis.days_since_last_activity >= 7:
                    rewards.append(RewardDecision(
                        user_id=user_id,
                        reward_type=RewardType.RECOVERY_INCENTIVE,
                        amount=get_config().ai_model.base_reward_amount * 1.5,
                        reason=f"Recovery incentive - inactive for {analysis.days_since_last_activity} days",
                        confidence=0.8,
                        expires_at=datetime.utcnow() + timedelta(days=7)
                    ))
            
            # High value user bonuses
            if analysis.segment in [UserSegment.HIGH_VALUE_USER, UserSegment.VIP_USER]:
                if analysis.lifetime_value >= get_config().ai_model.high_value_threshold:
                    amount = min(
                        analysis.lifetime_value * 0.02,  # 2% of lifetime value
                        get_config().ai_model.base_reward_amount * 5  # Cap at 5x base
                    )
                    rewards.append(RewardDecision(
                        user_id=user_id,
                        reward_type=RewardType.HIGH_VALUE_BONUS,
                        amount=amount,
                        reason=f"High value user bonus - LTV: ${analysis.lifetime_value:.2f}",
                        confidence=0.85
                    ))
            
            # Log reward decisions
            for reward in rewards:
                await self._log_reward_decision(reward, analysis)
            
            self.logger.info(f"Generated {len(rewards)} reward decisions for user {user_id}")
            return rewards
            
        except Exception as e:
            self.logger.error(f"Error deciding rewards for user {user_id}: {str(e)}")
            raise
    
    async def detect_fraud_patterns(self, user_id: str) -> Dict[str, Any]:
        """
        Detect potential fraud patterns for a user.
        
        Args:
            user_id: Target user ID
            
        Returns:
            Dictionary with fraud detection results
        """
        try:
            self.logger.debug(f"Running fraud detection for user {user_id}")
            
            fraud_indicators = {
                'user_id': user_id,
                'risk_score': 0.0,
                'patterns_detected': [],
                'recommendations': [],
                'requires_investigation': False
            }
            
            # Velocity-based fraud detection
            velocity_risk = await self._check_velocity_fraud(user_id)
            fraud_indicators['risk_score'] += velocity_risk['score']
            fraud_indicators['patterns_detected'].extend(velocity_risk['patterns'])
            
            # Pattern-based fraud detection
            pattern_risk = await self._check_pattern_fraud(user_id)
            fraud_indicators['risk_score'] += pattern_risk['score']
            fraud_indicators['patterns_detected'].extend(pattern_risk['patterns'])
            
            # Device/IP fraud detection
            device_risk = await self._check_device_fraud(user_id)
            fraud_indicators['risk_score'] += device_risk['score']
            fraud_indicators['patterns_detected'].extend(device_risk['patterns'])
            
            # Normalize risk score (0-1 scale)
            fraud_indicators['risk_score'] = min(fraud_indicators['risk_score'], 1.0)
            
            # Generate recommendations based on risk level
            if fraud_indicators['risk_score'] >= get_config().ai_model.suspicious_threshold:
                fraud_indicators['requires_investigation'] = True
                fraud_indicators['recommendations'] = [
                    'Flag account for manual review',
                    'Implement additional verification steps',
                    'Monitor transaction patterns closely'
                ]
                
                # Log high-risk case
                await self._log_fraud_alert(user_id, fraud_indicators)
            
            self.logger.info(f"Fraud detection completed for user {user_id}: "
                           f"risk_score={fraud_indicators['risk_score']:.2f}, "
                           f"patterns={len(fraud_indicators['patterns_detected'])}")
            
            return fraud_indicators
            
        except Exception as e:
            self.logger.error(f"Error in fraud detection for user {user_id}: {str(e)}")
            raise
    
    async def batch_analyze_users(self, user_ids: List[str]) -> Dict[str, UserAnalysis]:
        """
        Perform batch analysis of multiple users.
        
        Args:
            user_ids: List of user IDs to analyze
            
        Returns:
            Dictionary mapping user IDs to their analysis results
        """
        try:
            self.logger.info(f"Starting batch analysis for {len(user_ids)} users")
            
            # Process users in batches to avoid overwhelming the database
            batch_size = get_config().operational.batch_size
            results = {}
            
            for i in range(0, len(user_ids), batch_size):
                batch = user_ids[i:i + batch_size]
                
                # Use semaphore to limit concurrent operations
                semaphore = asyncio.Semaphore(get_config().operational.max_concurrent_operations)
                
                async def analyze_with_semaphore(uid):
                    async with semaphore:
                        try:
                            return uid, await self.analyze_user_behavior(uid)
                        except Exception as e:
                            self.logger.error(f"Failed to analyze user {uid}: {str(e)}")
                            return uid, None
                
                # Process batch concurrently
                batch_tasks = [analyze_with_semaphore(uid) for uid in batch]
                batch_results = await asyncio.gather(*batch_tasks, return_exceptions=True)
                
                # Collect results
                for result in batch_results:
                    if isinstance(result, tuple) and result[1] is not None:
                        results[result[0]] = result[1]
                
                self.logger.info(f"Completed batch {i//batch_size + 1} - "
                               f"analyzed {len([r for r in batch_results if not isinstance(r, Exception)])} users")
                
                # Brief pause between batches to prevent overload
                await asyncio.sleep(1)
            
            self.logger.info(f"Batch analysis completed: {len(results)} successful analyses")
            return results
            
        except Exception as e:
            self.logger.error(f"Error in batch analysis: {str(e)}")
            raise
    
    # Private helper methods
    async def _gather_user_data(self, user_id: str) -> Optional[Dict[str, Any]]:
        """Gather comprehensive user data for analysis."""
        return await self.db.get_user_data(user_id)
    
    async def _calculate_engagement_score(self, user_id: str, user_data: Dict[str, Any]) -> float:
        """Calculate user engagement score based on various metrics."""
        score = 0.0
        
        # Transaction frequency (0-0.4)
        tx_count = len(user_data.get('transactions', []))
        if tx_count > 0:
            score += min(tx_count / 20.0, 0.4)  # Max 0.4 for 20+ transactions
        
        # Account activity (0-0.3)
        if user_data.get('last_login'):
            score += 0.3
        
        # Recency (0-0.3)
        days_since_activity = user_data.get('days_since_last_activity', 999)
        if days_since_activity <= 1:
            score += 0.3
        elif days_since_activity <= 7:
            score += 0.2
        elif days_since_activity <= 30:
            score += 0.1
        
        return min(score, 1.0)
    
    async def _analyze_transaction_patterns(self, user_id: str) -> Dict[str, Any]:
        """Analyze user transaction patterns."""
        return await self.db.get_transaction_analytics(user_id, get_config().ai_model.transaction_analysis_window_days)
    
    async def _assess_user_risk(self, user_id: str, user_data: Dict[str, Any], 
                               transaction_analysis: Dict[str, Any]) -> Dict[str, Any]:
        """Assess user risk level and patterns."""
        risk_score = 0.0
        patterns = []
        
        # High velocity risk
        if transaction_analysis['velocity'] > get_config().ai_model.risk_velocity_threshold:
            risk_score += 0.3
            patterns.append("High transaction velocity")
        
        # New account with high activity
        account_age_days = (datetime.utcnow() - user_data['created_at'].replace(tzinfo=None)).days
        if account_age_days < 7 and transaction_analysis['transaction_count'] > 10:
            risk_score += 0.4
            patterns.append("New account with high activity")
        
        # Unusual transaction amounts
        if transaction_analysis['max_transaction'] > transaction_analysis['avg_transaction'] * 10:
            risk_score += 0.2
            patterns.append("Unusual transaction amounts")
        
        # Determine risk level
        if risk_score >= 0.8:
            level = RiskLevel.CRITICAL
        elif risk_score >= 0.6:
            level = RiskLevel.HIGH
        elif risk_score >= 0.3:
            level = RiskLevel.MEDIUM
        else:
            level = RiskLevel.LOW
        
        return {
            'level': level,
            'score': risk_score,
            'patterns': patterns
        }
    
    async def _classify_user_segment(self, user_id: str, user_data: Dict[str, Any], 
                                   engagement_score: float) -> UserSegment:
        """Classify user into appropriate segment."""
        account_age_days = (datetime.utcnow() - user_data['created_at'].replace(tzinfo=None)).days
        tx_count = len(user_data.get('transactions', []))
        total_amount = sum(float(tx.get('amount', 0)) for tx in user_data.get('transactions', []) if tx.get('type') == 'debit')
        days_since_activity = user_data.get('days_since_last_activity', 0)
        
        # New user (account < 30 days old)
        if account_age_days < 30:
            return UserSegment.NEW_USER
        
        # Dormant user (no activity in 30+ days)
        if days_since_activity >= 30:
            return UserSegment.DORMANT_USER
        
        # VIP user (very high value and engagement)
        if total_amount >= get_config().ai_model.high_value_threshold * 2 and engagement_score >= 0.8:
            return UserSegment.VIP_USER
        
        # High value user
        if total_amount >= get_config().ai_model.high_value_threshold:
            return UserSegment.HIGH_VALUE_USER
        
        # Active user (high engagement or frequent transactions)
        if engagement_score >= 0.6 or tx_count >= get_config().ai_model.frequent_user_tx_threshold:
            return UserSegment.ACTIVE_USER
        
        # At-risk user (declining engagement)
        if engagement_score < 0.3 and days_since_activity >= 7:
            return UserSegment.AT_RISK_USER
        
        # Default to casual user
        return UserSegment.CASUAL_USER
    
    async def _generate_recommendations(self, user_id: str, segment: UserSegment, 
                                      engagement_score: float, risk_assessment: Dict[str, Any]) -> List[Dict[str, Any]]:
        """Generate AI recommendations for user."""
        recommendations = []
        
        if segment == UserSegment.NEW_USER:
            recommendations.append({
                'action': 'send_welcome_tutorial',
                'priority': 'high',
                'reason': 'New user onboarding'
            })
        
        if segment == UserSegment.DORMANT_USER:
            recommendations.append({
                'action': 'send_reactivation_campaign',
                'priority': 'medium',
                'reason': 'User reactivation needed'
            })
        
        if risk_assessment['level'] in [RiskLevel.HIGH, RiskLevel.CRITICAL]:
            recommendations.append({
                'action': 'flag_for_review',
                'priority': 'critical',
                'reason': f"High risk score: {risk_assessment['score']:.2f}"
            })
        
        if engagement_score >= 0.8:
            recommendations.append({
                'action': 'offer_premium_features',
                'priority': 'medium',
                'reason': 'High engagement user'
            })
        
        return recommendations
    
    async def _calculate_analysis_confidence(self, user_data: Dict[str, Any], 
                                           transaction_analysis: Dict[str, Any]) -> float:
        """Calculate confidence score for the analysis."""
        confidence = 0.5  # Base confidence
        
        # More data = higher confidence
        if transaction_analysis['transaction_count'] >= 10:
            confidence += 0.2
        if len(user_data.get('transactions', [])) >= 5:
            confidence += 0.2
        
        # Account age factor
        account_age_days = (datetime.utcnow() - user_data['created_at'].replace(tzinfo=None)).days
        if account_age_days >= 30:
            confidence += 0.1
        
        return min(confidence, 1.0)
    
    async def _log_analysis(self, analysis: UserAnalysis) -> None:
        """Log analysis results to database."""
        await self.db.log_ai_action(
            action_type='user_behavior_analysis',
            description=f'User behavior analysis completed for {analysis.user_id}',
            user_id=analysis.user_id,
            output_data=asdict(analysis),
            confidence_score=analysis.confidence_score
        )
    
    async def _is_reward_eligible(self, user_id: str) -> bool:
        """Check if user is eligible for rewards."""
        return await self.db.is_user_reward_eligible(user_id)
    
    async def _get_daily_rewards_count(self, user_id: str) -> int:
        """Get count of rewards given to user today."""
        return await self.db.get_daily_rewards_count(user_id)
    
    async def _should_give_welcome_bonus(self, user_id: str) -> bool:
        """Check if user should receive welcome bonus."""
        try:
            async with self.db.pool.acquire() as conn:
                count = await conn.fetchval("""
                    SELECT COUNT(*)
                    FROM reward_queue 
                    WHERE user_id = $1 
                    AND reward_type = 'welcome_bonus'
                """, user_id)
                return (count or 0) == 0
        except Exception as e:
            self.logger.error(f"Error checking welcome bonus eligibility: {str(e)}")
            return False
    
    async def _should_give_engagement_reward(self, user_id: str) -> bool:
        """Check if user should receive engagement reward."""
        try:
            async with self.db.pool.acquire() as conn:
                last_reward = await conn.fetchval("""
                    SELECT MAX(created_at)
                    FROM reward_queue 
                    WHERE user_id = $1 
                    AND reward_type = 'engagement_reward'
                """, user_id)
                
                if last_reward:
                    hours_since = (datetime.utcnow() - last_reward.replace(tzinfo=None)).total_seconds() / 3600
                    return hours_since >= get_config().ai_model.reward_cooldown_hours
                return True
        except Exception as e:
            self.logger.error(f"Error checking engagement reward eligibility: {str(e)}")
            return False
    
    async def _calculate_engagement_reward_amount(self, engagement_score: float) -> float:
        """Calculate reward amount based on engagement score."""
        return get_config().ai_model.base_reward_amount * engagement_score * 1.5
    
    async def _log_reward_decision(self, reward: RewardDecision, analysis: UserAnalysis) -> None:
        """Log reward decision to database."""
        await self.db.log_ai_action(
            action_type='reward_decision',
            description=f'Reward decision: {reward.reward_type.value} ${reward.amount:.2f} for {reward.user_id}',
            user_id=reward.user_id,
            output_data={
                'reward': asdict(reward),
                'analysis_context': {
                    'segment': analysis.segment.value,
                    'engagement_score': analysis.engagement_score,
                    'risk_level': analysis.risk_level.value
                }
            },
            confidence_score=reward.confidence
        )
    
    async def _check_velocity_fraud(self, user_id: str) -> Dict[str, Any]:
        """Check for velocity-based fraud patterns."""
        try:
            async with self.db.pool.acquire() as conn:
                result = await conn.fetchrow("""
                    SELECT 
                        COUNT(*) as tx_count,
                        COALESCE(SUM(amount), 0) as total_amount
                    FROM transactions 
                    WHERE user_id = $1 
                    AND created_at >= NOW() - INTERVAL '1 HOUR'
                """, user_id)
                
                risk_score = 0.0
                patterns = []
                
                if result:
                    hourly_tx_count = result['tx_count']
                    hourly_amount = float(result['total_amount'] or 0)
                    
                    if hourly_tx_count > 10:
                        risk_score += 0.4
                        patterns.append(f"High transaction frequency: {hourly_tx_count} in 1 hour")
                    
                    if hourly_amount > get_config().ai_model.risk_velocity_threshold:
                        risk_score += 0.5
                        patterns.append(f"High transaction volume: ${hourly_amount:.2f} in 1 hour")
                
                return {'score': risk_score, 'patterns': patterns}
        except Exception as e:
            self.logger.error(f"Error checking velocity fraud: {str(e)}")
            return {'score': 0.0, 'patterns': []}
    
    async def _check_pattern_fraud(self, user_id: str) -> Dict[str, Any]:
        """Check for pattern-based fraud indicators."""
        # This would implement more sophisticated pattern detection
        # For now, returning basic structure
        return {'score': 0.0, 'patterns': []}
    
    async def _check_device_fraud(self, user_id: str) -> Dict[str, Any]:
        """Check for device/IP based fraud indicators."""
        # This would implement device fingerprinting and IP analysis
        # For now, returning basic structure
        return {'score': 0.0, 'patterns': []}
    
    async def _log_fraud_alert(self, user_id: str, fraud_indicators: Dict[str, Any]) -> None:
        """Log fraud alert to database."""
        await self.db.log_fraud_alert(user_id, fraud_indicators)
