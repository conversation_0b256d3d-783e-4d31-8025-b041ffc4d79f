import React, { useEffect, useRef } from 'react';
import {
  View,
  Text,
  StyleSheet,
  SafeAreaView,
  TouchableOpacity,
  Animated,
  Dimensions,
  StatusBar,
} from 'react-native';
import { useRoute } from '@react-navigation/native';
import ApiService from '../../../infrastructure/api/apiService';
import { useTheme } from '../../../shared/components/layout/ThemeContext';
import { logger } from '../../../infrastructure/monitoring/productionLogger';
import {
  CheckIcon,
  SecurityIcon,
  FingerprintIcon,
  SpeedIcon,
  CelebrationIcon,
  StarIcon,
  HeartIcon,
  RocketIcon,
  ArrowRightIcon
} from '../../../shared/components/icons';
import { navigationHandler } from '../../../navigation/handlers/navigationHandler';



const SetupCompleteScreen: React.FC = () => {
  const route = useRoute();
  const { userData } = route.params as { userData?: any };
  const { theme, isDark } = useTheme();

  // Animation values
  const scaleAnimation = useRef(new Animated.Value(0)).current;
  const fadeAnimation = useRef(new Animated.Value(0)).current;
  const slideAnimation = useRef(new Animated.Value(50)).current;

  useEffect(() => {
    // Start animations
    Animated.sequence([
      Animated.timing(scaleAnimation, {
        toValue: 1,
        duration: 600,
        useNativeDriver: true,
      }),
      Animated.parallel([
        Animated.timing(fadeAnimation, {
          toValue: 1,
          duration: 400,
          useNativeDriver: true,
        }),
        Animated.timing(slideAnimation, {
          toValue: 0,
          duration: 400,
          useNativeDriver: true,
        }),
      ]),
    ]).start();

    // Mark setup as complete in backend
    markSetupComplete();
  }, []);

  const markSetupComplete = async () => {
    try {
      const response = await ApiService.post('/setup/complete');
      
      // Store tokens if they exist in the response (for authentication persistence)
      if (response.data?.data?.tokens) {
        const { accessToken, refreshToken } = response.data.data.tokens;
        await ApiService.storeTokens(accessToken, refreshToken);
        logger.info('Tokens stored after setup completion', null, 'setup');
      }
    } catch (error) {
      logger.error('Error marking setup complete', { error, screen: 'SetupCompleteScreen' });
      // Continue anyway, user can still access the app
    }
  };

  const handleGetStarted = () => {
    // Navigate to main tabs screen
    navigationHandler.resetToScreen('MainTabs');
  };

  const styles = StyleSheet.create({
    container: {
      flex: 1,
      backgroundColor: theme.colors.background,
    },
    content: {
      flex: 1,
      paddingHorizontal: 20,
      justifyContent: 'center',
      paddingTop: 60,
    },
    successContainer: {
      alignItems: 'center',
      marginBottom: 30,
    },
    successIcon: {
      width: 120,
      height: 120,
      borderRadius: 60,
      backgroundColor: theme.colors.primary + '20',
      justifyContent: 'center',
      alignItems: 'center',
      marginBottom: 20,
    },
    textContainer: {
      alignItems: 'center',
      marginBottom: 30,
    },
    title: {
      fontSize: 32,
      fontWeight: '700',
      color: theme.colors.text,
      marginBottom: 12,
      textAlign: 'center',
    },
    subtitle: {
      fontSize: 16,
      color: theme.colors.muted,
      textAlign: 'center',
      lineHeight: 24,
      paddingHorizontal: 10,
    },
    featuresContainer: {
      marginBottom: 30,
    },
    featureItem: {
      flexDirection: 'row',
      alignItems: 'center',
      paddingVertical: 16,
      paddingHorizontal: 20,
      backgroundColor: theme.colors.card,
      borderRadius: 12,
      marginBottom: 12,
      borderWidth: 1,
      borderColor: theme.colors.border,
    },
    featureIcon: {
      width: 40,
      height: 40,
      borderRadius: 20,
      backgroundColor: theme.colors.primary + '20',
      justifyContent: 'center',
      alignItems: 'center',
      marginRight: 16,
    },
    featureContent: {
      flex: 1,
    },
    featureTitle: {
      fontSize: 16,
      fontWeight: '600',
      color: theme.colors.text,
      marginBottom: 2,
    },
    featureDescription: {
      fontSize: 14,
      color: theme.colors.muted,
    },
    featureStatus: {
      marginLeft: 12,
    },
    buttonContainer: {
      marginBottom: 20,
    },
    getStartedButton: {
      backgroundColor: theme.colors.primary,
      paddingVertical: 18,
      paddingHorizontal: 24,
      borderRadius: 16,
      flexDirection: 'row',
      justifyContent: 'center',
      alignItems: 'center',
      shadowColor: theme.colors.primary,
      shadowOffset: {
        width: 0,
        height: 4,
      },
      shadowOpacity: 0.3,
      shadowRadius: 8,
      elevation: 8,
    },
    getStartedButtonText: {
      color: '#FFFFFF',
      fontSize: 18,
      fontWeight: '600',
      marginRight: 8,
    },
  });

  return (
    <SafeAreaView style={styles.container}>
      <StatusBar barStyle={isDark ? "light-content" : "dark-content"} backgroundColor={theme.colors.background} />
      
      <View style={styles.content}>
        {/* Success Animation */}
        <Animated.View 
          style={[
            styles.successContainer,
            { transform: [{ scale: scaleAnimation }] }
          ]}
        >
          <View style={styles.successIcon}>
            <CelebrationIcon size={60} color={theme.colors.primary} />
          </View>
        </Animated.View>

        {/* Content */}
        <Animated.View 
          style={[
            styles.textContainer,
            { 
              opacity: fadeAnimation,
              transform: [{ translateY: slideAnimation }]
            }
          ]}
        >
          <Text style={styles.title}>You're All Set!</Text>
          <Text style={styles.subtitle}>
            Your Vendy account has been successfully set up and secured. 
            You can now enjoy all our features safely.
          </Text>
        </Animated.View>

        {/* Features Summary */}
        <Animated.View 
          style={[
            styles.featuresContainer,
            { 
              opacity: fadeAnimation,
              transform: [{ translateY: slideAnimation }]
            }
          ]}
        >
          <View style={styles.featureItem}>
            <View style={styles.featureIcon}>
              <SecurityIcon size={20} color={theme.colors.primary} />
            </View>
            <View style={styles.featureContent}>
              <Text style={styles.featureTitle}>Secure PIN</Text>
              <Text style={styles.featureDescription}>Your transactions are protected</Text>
            </View>
            <View style={styles.featureStatus}>
              <CheckIcon size={20} color="#34C759" />
            </View>
          </View>

          <View style={styles.featureItem}>
            <View style={styles.featureIcon}>
              <FingerprintIcon size={20} color={theme.colors.primary} />
            </View>
            <View style={styles.featureContent}>
              <Text style={styles.featureTitle}>Biometric Security</Text>
              <Text style={styles.featureDescription}>Quick and secure access</Text>
            </View>
            <View style={styles.featureStatus}>
              <CheckIcon size={20} color="#34C759" />
            </View>
          </View>

          <View style={styles.featureItem}>
            <View style={styles.featureIcon}>
              <SpeedIcon size={20} color={theme.colors.primary} />
            </View>
            <View style={styles.featureContent}>
              <Text style={styles.featureTitle}>Ready to Use</Text>
              <Text style={styles.featureDescription}>Your account is fully configured</Text>
            </View>
            <View style={styles.featureStatus}>
              <CheckIcon size={20} color="#34C759" />
            </View>
          </View>
        </Animated.View>

        {/* Action Button */}
        <Animated.View 
          style={[
            styles.buttonContainer,
            { 
              opacity: fadeAnimation,
              transform: [{ translateY: slideAnimation }]
            }
          ]}
        >
          <TouchableOpacity style={styles.getStartedButton} onPress={handleGetStarted}>
            <Text style={styles.getStartedButtonText}>Continue</Text>
            <ArrowRightIcon size={18} color="#FFFFFF" />
          </TouchableOpacity>
        </Animated.View>


      </View>
    </SafeAreaView>
  );
};

export default SetupCompleteScreen;