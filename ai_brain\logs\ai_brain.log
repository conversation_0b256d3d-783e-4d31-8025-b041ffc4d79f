{"timestamp": "2025-07-01T06:25:59.986448Z", "level": "INFO", "logger": "ai_brain", "message": "Starting PayVendy AI Brain...", "module": "logger", "function": "_log_with_context", "line": 132, "asctime": "2025-07-01 07:25:59,971"}
{"timestamp": "2025-07-01T06:25:59.989890Z", "level": "INFO", "logger": "ai_brain", "message": "Initializing database connection pool...", "module": "logger", "function": "_log_with_context", "line": 132, "asctime": "2025-07-01 07:25:59,986"}
{"timestamp": "2025-07-01T06:26:04.193413Z", "level": "ERROR", "logger": "ai_brain", "message": "Failed to initialize database: [WinError 1225] The remote computer refused the network connection", "module": "logger", "function": "_log_with_context", "line": 132, "asctime": "2025-07-01 07:26:04,192"}
{"timestamp": "2025-07-01T06:26:04.195204Z", "level": "ERROR", "logger": "ai_brain", "message": "Failed to start AI Brain: [WinError 1225] The remote computer refused the network connection", "module": "logger", "function": "_log_with_context", "line": 132, "asctime": "2025-07-01 07:26:04,194"}
{"timestamp": "2025-07-01T07:15:50.908399Z", "level": "INFO", "logger": "ai_brain", "message": "Starting PayVendy AI Brain...", "module": "logger", "function": "_log_with_context", "line": 132, "asctime": "2025-07-01 08:15:50,904"}
{"timestamp": "2025-07-01T07:15:50.914550Z", "level": "INFO", "logger": "ai_brain", "message": "Initializing database connection pool...", "module": "logger", "function": "_log_with_context", "line": 132, "asctime": "2025-07-01 08:15:50,911"}
{"timestamp": "2025-07-01T07:15:50.916576Z", "level": "INFO", "logger": "ai_brain", "message": "Connecting to database: postgresql://postgres:password@localhost:5432/payv...", "module": "logger", "function": "_log_with_context", "line": 132, "asctime": "2025-07-01 08:15:50,915"}
{"timestamp": "2025-07-01T07:15:55.114475Z", "level": "WARNING", "logger": "ai_brain", "message": "Database connection attempt 1/3 failed: [WinError 1225] The remote computer refused the network connection", "module": "logger", "function": "_log_with_context", "line": 132, "asctime": "2025-07-01 08:15:55,112"}
{"timestamp": "2025-07-01T07:15:55.117744Z", "level": "INFO", "logger": "ai_brain", "message": "Retrying in 5 seconds...", "module": "logger", "function": "_log_with_context", "line": 132, "asctime": "2025-07-01 08:15:55,115"}
{"timestamp": "2025-07-01T07:16:04.191369Z", "level": "WARNING", "logger": "ai_brain", "message": "Database connection attempt 2/3 failed: [WinError 1225] The remote computer refused the network connection", "module": "logger", "function": "_log_with_context", "line": 132, "asctime": "2025-07-01 08:16:04,190"}
{"timestamp": "2025-07-01T07:16:04.194904Z", "level": "INFO", "logger": "ai_brain", "message": "Retrying in 5 seconds...", "module": "logger", "function": "_log_with_context", "line": 132, "asctime": "2025-07-01 08:16:04,192"}
{"timestamp": "2025-07-01T07:16:13.267687Z", "level": "WARNING", "logger": "ai_brain", "message": "Database connection attempt 3/3 failed: [WinError 1225] The remote computer refused the network connection", "module": "logger", "function": "_log_with_context", "line": 132, "asctime": "2025-07-01 08:16:13,266"}
{"timestamp": "2025-07-01T07:16:13.271301Z", "level": "ERROR", "logger": "ai_brain", "message": "Failed to initialize database after 3 attempts: [WinError 1225] The remote computer refused the network connection", "module": "logger", "function": "_log_with_context", "line": 132, "asctime": "2025-07-01 08:16:13,268"}
{"timestamp": "2025-07-01T07:16:13.276215Z", "level": "ERROR", "logger": "ai_brain", "message": "Please check:", "module": "logger", "function": "_log_with_context", "line": 132, "asctime": "2025-07-01 08:16:13,271"}
{"timestamp": "2025-07-01T07:16:13.279501Z", "level": "ERROR", "logger": "ai_brain", "message": "1. Database server is running and accessible", "module": "logger", "function": "_log_with_context", "line": 132, "asctime": "2025-07-01 08:16:13,276"}
{"timestamp": "2025-07-01T07:16:13.287072Z", "level": "ERROR", "logger": "ai_brain", "message": "2. DATABASE_URL is correct", "module": "logger", "function": "_log_with_context", "line": 132, "asctime": "2025-07-01 08:16:13,279"}
{"timestamp": "2025-07-01T07:16:13.290073Z", "level": "ERROR", "logger": "ai_brain", "message": "3. Network connectivity to the database", "module": "logger", "function": "_log_with_context", "line": 132, "asctime": "2025-07-01 08:16:13,287"}
{"timestamp": "2025-07-01T07:16:13.293676Z", "level": "ERROR", "logger": "ai_brain", "message": "4. Database credentials are valid", "module": "logger", "function": "_log_with_context", "line": 132, "asctime": "2025-07-01 08:16:13,290"}
{"timestamp": "2025-07-01T07:16:13.298210Z", "level": "ERROR", "logger": "ai_brain", "message": "Failed to start AI Brain: [WinError 1225] The remote computer refused the network connection", "module": "logger", "function": "_log_with_context", "line": 132, "asctime": "2025-07-01 08:16:13,293"}
2025-07-01 08:16:13,298 - root - ERROR - Fatal error: [WinError 1225] The remote computer refused the network connection
{"timestamp": "2025-07-01T07:33:02.760017Z", "level": "INFO", "logger": "ai_brain", "message": "Starting PayVendy AI Brain...", "module": "logger", "function": "_log_with_context", "line": 132, "asctime": "2025-07-01 08:33:02,753"}
{"timestamp": "2025-07-01T07:33:02.764321Z", "level": "INFO", "logger": "ai_brain", "message": "Initializing database connection pool...", "module": "logger", "function": "_log_with_context", "line": 132, "asctime": "2025-07-01 08:33:02,761"}
{"timestamp": "2025-07-01T07:33:02.766242Z", "level": "INFO", "logger": "ai_brain", "message": "Connecting to database: postgresql://postgres:<EMAIL>...", "module": "logger", "function": "_log_with_context", "line": 132, "asctime": "2025-07-01 08:33:02,764"}
{"timestamp": "2025-07-01T07:33:03.711616Z", "level": "WARNING", "logger": "ai_brain", "message": "Database connection attempt 1/3 failed: [Errno 11001] getaddrinfo failed", "module": "logger", "function": "_log_with_context", "line": 132, "asctime": "2025-07-01 08:33:03,711"}
{"timestamp": "2025-07-01T07:33:03.711616Z", "level": "INFO", "logger": "ai_brain", "message": "Retrying in 5 seconds...", "module": "logger", "function": "_log_with_context", "line": 132, "asctime": "2025-07-01 08:33:03,711"}
{"timestamp": "2025-07-01T07:33:08.732205Z", "level": "WARNING", "logger": "ai_brain", "message": "Database connection attempt 2/3 failed: [Errno 11001] getaddrinfo failed", "module": "logger", "function": "_log_with_context", "line": 132, "asctime": "2025-07-01 08:33:08,724"}
{"timestamp": "2025-07-01T07:33:08.740594Z", "level": "INFO", "logger": "ai_brain", "message": "Retrying in 5 seconds...", "module": "logger", "function": "_log_with_context", "line": 132, "asctime": "2025-07-01 08:33:08,732"}
{"timestamp": "2025-07-01T07:33:13.770938Z", "level": "WARNING", "logger": "ai_brain", "message": "Database connection attempt 3/3 failed: [Errno 11001] getaddrinfo failed", "module": "logger", "function": "_log_with_context", "line": 132, "asctime": "2025-07-01 08:33:13,768"}
{"timestamp": "2025-07-01T07:33:13.774342Z", "level": "ERROR", "logger": "ai_brain", "message": "Failed to initialize database after 3 attempts: [Errno 11001] getaddrinfo failed", "module": "logger", "function": "_log_with_context", "line": 132, "asctime": "2025-07-01 08:33:13,770"}
{"timestamp": "2025-07-01T07:33:13.778397Z", "level": "ERROR", "logger": "ai_brain", "message": "Please check:", "module": "logger", "function": "_log_with_context", "line": 132, "asctime": "2025-07-01 08:33:13,774"}
{"timestamp": "2025-07-01T07:33:13.784083Z", "level": "ERROR", "logger": "ai_brain", "message": "1. Database server is running and accessible", "module": "logger", "function": "_log_with_context", "line": 132, "asctime": "2025-07-01 08:33:13,778"}
{"timestamp": "2025-07-01T07:33:13.794201Z", "level": "ERROR", "logger": "ai_brain", "message": "2. DATABASE_URL is correct", "module": "logger", "function": "_log_with_context", "line": 132, "asctime": "2025-07-01 08:33:13,784"}
{"timestamp": "2025-07-01T07:33:13.803639Z", "level": "ERROR", "logger": "ai_brain", "message": "3. Network connectivity to the database", "module": "logger", "function": "_log_with_context", "line": 132, "asctime": "2025-07-01 08:33:13,794"}
{"timestamp": "2025-07-01T07:33:13.808999Z", "level": "ERROR", "logger": "ai_brain", "message": "4. Database credentials are valid", "module": "logger", "function": "_log_with_context", "line": 132, "asctime": "2025-07-01 08:33:13,803"}
{"timestamp": "2025-07-01T07:33:13.818055Z", "level": "ERROR", "logger": "ai_brain", "message": "Failed to start AI Brain: [Errno 11001] getaddrinfo failed", "module": "logger", "function": "_log_with_context", "line": 132, "asctime": "2025-07-01 08:33:13,808"}
2025-07-01 08:33:13,820 - root - ERROR - Fatal error: [Errno 11001] getaddrinfo failed
{"timestamp": "2025-07-01T07:45:02.832195Z", "level": "INFO", "logger": "ai_brain", "message": "Starting PayVendy AI Brain...", "module": "logger", "function": "_log_with_context", "line": 132, "asctime": "2025-07-01 08:45:02,783"}
{"timestamp": "2025-07-01T07:45:02.860187Z", "level": "INFO", "logger": "ai_brain", "message": "Initializing database connection pool...", "module": "logger", "function": "_log_with_context", "line": 132, "asctime": "2025-07-01 08:45:02,833"}
{"timestamp": "2025-07-01T07:45:02.863817Z", "level": "INFO", "logger": "ai_brain", "message": "Connecting to database: postgresql://postgres.akplhhlissxjvyuaokcs:5g5I0W1...", "module": "logger", "function": "_log_with_context", "line": 132, "asctime": "2025-07-01 08:45:02,860"}
{"timestamp": "2025-07-01T07:45:09.877835Z", "level": "INFO", "logger": "ai_brain", "message": "Database connection test successful: 1", "module": "logger", "function": "_log_with_context", "line": 132, "asctime": "2025-07-01 08:45:09,872"}
{"timestamp": "2025-07-01T07:45:10.057712Z", "level": "INFO", "logger": "ai_brain", "message": "Database connection pool initialized successfully", "module": "logger", "function": "_log_with_context", "line": 132, "asctime": "2025-07-01 08:45:10,055"}
{"timestamp": "2025-07-01T07:45:10.096159Z", "level": "INFO", "logger": "ai_brain", "message": "Initializing AI Brain Orchestrator...", "module": "logger", "function": "_log_with_context", "line": 132, "asctime": "2025-07-01 08:45:10,059"}
{"timestamp": "2025-07-01T07:45:10.218930Z", "level": "INFO", "logger": "ai_brain", "message": "System event [orchestrator_init]: AI Brain Orchestrator initialized successfully", "module": "logger", "function": "_log_with_context", "line": 132, "context": {"user_id": null, "session_id": null, "operation": "system_event", "model_version": null, "processing_time_ms": null, "confidence_score": null, "additional_data": {"event_type": "orchestrator_init", "severity": "info", "database": {"url": "***REDACTED***", "min_pool_size": 5, "max_pool_size": 20, "pool_timeout": 30, "statement_timeout": 60}, "ai_model": {"transaction_analysis_window_days": 30, "suspicious_threshold": 0.8, "engagement_threshold": 0.6, "base_reward_amount": 10.0, "max_daily_rewards": 5, "reward_cooldown_hours": 24, "high_value_threshold": 1000.0, "frequent_user_tx_threshold": 10, "risk_velocity_threshold": 500.0, "risk_pattern_threshold": 3}, "operational": {"analysis_interval_seconds": 300, "reward_processing_interval_seconds": 600, "cleanup_interval_hours": 24, "log_retention_days": 90, "analytics_retention_days": 365, "max_concurrent_operations": 10, "batch_size": 100, "enable_performance_monitoring": true, "enable_detailed_logging": false}, "environment": "development", "log_level": "INFO", "feature_flags": {"real_time_processing": true, "advanced_analytics": true, "fraud_detection": true}}}, "asctime": "2025-07-01 08:45:10,100"}
{"timestamp": "2025-07-01T07:45:10.311423Z", "level": "INFO", "logger": "ai_brain", "message": "AI Brain started successfully", "module": "logger", "function": "_log_with_context", "line": 132, "asctime": "2025-07-01 08:45:10,221"}
{"timestamp": "2025-07-01T07:45:11.408174Z", "level": "INFO", "logger": "ai_brain", "message": "Starting AI Brain Orchestrator main loop...", "module": "logger", "function": "_log_with_context", "line": 132, "asctime": "2025-07-01 08:45:11,404"}
{"timestamp": "2025-07-01T07:45:11.411171Z", "level": "INFO", "logger": "ai_brain", "message": "Starting user analysis loop...", "module": "logger", "function": "_log_with_context", "line": 132, "asctime": "2025-07-01 08:45:11,408"}
{"timestamp": "2025-07-01T07:45:11.425953Z", "level": "INFO", "logger": "ai_brain", "message": "Starting reward processing loop...", "module": "logger", "function": "_log_with_context", "line": 132, "asctime": "2025-07-01 08:45:11,412"}
{"timestamp": "2025-07-01T07:45:11.461445Z", "level": "INFO", "logger": "ai_brain", "message": "Starting cleanup loop...", "module": "logger", "function": "_log_with_context", "line": 132, "asctime": "2025-07-01 08:45:11,425"}
{"timestamp": "2025-07-01T07:45:11.524618Z", "level": "INFO", "logger": "ai_brain", "message": "Starting scheduled data cleanup...", "module": "logger", "function": "_log_with_context", "line": 132, "asctime": "2025-07-01 08:45:11,467"}
{"timestamp": "2025-07-01T07:45:11.605283Z", "level": "INFO", "logger": "ai_brain", "message": "Starting performance monitoring loop...", "module": "logger", "function": "_log_with_context", "line": 132, "asctime": "2025-07-01 08:45:11,526"}
{"timestamp": "2025-07-01T07:45:11.644662Z", "level": "INFO", "logger": "ai_brain", "message": "Performance metric: analyses_per_hour=0.0analyses/hour", "module": "logger", "function": "_log_with_context", "line": 132, "context": {"user_id": null, "session_id": null, "operation": "performance_metric", "model_version": null, "processing_time_ms": null, "confidence_score": null, "additional_data": {"metric_name": "analyses_per_hour", "value": 0.0, "unit": "analyses/hour"}}, "asctime": "2025-07-01 08:45:11,606"}
{"timestamp": "2025-07-01T07:45:11.705336Z", "level": "INFO", "logger": "ai_brain", "message": "Performance metric: rewards_per_hour=0.0rewards/hour", "module": "logger", "function": "_log_with_context", "line": 132, "context": {"user_id": null, "session_id": null, "operation": "performance_metric", "model_version": null, "processing_time_ms": null, "confidence_score": null, "additional_data": {"metric_name": "rewards_per_hour", "value": 0.0, "unit": "rewards/hour"}}, "asctime": "2025-07-01 08:45:11,655"}
{"timestamp": "2025-07-01T07:45:11.744180Z", "level": "INFO", "logger": "ai_brain", "message": "Performance metric: error_rate=0.0errors/operation", "module": "logger", "function": "_log_with_context", "line": 132, "context": {"user_id": null, "session_id": null, "operation": "performance_metric", "model_version": null, "processing_time_ms": null, "confidence_score": null, "additional_data": {"metric_name": "error_rate", "value": 0.0, "unit": "errors/operation"}}, "asctime": "2025-07-01 08:45:11,705"}
{"timestamp": "2025-07-01T07:45:12.458908Z", "level": "ERROR", "logger": "ai_brain", "message": "Error getting batch users for analysis: column u.status does not exist", "module": "logger", "function": "_log_with_context", "line": 132, "asctime": "2025-07-01 08:45:12,427"}
{"timestamp": "2025-07-01T07:45:12.881540Z", "level": "ERROR", "logger": "ai_brain", "message": "Error cleaning up old data: function cleanup_old_ai_data() does not exist\nHINT:  No function matches the given name and argument types. You might need to add explicit type casts.", "module": "logger", "function": "_log_with_context", "line": 132, "asctime": "2025-07-01 08:45:12,869"}
{"timestamp": "2025-07-01T07:45:12.924196Z", "level": "INFO", "logger": "ai_brain", "message": "System event [data_cleanup]: Cleanup completed: 0 records removed", "module": "logger", "function": "_log_with_context", "line": 132, "context": {"user_id": null, "session_id": null, "operation": "system_event", "model_version": null, "processing_time_ms": null, "confidence_score": null, "additional_data": {"event_type": "data_cleanup", "severity": "info", "deleted_count": 0, "processing_time_ms": 1354}}, "asctime": "2025-07-01 08:45:12,881"}
{"timestamp": "2025-07-01T07:50:11.849995Z", "level": "INFO", "logger": "ai_brain", "message": "Performance metric: analyses_per_hour=0.0analyses/hour", "module": "logger", "function": "_log_with_context", "line": 132, "context": {"user_id": null, "session_id": null, "operation": "performance_metric", "model_version": null, "processing_time_ms": null, "confidence_score": null, "additional_data": {"metric_name": "analyses_per_hour", "value": 0.0, "unit": "analyses/hour"}}, "asctime": "2025-07-01 08:50:11,794"}
{"timestamp": "2025-07-01T07:50:11.861892Z", "level": "INFO", "logger": "ai_brain", "message": "Performance metric: rewards_per_hour=0.0rewards/hour", "module": "logger", "function": "_log_with_context", "line": 132, "context": {"user_id": null, "session_id": null, "operation": "performance_metric", "model_version": null, "processing_time_ms": null, "confidence_score": null, "additional_data": {"metric_name": "rewards_per_hour", "value": 0.0, "unit": "rewards/hour"}}, "asctime": "2025-07-01 08:50:11,858"}
{"timestamp": "2025-07-01T07:50:11.880491Z", "level": "INFO", "logger": "ai_brain", "message": "Performance metric: error_rate=0.0errors/operation", "module": "logger", "function": "_log_with_context", "line": 132, "context": {"user_id": null, "session_id": null, "operation": "performance_metric", "model_version": null, "processing_time_ms": null, "confidence_score": null, "additional_data": {"metric_name": "error_rate", "value": 0.0, "unit": "errors/operation"}}, "asctime": "2025-07-01 08:50:11,863"}
{"timestamp": "2025-07-01T07:50:13.263245Z", "level": "ERROR", "logger": "ai_brain", "message": "Error getting batch users for analysis: column u.status does not exist", "module": "logger", "function": "_log_with_context", "line": 132, "asctime": "2025-07-01 08:50:13,260"}
{"timestamp": "2025-07-01T07:55:11.966988Z", "level": "INFO", "logger": "ai_brain", "message": "Performance metric: analyses_per_hour=0.0analyses/hour", "module": "logger", "function": "_log_with_context", "line": 132, "context": {"user_id": null, "session_id": null, "operation": "performance_metric", "model_version": null, "processing_time_ms": null, "confidence_score": null, "additional_data": {"metric_name": "analyses_per_hour", "value": 0.0, "unit": "analyses/hour"}}, "asctime": "2025-07-01 08:55:11,881"}
{"timestamp": "2025-07-01T07:55:11.986995Z", "level": "INFO", "logger": "ai_brain", "message": "Performance metric: rewards_per_hour=0.0rewards/hour", "module": "logger", "function": "_log_with_context", "line": 132, "context": {"user_id": null, "session_id": null, "operation": "performance_metric", "model_version": null, "processing_time_ms": null, "confidence_score": null, "additional_data": {"metric_name": "rewards_per_hour", "value": 0.0, "unit": "rewards/hour"}}, "asctime": "2025-07-01 08:55:11,971"}
{"timestamp": "2025-07-01T07:55:12.005768Z", "level": "INFO", "logger": "ai_brain", "message": "Performance metric: error_rate=0.0errors/operation", "module": "logger", "function": "_log_with_context", "line": 132, "context": {"user_id": null, "session_id": null, "operation": "performance_metric", "model_version": null, "processing_time_ms": null, "confidence_score": null, "additional_data": {"metric_name": "error_rate", "value": 0.0, "unit": "errors/operation"}}, "asctime": "2025-07-01 08:55:11,986"}
{"timestamp": "2025-07-01T07:55:13.818406Z", "level": "ERROR", "logger": "ai_brain", "message": "Error getting batch users for analysis: column u.status does not exist", "module": "logger", "function": "_log_with_context", "line": 132, "asctime": "2025-07-01 08:55:13,815"}
{"timestamp": "2025-07-01T08:00:12.020833Z", "level": "INFO", "logger": "ai_brain", "message": "Performance metric: analyses_per_hour=0.0analyses/hour", "module": "logger", "function": "_log_with_context", "line": 132, "context": {"user_id": null, "session_id": null, "operation": "performance_metric", "model_version": null, "processing_time_ms": null, "confidence_score": null, "additional_data": {"metric_name": "analyses_per_hour", "value": 0.0, "unit": "analyses/hour"}}, "asctime": "2025-07-01 09:00:12,009"}
{"timestamp": "2025-07-01T08:00:12.041753Z", "level": "INFO", "logger": "ai_brain", "message": "Performance metric: rewards_per_hour=0.0rewards/hour", "module": "logger", "function": "_log_with_context", "line": 132, "context": {"user_id": null, "session_id": null, "operation": "performance_metric", "model_version": null, "processing_time_ms": null, "confidence_score": null, "additional_data": {"metric_name": "rewards_per_hour", "value": 0.0, "unit": "rewards/hour"}}, "asctime": "2025-07-01 09:00:12,020"}
{"timestamp": "2025-07-01T08:00:12.069956Z", "level": "INFO", "logger": "ai_brain", "message": "Performance metric: error_rate=0.0errors/operation", "module": "logger", "function": "_log_with_context", "line": 132, "context": {"user_id": null, "session_id": null, "operation": "performance_metric", "model_version": null, "processing_time_ms": null, "confidence_score": null, "additional_data": {"metric_name": "error_rate", "value": 0.0, "unit": "errors/operation"}}, "asctime": "2025-07-01 09:00:12,043"}
{"timestamp": "2025-07-01T08:00:19.387903Z", "level": "ERROR", "logger": "ai_brain", "message": "Error getting batch users for analysis: column u.status does not exist", "module": "logger", "function": "_log_with_context", "line": 132, "asctime": "2025-07-01 09:00:19,385"}
{"timestamp": "2025-07-01T08:04:07.494141Z", "level": "INFO", "logger": "ai_brain", "message": "Received signal 2", "module": "logger", "function": "_log_with_context", "line": 132, "asctime": "2025-07-01 09:04:06,779"}
{"timestamp": "2025-07-01T08:04:08.687594Z", "level": "INFO", "logger": "ai_brain", "message": "Received signal 2", "module": "logger", "function": "_log_with_context", "line": 132, "asctime": "2025-07-01 09:04:08,679"}
{"timestamp": "2025-07-01T08:04:09.979284Z", "level": "INFO", "logger": "ai_brain", "message": "Received signal 2", "module": "logger", "function": "_log_with_context", "line": 132, "asctime": "2025-07-01 09:04:09,976"}
{"timestamp": "2025-07-01T08:04:09.980291Z", "level": "INFO", "logger": "ai_brain", "message": "Received signal 2", "module": "logger", "function": "_log_with_context", "line": 132, "asctime": "2025-07-01 09:04:09,479"}
{"timestamp": "2025-07-01T08:04:21.153563Z", "level": "INFO", "logger": "ai_brain", "message": "Received signal 2", "module": "logger", "function": "_log_with_context", "line": 132, "asctime": "2025-07-01 09:04:21,151"}
{"timestamp": "2025-07-01T08:04:21.341991Z", "level": "INFO", "logger": "ai_brain", "message": "Received signal 2", "module": "logger", "function": "_log_with_context", "line": 132, "asctime": "2025-07-01 09:04:21,337"}
{"timestamp": "2025-07-01T08:04:21.506838Z", "level": "INFO", "logger": "ai_brain", "message": "Received signal 2", "module": "logger", "function": "_log_with_context", "line": 132, "asctime": "2025-07-01 09:04:21,505"}
{"timestamp": "2025-07-01T08:04:21.662195Z", "level": "INFO", "logger": "ai_brain", "message": "Received signal 2", "module": "logger", "function": "_log_with_context", "line": 132, "asctime": "2025-07-01 09:04:21,658"}
{"timestamp": "2025-07-01T08:04:21.819805Z", "level": "INFO", "logger": "ai_brain", "message": "Received signal 2", "module": "logger", "function": "_log_with_context", "line": 132, "asctime": "2025-07-01 09:04:21,809"}
{"timestamp": "2025-07-01T08:04:21.967092Z", "level": "INFO", "logger": "ai_brain", "message": "Received signal 2", "module": "logger", "function": "_log_with_context", "line": 132, "asctime": "2025-07-01 09:04:21,958"}
{"timestamp": "2025-07-01T08:04:22.457138Z", "level": "INFO", "logger": "ai_brain", "message": "Received signal 2", "module": "logger", "function": "_log_with_context", "line": 132, "asctime": "2025-07-01 09:04:22,456"}
{"timestamp": "2025-07-01T08:04:22.488749Z", "level": "INFO", "logger": "ai_brain", "message": "Received signal 2", "module": "logger", "function": "_log_with_context", "line": 132, "asctime": "2025-07-01 09:04:22,487"}
{"timestamp": "2025-07-01T08:04:22.522089Z", "level": "INFO", "logger": "ai_brain", "message": "Received signal 2", "module": "logger", "function": "_log_with_context", "line": 132, "asctime": "2025-07-01 09:04:22,520"}
{"timestamp": "2025-07-01T08:04:22.553405Z", "level": "INFO", "logger": "ai_brain", "message": "Received signal 2", "module": "logger", "function": "_log_with_context", "line": 132, "asctime": "2025-07-01 09:04:22,551"}
{"timestamp": "2025-07-01T08:04:22.591907Z", "level": "INFO", "logger": "ai_brain", "message": "Received signal 2", "module": "logger", "function": "_log_with_context", "line": 132, "asctime": "2025-07-01 09:04:22,589"}
{"timestamp": "2025-07-01T08:04:22.618285Z", "level": "INFO", "logger": "ai_brain", "message": "Received signal 2", "module": "logger", "function": "_log_with_context", "line": 132, "asctime": "2025-07-01 09:04:22,617"}
{"timestamp": "2025-07-01T08:04:22.653445Z", "level": "INFO", "logger": "ai_brain", "message": "Received signal 2", "module": "logger", "function": "_log_with_context", "line": 132, "asctime": "2025-07-01 09:04:22,652"}
{"timestamp": "2025-07-01T08:04:22.683184Z", "level": "INFO", "logger": "ai_brain", "message": "Received signal 2", "module": "logger", "function": "_log_with_context", "line": 132, "asctime": "2025-07-01 09:04:22,682"}
{"timestamp": "2025-07-01T08:04:22.716281Z", "level": "INFO", "logger": "ai_brain", "message": "Received signal 2", "module": "logger", "function": "_log_with_context", "line": 132, "asctime": "2025-07-01 09:04:22,715"}
{"timestamp": "2025-07-01T08:04:22.749126Z", "level": "INFO", "logger": "ai_brain", "message": "Received signal 2", "module": "logger", "function": "_log_with_context", "line": 132, "asctime": "2025-07-01 09:04:22,743"}
{"timestamp": "2025-07-01T08:04:22.776609Z", "level": "INFO", "logger": "ai_brain", "message": "Received signal 2", "module": "logger", "function": "_log_with_context", "line": 132, "asctime": "2025-07-01 09:04:22,775"}
{"timestamp": "2025-07-01T08:04:22.808945Z", "level": "INFO", "logger": "ai_brain", "message": "Received signal 2", "module": "logger", "function": "_log_with_context", "line": 132, "asctime": "2025-07-01 09:04:22,807"}
{"timestamp": "2025-07-01T08:04:22.841721Z", "level": "INFO", "logger": "ai_brain", "message": "Received signal 2", "module": "logger", "function": "_log_with_context", "line": 132, "asctime": "2025-07-01 09:04:22,839"}
{"timestamp": "2025-07-01T08:04:22.873462Z", "level": "INFO", "logger": "ai_brain", "message": "Received signal 2", "module": "logger", "function": "_log_with_context", "line": 132, "asctime": "2025-07-01 09:04:22,871"}
{"timestamp": "2025-07-01T08:04:22.925993Z", "level": "INFO", "logger": "ai_brain", "message": "Received signal 2", "module": "logger", "function": "_log_with_context", "line": 132, "asctime": "2025-07-01 09:04:22,922"}
{"timestamp": "2025-07-01T08:04:22.938469Z", "level": "INFO", "logger": "ai_brain", "message": "Received signal 2", "module": "logger", "function": "_log_with_context", "line": 132, "asctime": "2025-07-01 09:04:22,936"}
{"timestamp": "2025-07-01T08:04:22.970433Z", "level": "INFO", "logger": "ai_brain", "message": "Received signal 2", "module": "logger", "function": "_log_with_context", "line": 132, "asctime": "2025-07-01 09:04:22,970"}
{"timestamp": "2025-07-01T08:04:23.004345Z", "level": "INFO", "logger": "ai_brain", "message": "Received signal 2", "module": "logger", "function": "_log_with_context", "line": 132, "asctime": "2025-07-01 09:04:23,003"}
{"timestamp": "2025-07-01T08:04:23.037000Z", "level": "INFO", "logger": "ai_brain", "message": "Received signal 2", "module": "logger", "function": "_log_with_context", "line": 132, "asctime": "2025-07-01 09:04:23,035"}
{"timestamp": "2025-07-01T08:04:23.083748Z", "level": "INFO", "logger": "ai_brain", "message": "Received signal 2", "module": "logger", "function": "_log_with_context", "line": 132, "asctime": "2025-07-01 09:04:23,082"}
{"timestamp": "2025-07-01T08:04:23.116897Z", "level": "INFO", "logger": "ai_brain", "message": "Received signal 2", "module": "logger", "function": "_log_with_context", "line": 132, "asctime": "2025-07-01 09:04:23,115"}
{"timestamp": "2025-07-01T08:04:23.150621Z", "level": "INFO", "logger": "ai_brain", "message": "Received signal 2", "module": "logger", "function": "_log_with_context", "line": 132, "asctime": "2025-07-01 09:04:23,148"}
{"timestamp": "2025-07-01T08:04:23.177799Z", "level": "INFO", "logger": "ai_brain", "message": "Received signal 2", "module": "logger", "function": "_log_with_context", "line": 132, "asctime": "2025-07-01 09:04:23,175"}
{"timestamp": "2025-07-01T08:04:23.209431Z", "level": "INFO", "logger": "ai_brain", "message": "Received signal 2", "module": "logger", "function": "_log_with_context", "line": 132, "asctime": "2025-07-01 09:04:23,208"}
{"timestamp": "2025-07-01T08:04:23.241470Z", "level": "INFO", "logger": "ai_brain", "message": "Received signal 2", "module": "logger", "function": "_log_with_context", "line": 132, "asctime": "2025-07-01 09:04:23,239"}
{"timestamp": "2025-07-01T08:04:23.274264Z", "level": "INFO", "logger": "ai_brain", "message": "Received signal 2", "module": "logger", "function": "_log_with_context", "line": 132, "asctime": "2025-07-01 09:04:23,272"}
{"timestamp": "2025-07-01T08:04:23.304660Z", "level": "INFO", "logger": "ai_brain", "message": "Received signal 2", "module": "logger", "function": "_log_with_context", "line": 132, "asctime": "2025-07-01 09:04:23,304"}
{"timestamp": "2025-07-01T08:04:23.338070Z", "level": "INFO", "logger": "ai_brain", "message": "Received signal 2", "module": "logger", "function": "_log_with_context", "line": 132, "asctime": "2025-07-01 09:04:23,336"}
{"timestamp": "2025-07-01T08:04:23.368852Z", "level": "INFO", "logger": "ai_brain", "message": "Received signal 2", "module": "logger", "function": "_log_with_context", "line": 132, "asctime": "2025-07-01 09:04:23,368"}
{"timestamp": "2025-07-01T08:04:23.401107Z", "level": "INFO", "logger": "ai_brain", "message": "Received signal 2", "module": "logger", "function": "_log_with_context", "line": 132, "asctime": "2025-07-01 09:04:23,400"}
{"timestamp": "2025-07-01T08:04:23.433134Z", "level": "INFO", "logger": "ai_brain", "message": "Received signal 2", "module": "logger", "function": "_log_with_context", "line": 132, "asctime": "2025-07-01 09:04:23,432"}
{"timestamp": "2025-07-01T08:04:23.468040Z", "level": "INFO", "logger": "ai_brain", "message": "Received signal 2", "module": "logger", "function": "_log_with_context", "line": 132, "asctime": "2025-07-01 09:04:23,465"}
{"timestamp": "2025-07-01T08:04:23.500876Z", "level": "INFO", "logger": "ai_brain", "message": "Received signal 2", "module": "logger", "function": "_log_with_context", "line": 132, "asctime": "2025-07-01 09:04:23,499"}
{"timestamp": "2025-07-01T08:04:23.534511Z", "level": "INFO", "logger": "ai_brain", "message": "Received signal 2", "module": "logger", "function": "_log_with_context", "line": 132, "asctime": "2025-07-01 09:04:23,532"}
{"timestamp": "2025-07-01T08:04:23.559770Z", "level": "INFO", "logger": "ai_brain", "message": "Received signal 2", "module": "logger", "function": "_log_with_context", "line": 132, "asctime": "2025-07-01 09:04:23,558"}
{"timestamp": "2025-07-01T08:04:23.593084Z", "level": "INFO", "logger": "ai_brain", "message": "Received signal 2", "module": "logger", "function": "_log_with_context", "line": 132, "asctime": "2025-07-01 09:04:23,590"}
{"timestamp": "2025-07-01T08:04:23.624618Z", "level": "INFO", "logger": "ai_brain", "message": "Received signal 2", "module": "logger", "function": "_log_with_context", "line": 132, "asctime": "2025-07-01 09:04:23,624"}
{"timestamp": "2025-07-01T08:04:23.657855Z", "level": "INFO", "logger": "ai_brain", "message": "Received signal 2", "module": "logger", "function": "_log_with_context", "line": 132, "asctime": "2025-07-01 09:04:23,656"}
{"timestamp": "2025-07-01T08:04:23.689439Z", "level": "INFO", "logger": "ai_brain", "message": "Received signal 2", "module": "logger", "function": "_log_with_context", "line": 132, "asctime": "2025-07-01 09:04:23,688"}
{"timestamp": "2025-07-01T08:04:23.721239Z", "level": "INFO", "logger": "ai_brain", "message": "Received signal 2", "module": "logger", "function": "_log_with_context", "line": 132, "asctime": "2025-07-01 09:04:23,719"}
{"timestamp": "2025-07-01T08:04:23.752827Z", "level": "INFO", "logger": "ai_brain", "message": "Received signal 2", "module": "logger", "function": "_log_with_context", "line": 132, "asctime": "2025-07-01 09:04:23,752"}
{"timestamp": "2025-07-01T08:04:23.785379Z", "level": "INFO", "logger": "ai_brain", "message": "Received signal 2", "module": "logger", "function": "_log_with_context", "line": 132, "asctime": "2025-07-01 09:04:23,783"}
{"timestamp": "2025-07-01T08:04:23.817992Z", "level": "INFO", "logger": "ai_brain", "message": "Received signal 2", "module": "logger", "function": "_log_with_context", "line": 132, "asctime": "2025-07-01 09:04:23,816"}
{"timestamp": "2025-07-01T08:04:23.848486Z", "level": "INFO", "logger": "ai_brain", "message": "Received signal 2", "module": "logger", "function": "_log_with_context", "line": 132, "asctime": "2025-07-01 09:04:23,848"}
{"timestamp": "2025-07-01T08:04:23.882990Z", "level": "INFO", "logger": "ai_brain", "message": "Received signal 2", "module": "logger", "function": "_log_with_context", "line": 132, "asctime": "2025-07-01 09:04:23,881"}
{"timestamp": "2025-07-01T08:04:23.916433Z", "level": "INFO", "logger": "ai_brain", "message": "Received signal 2", "module": "logger", "function": "_log_with_context", "line": 132, "asctime": "2025-07-01 09:04:23,915"}
{"timestamp": "2025-07-01T08:04:23.947446Z", "level": "INFO", "logger": "ai_brain", "message": "Received signal 2", "module": "logger", "function": "_log_with_context", "line": 132, "asctime": "2025-07-01 09:04:23,941"}
{"timestamp": "2025-07-01T08:04:23.987844Z", "level": "INFO", "logger": "ai_brain", "message": "Received signal 2", "module": "logger", "function": "_log_with_context", "line": 132, "asctime": "2025-07-01 09:04:23,985"}
{"timestamp": "2025-07-01T08:04:24.025192Z", "level": "INFO", "logger": "ai_brain", "message": "Received signal 2", "module": "logger", "function": "_log_with_context", "line": 132, "asctime": "2025-07-01 09:04:24,023"}
{"timestamp": "2025-07-01T08:04:24.059249Z", "level": "INFO", "logger": "ai_brain", "message": "Received signal 2", "module": "logger", "function": "_log_with_context", "line": 132, "asctime": "2025-07-01 09:04:24,057"}
{"timestamp": "2025-07-01T08:04:24.089382Z", "level": "INFO", "logger": "ai_brain", "message": "Received signal 2", "module": "logger", "function": "_log_with_context", "line": 132, "asctime": "2025-07-01 09:04:24,088"}
{"timestamp": "2025-07-01T08:04:24.121842Z", "level": "INFO", "logger": "ai_brain", "message": "Received signal 2", "module": "logger", "function": "_log_with_context", "line": 132, "asctime": "2025-07-01 09:04:24,118"}
{"timestamp": "2025-07-01T08:04:24.153179Z", "level": "INFO", "logger": "ai_brain", "message": "Received signal 2", "module": "logger", "function": "_log_with_context", "line": 132, "asctime": "2025-07-01 09:04:24,152"}
{"timestamp": "2025-07-01T08:04:24.186405Z", "level": "INFO", "logger": "ai_brain", "message": "Received signal 2", "module": "logger", "function": "_log_with_context", "line": 132, "asctime": "2025-07-01 09:04:24,184"}
{"timestamp": "2025-07-01T08:04:37.966268Z", "level": "INFO", "logger": "ai_brain", "message": "Received signal 2", "module": "logger", "function": "_log_with_context", "line": 132, "asctime": "2025-07-01 09:04:37,964"}
{"timestamp": "2025-07-01T08:04:38.360100Z", "level": "INFO", "logger": "ai_brain", "message": "Received signal 2", "module": "logger", "function": "_log_with_context", "line": 132, "asctime": "2025-07-01 09:04:38,356"}
{"timestamp": "2025-07-01T08:04:38.634638Z", "level": "INFO", "logger": "ai_brain", "message": "Received signal 2", "module": "logger", "function": "_log_with_context", "line": 132, "asctime": "2025-07-01 09:04:38,633"}
{"timestamp": "2025-07-01T08:04:40.862887Z", "level": "INFO", "logger": "ai_brain", "message": "Received signal 2", "module": "logger", "function": "_log_with_context", "line": 132, "asctime": "2025-07-01 09:04:40,860"}
{"timestamp": "2025-07-01T08:05:12.074904Z", "level": "INFO", "logger": "ai_brain", "message": "Performance metric: analyses_per_hour=0.0analyses/hour", "module": "logger", "function": "_log_with_context", "line": 132, "context": {"user_id": null, "session_id": null, "operation": "performance_metric", "model_version": null, "processing_time_ms": null, "confidence_score": null, "additional_data": {"metric_name": "analyses_per_hour", "value": 0.0, "unit": "analyses/hour"}}, "asctime": "2025-07-01 09:05:12,069"}
{"timestamp": "2025-07-01T08:05:12.080941Z", "level": "INFO", "logger": "ai_brain", "message": "Performance metric: rewards_per_hour=0.0rewards/hour", "module": "logger", "function": "_log_with_context", "line": 132, "context": {"user_id": null, "session_id": null, "operation": "performance_metric", "model_version": null, "processing_time_ms": null, "confidence_score": null, "additional_data": {"metric_name": "rewards_per_hour", "value": 0.0, "unit": "rewards/hour"}}, "asctime": "2025-07-01 09:05:12,074"}
{"timestamp": "2025-07-01T08:05:12.099398Z", "level": "INFO", "logger": "ai_brain", "message": "Performance metric: error_rate=0.0errors/operation", "module": "logger", "function": "_log_with_context", "line": 132, "context": {"user_id": null, "session_id": null, "operation": "performance_metric", "model_version": null, "processing_time_ms": null, "confidence_score": null, "additional_data": {"metric_name": "error_rate", "value": 0.0, "unit": "errors/operation"}}, "asctime": "2025-07-01 09:05:12,080"}
{"timestamp": "2025-07-01T08:05:20.144370Z", "level": "ERROR", "logger": "ai_brain", "message": "Error getting batch users for analysis: column u.status does not exist", "module": "logger", "function": "_log_with_context", "line": 132, "asctime": "2025-07-01 09:05:20,143"}
{"timestamp": "2025-07-01T08:10:12.100592Z", "level": "INFO", "logger": "ai_brain", "message": "Performance metric: analyses_per_hour=0.0analyses/hour", "module": "logger", "function": "_log_with_context", "line": 132, "context": {"user_id": null, "session_id": null, "operation": "performance_metric", "model_version": null, "processing_time_ms": null, "confidence_score": null, "additional_data": {"metric_name": "analyses_per_hour", "value": 0.0, "unit": "analyses/hour"}}, "asctime": "2025-07-01 09:10:12,098"}
{"timestamp": "2025-07-01T08:10:12.118269Z", "level": "INFO", "logger": "ai_brain", "message": "Performance metric: rewards_per_hour=0.0rewards/hour", "module": "logger", "function": "_log_with_context", "line": 132, "context": {"user_id": null, "session_id": null, "operation": "performance_metric", "model_version": null, "processing_time_ms": null, "confidence_score": null, "additional_data": {"metric_name": "rewards_per_hour", "value": 0.0, "unit": "rewards/hour"}}, "asctime": "2025-07-01 09:10:12,100"}
{"timestamp": "2025-07-01T08:10:12.135049Z", "level": "INFO", "logger": "ai_brain", "message": "Performance metric: error_rate=0.0errors/operation", "module": "logger", "function": "_log_with_context", "line": 132, "context": {"user_id": null, "session_id": null, "operation": "performance_metric", "model_version": null, "processing_time_ms": null, "confidence_score": null, "additional_data": {"metric_name": "error_rate", "value": 0.0, "unit": "errors/operation"}}, "asctime": "2025-07-01 09:10:12,119"}
{"timestamp": "2025-07-01T08:10:24.819488Z", "level": "ERROR", "logger": "ai_brain", "message": "Error getting batch users for analysis: column u.status does not exist", "module": "logger", "function": "_log_with_context", "line": 132, "asctime": "2025-07-01 09:10:24,817"}
{"timestamp": "2025-07-01T08:12:30.287446Z", "level": "INFO", "logger": "ai_brain", "message": "Received signal 2", "module": "logger", "function": "_log_with_context", "line": 132, "asctime": "2025-07-01 09:12:30,273"}
{"timestamp": "2025-07-01T08:12:30.870992Z", "level": "INFO", "logger": "ai_brain", "message": "Received signal 2", "module": "logger", "function": "_log_with_context", "line": 132, "asctime": "2025-07-01 09:12:30,862"}
{"timestamp": "2025-07-01T08:12:31.134658Z", "level": "INFO", "logger": "ai_brain", "message": "Received signal 2", "module": "logger", "function": "_log_with_context", "line": 132, "asctime": "2025-07-01 09:12:31,126"}
{"timestamp": "2025-07-01T08:12:35.342713Z", "level": "INFO", "logger": "ai_brain", "message": "Received signal 2", "module": "logger", "function": "_log_with_context", "line": 132, "asctime": "2025-07-01 09:12:35,340"}
{"timestamp": "2025-07-01T08:12:35.532604Z", "level": "INFO", "logger": "ai_brain", "message": "Received signal 2", "module": "logger", "function": "_log_with_context", "line": 132, "asctime": "2025-07-01 09:12:35,527"}
{"timestamp": "2025-07-01T08:12:35.701675Z", "level": "INFO", "logger": "ai_brain", "message": "Received signal 2", "module": "logger", "function": "_log_with_context", "line": 132, "asctime": "2025-07-01 09:12:35,700"}
{"timestamp": "2025-07-01T08:15:12.165394Z", "level": "INFO", "logger": "ai_brain", "message": "Performance metric: analyses_per_hour=0.0analyses/hour", "module": "logger", "function": "_log_with_context", "line": 132, "context": {"user_id": null, "session_id": null, "operation": "performance_metric", "model_version": null, "processing_time_ms": null, "confidence_score": null, "additional_data": {"metric_name": "analyses_per_hour", "value": 0.0, "unit": "analyses/hour"}}, "asctime": "2025-07-01 09:15:12,158"}
{"timestamp": "2025-07-01T08:15:12.169877Z", "level": "INFO", "logger": "ai_brain", "message": "Performance metric: rewards_per_hour=0.0rewards/hour", "module": "logger", "function": "_log_with_context", "line": 132, "context": {"user_id": null, "session_id": null, "operation": "performance_metric", "model_version": null, "processing_time_ms": null, "confidence_score": null, "additional_data": {"metric_name": "rewards_per_hour", "value": 0.0, "unit": "rewards/hour"}}, "asctime": "2025-07-01 09:15:12,167"}
{"timestamp": "2025-07-01T08:15:12.175963Z", "level": "INFO", "logger": "ai_brain", "message": "Performance metric: error_rate=0.0errors/operation", "module": "logger", "function": "_log_with_context", "line": 132, "context": {"user_id": null, "session_id": null, "operation": "performance_metric", "model_version": null, "processing_time_ms": null, "confidence_score": null, "additional_data": {"metric_name": "error_rate", "value": 0.0, "unit": "errors/operation"}}, "asctime": "2025-07-01 09:15:12,171"}
{"timestamp": "2025-07-01T08:15:26.059503Z", "level": "ERROR", "logger": "ai_brain", "message": "Error getting batch users for analysis: column u.status does not exist", "module": "logger", "function": "_log_with_context", "line": 132, "asctime": "2025-07-01 09:15:26,052"}
