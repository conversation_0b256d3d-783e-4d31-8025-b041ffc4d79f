# PayVendy Documentation

This folder contains all documentation files for the PayVendy project.

## Documentation Structure

### Main Documentation
- `README.md` - This file, overview of documentation structure
- `PROJECT_README.md` - Main project README (moved from root)

### Architecture & Implementation Guides
- `FRONTEND_MODULAR_ARCHITECTURE.md` - Frontend modular architecture documentation
- `FRONTEND_REORGANIZATION_SUMMARY.md` - Summary of frontend reorganization
- `NATIVE_AUTH_IMPLEMENTATION.md` - Native authentication implementation guide
- `GOOGLE_SIGNIN_REFACTOR.md` - Google Sign-In refactoring documentation

### Security Documentation
- `SECURITY_INTEGRATION_GUIDE.md` - Security integration guide
- `SECURITY_OPTIMIZATION_README.md` - Security optimization documentation
- `SECURITY_PERFORMANCE_RECOMMENDATIONS.md` - Security and performance recommendations

### AI Brain Documentation
- `AI_BRAIN_README.md` - AI Brain service documentation (from ai_brain folder)
- `SUPABASE_SETUP.md` - Supabase setup guide for AI Brain

### Assets Documentation
- `ASSETS_README.md` - Assets folder documentation

### Project Management
- `TODO_COMPREHENSIVE.md` - Comprehensive TODO list

## Quick Navigation

### For Developers
- Start with `PROJECT_README.md` for project overview
- Review `FRONTEND_MODULAR_ARCHITECTURE.md` for code organization
- Check `SECURITY_INTEGRATION_GUIDE.md` for security implementation

### For Security
- `SECURITY_OPTIMIZATION_README.md` - Complete security setup
- `SECURITY_PERFORMANCE_RECOMMENDATIONS.md` - Best practices

### For AI Features
- `AI_BRAIN_README.md` - AI service setup and usage
- `SUPABASE_SETUP.md` - Database configuration

### For Authentication
- `NATIVE_AUTH_IMPLEMENTATION.md` - Native auth setup
- `GOOGLE_SIGNIN_REFACTOR.md` - Google Sign-In integration

## File Organization

All documentation files have been moved from their original locations to this centralized `docs` folder for better organization and easier maintenance.
