import React, { memo, useMemo } from 'react'
import { View, StyleSheet, DimensionValue } from 'react-native'
import { useOptimizedWaveConfig } from '../../utils/performance'

interface WaveProps {
  backgroundColor: string
  containerHeight?: number
}

// Memoized individual wave segment
const WaveSegment = memo<{
  left: DimensionValue
  width: number
  height: number
  backgroundColor: string
  marginLeft: number
}>(({ left, width, height, backgroundColor, marginLeft }) => (
  <View
    style={[
      styles.waveSegment,
      {
        left,
        width,
        height,
        backgroundColor,
        marginLeft,
      }
    ]}
  />
))

WaveSegment.displayName = 'WaveSegment'

// Optimized wave transition component
const OptimizedWave = memo<WaveProps>(({ backgroundColor, containerHeight = 60 }) => {
  const waveConfig = useOptimizedWaveConfig()
  
  // Memoize wave segments calculation
  const waveSegments = useMemo(() => {
    const segments: Array<{
      key: string
      left: DimensionValue
      width: number
      height: number
      backgroundColor: string
      marginLeft: number
    }> = []
    const { segments: totalSegments, amplitude, frequency, waveWidth, overlap } = waveConfig
    
    for (let i = 0; i < totalSegments; i++) {
      const x = (i / totalSegments) * 100
      const angle = (i / totalSegments) * frequency * 2 * Math.PI
      const height = Math.sin(angle) * amplitude + amplitude + 20
      
      segments.push({
        key: `wave-${i}`,
        left: `${x}%` as DimensionValue,
        width: waveWidth,
        height,
        backgroundColor,
        marginLeft: overlap,
      })
    }
    
    return segments
  }, [waveConfig, backgroundColor])

  // Memoize container styles
  const containerStyle = useMemo(() => [
    styles.wavyContainer,
    { height: containerHeight }
  ], [containerHeight])

  // Memoize background fill style
  const backgroundFillStyle = useMemo(() => [
    styles.waveBackgroundFill,
    { backgroundColor }
  ], [backgroundColor])

  return (
    <View style={containerStyle}>
      {/* Solid background fill */}
      <View style={backgroundFillStyle} />
      
      {/* Render wave segments */}
      {waveSegments.map((segment) => (
        <WaveSegment
          key={segment.key}
          left={segment.left}
          width={segment.width}
          height={segment.height}
          backgroundColor={segment.backgroundColor}
          marginLeft={segment.marginLeft}
        />
      ))}
    </View>
  )
})

OptimizedWave.displayName = 'OptimizedWave'

const styles = StyleSheet.create({
  wavyContainer: {
    position: 'absolute',
    bottom: 0,
    left: 0,
    right: 0,
    flexDirection: 'row',
    overflow: 'hidden',
    borderWidth: 0,
    borderBottomWidth: 0,
  },
  waveSegment: {
    position: 'absolute',
    bottom: 0,
    borderTopLeftRadius: 4,
    borderTopRightRadius: 4,
    borderWidth: 0,
    borderBottomWidth: 0,
  },
  waveBackgroundFill: {
    position: 'absolute',
    bottom: 0,
    left: 0,
    right: 0,
    height: 20,
  },
})

export default OptimizedWave
