import React from 'react';
import Svg, { Path } from 'react-native-svg';

interface HomeIconProps {
  size?: number;
  color?: string;
}

// Modern, bold home icon
const HomeIcon: React.FC<HomeIconProps> = ({ size = 28, color = '#000' }) => (
  <Svg width={size} height={size} viewBox="0 0 24 24" fill="none">
    <Path d="M3 11.5L12 4l9 7.5V19a2 2 0 0 1-2 2h-3.5a.5.5 0 0 1-.5-.5V15a1 1 0 0 0-2 0v5.5a.5.5 0 0 1-.5.5H5a2 2 0 0 1-2-2V11.5Z" stroke={color} strokeWidth={2} strokeLinejoin="round"/>
  </Svg>
);

export default HomeIcon;