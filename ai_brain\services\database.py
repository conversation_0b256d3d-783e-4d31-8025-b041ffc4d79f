"""
PayVendy AI Brain Database Service

Handles all database operations for the AI Brain system including
user data retrieval, analytics storage, and reward management.
"""

import asyncio
import asyncpg
import json
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Any, Tuple
from dataclasses import asdict
import logging


class DatabaseService:
    """Database service for AI Brain operations."""
    
    def __init__(self, config, logger):
        self.config = config
        self.logger = logger
        self.pool: Optional[asyncpg.Pool] = None
        
    async def initialize(self) -> None:
        """Initialize database connection pool."""
        try:
            self.logger.info("Initializing database connection pool...")
            self.logger.info(f"Connecting to database: {self.config.database.url[:50]}...")
            
            # Add connection timeout and retry logic
            max_retries = 3
            retry_delay = 5  # seconds
            
            for attempt in range(max_retries):
                try:
                    self.pool = await asyncpg.create_pool(
                        self.config.database.url,
                        min_size=self.config.database.min_pool_size,
                        max_size=self.config.database.max_pool_size,
                        command_timeout=self.config.database.statement_timeout,
                        server_settings={
                            'application_name': 'payvendy_ai_brain'
                        }
                    )
                    
                    # Test connection
                    async with self.pool.acquire() as conn:
                        result = await conn.fetchval('SELECT 1')
                        self.logger.info(f"Database connection test successful: {result}")
                    
                    self.logger.info("Database connection pool initialized successfully")
                    return
                    
                except Exception as e:
                    self.logger.warning(f"Database connection attempt {attempt + 1}/{max_retries} failed: {str(e)}")
                    if attempt < max_retries - 1:
                        self.logger.info(f"Retrying in {retry_delay} seconds...")
                        await asyncio.sleep(retry_delay)
                    else:
                        raise
            
        except Exception as e:
            self.logger.error(f"Failed to initialize database after {max_retries} attempts: {str(e)}")
            self.logger.error("Please check:")
            self.logger.error("1. Database server is running and accessible")
            self.logger.error("2. DATABASE_URL is correct")
            self.logger.error("3. Network connectivity to the database")
            self.logger.error("4. Database credentials are valid")
            raise
    
    async def close(self) -> None:
        """Close database connection pool."""
        if self.pool:
            await self.pool.close()
            self.logger.info("Database connection pool closed")
    
    async def get_user_data(self, user_id: str) -> Optional[Dict[str, Any]]:
        """Get comprehensive user data for analysis."""
        try:
            async with self.pool.acquire() as conn:
                # Get basic user info
                user_query = """
                    SELECT id, email, phone_number, created_at, updated_at, 
                           balance, is_active, last_login
                    FROM users 
                    WHERE id = $1
                """
                user_row = await conn.fetchrow(user_query, user_id)
                
                if not user_row:
                    return None
                
                # Get transaction history (last 90 days)
                transactions_query = """
                    SELECT id, amount, type, status, created_at, description
                    FROM transactions 
                    WHERE user_id = $1 
                    AND created_at >= $2
                    ORDER BY created_at DESC
                """
                transaction_rows = await conn.fetch(
                    transactions_query, 
                    user_id, 
                    datetime.utcnow() - timedelta(days=90)
                )
                
                # Get existing segments
                segments_query = """
                    SELECT segment_name, segment_score, attributes, last_updated
                    FROM user_segments 
                    WHERE user_id = $1
                """
                segment_rows = await conn.fetch(segments_query, user_id)
                
                # Get recent behavior analytics
                analytics_query = """
                    SELECT analysis_type, metrics, insights, confidence_level, created_at
                    FROM behavior_analytics 
                    WHERE user_id = $1 
                    AND created_at >= $2
                    ORDER BY created_at DESC
                    LIMIT 10
                """
                analytics_rows = await conn.fetch(
                    analytics_query, 
                    user_id, 
                    datetime.utcnow() - timedelta(days=30)
                )
                
                # Calculate days since last activity
                last_activity = user_row['last_login'] or user_row['created_at']
                days_since_activity = (datetime.utcnow() - last_activity.replace(tzinfo=None)).days
                
                return {
                    'user_id': str(user_row['id']),
                    'email': user_row['email'],
                    'phone_number': user_row['phone_number'],
                    'created_at': user_row['created_at'],
                    'updated_at': user_row['updated_at'],
                    'balance': float(user_row['balance']) if user_row['balance'] else 0.0,
                    'is_active': user_row['is_active'],
                    'last_login': user_row['last_login'],
                    'days_since_last_activity': days_since_activity,
                    'transactions': [dict(row) for row in transaction_rows],
                    'segments': [dict(row) for row in segment_rows],
                    'analytics': [dict(row) for row in analytics_rows]
                }
                
        except Exception as e:
            self.logger.error(f"Error getting user data for {user_id}: {str(e)}")
            raise
    
    async def get_transaction_analytics(self, user_id: str, days: int = 30) -> Dict[str, Any]:
        """Get transaction analytics for a user."""
        try:
            async with self.pool.acquire() as conn:
                query = """
                    SELECT 
                        COUNT(*) as transaction_count,
                        COALESCE(SUM(CASE WHEN type = 'debit' THEN amount ELSE 0 END), 0) as total_spent,
                        COALESCE(SUM(CASE WHEN type = 'credit' THEN amount ELSE 0 END), 0) as total_received,
                        COALESCE(AVG(amount), 0) as avg_transaction_amount,
                        COUNT(DISTINCT DATE(created_at)) as active_days,
                        MAX(created_at) as last_transaction_date
                    FROM transactions 
                    WHERE user_id = $1 
                    AND created_at >= $2
                    AND status = 'completed'
                """
                
                result = await conn.fetchrow(
                    query, 
                    user_id, 
                    datetime.utcnow() - timedelta(days=days)
                )
                
                # Calculate velocity (transactions per day)
                velocity = result['transaction_count'] / days if days > 0 else 0
                
                # Calculate lifetime value
                lifetime_query = """
                    SELECT COALESCE(SUM(amount), 0) as lifetime_value
                    FROM transactions 
                    WHERE user_id = $1 
                    AND type = 'debit'
                    AND status = 'completed'
                """
                lifetime_result = await conn.fetchrow(lifetime_query, user_id)
                
                return {
                    'transaction_count': result['transaction_count'],
                    'total_spent': float(result['total_spent']),
                    'total_received': float(result['total_received']),
                    'avg_transaction_amount': float(result['avg_transaction_amount']),
                    'active_days': result['active_days'],
                    'velocity': velocity,
                    'lifetime_value': float(lifetime_result['lifetime_value']),
                    'last_transaction_date': result['last_transaction_date']
                }
                
        except Exception as e:
            self.logger.error(f"Error getting transaction analytics for {user_id}: {str(e)}")
            raise
    
    async def log_ai_action(self, action_type: str, description: str, 
                           user_id: Optional[str] = None, input_data: Optional[Dict] = None,
                           output_data: Optional[Dict] = None, confidence_score: Optional[float] = None,
                           processing_time_ms: Optional[int] = None) -> None:
        """Log AI action to database."""
        try:
            async with self.pool.acquire() as conn:
                await conn.execute("""
                    INSERT INTO ai_logs (user_id, action_type, description, input_data, 
                                       output_data, confidence_score, processing_time_ms)
                    VALUES ($1, $2, $3, $4, $5, $6, $7)
                """, user_id, action_type, description, 
                json.dumps(input_data or {}), json.dumps(output_data or {}),
                confidence_score, processing_time_ms)
                
        except Exception as e:
            self.logger.error(f"Error logging AI action: {str(e)}")
            # Don't raise - logging failures shouldn't break the main flow
    
    async def save_user_analysis(self, user_id: str, analysis: Dict[str, Any]) -> None:
        """Save user behavior analysis to database."""
        try:
            async with self.pool.acquire() as conn:
                # Save to behavior_analytics table
                await conn.execute("""
                    INSERT INTO behavior_analytics (user_id, analysis_type, period_start, 
                                                  period_end, metrics, insights, recommendations, 
                                                  confidence_level)
                    VALUES ($1, $2, $3, $4, $5, $6, $7, $8)
                """, user_id, 'comprehensive_analysis',
                datetime.utcnow() - timedelta(days=30), datetime.utcnow(),
                json.dumps(analysis.get('metrics', {})),
                json.dumps(analysis.get('insights', {})),
                json.dumps(analysis.get('recommendations', [])),
                analysis.get('confidence_score', 0.0))
                
                # Update user segment
                segment = analysis.get('segment')
                if segment:
                    await conn.execute("""
                        INSERT INTO user_segments (user_id, segment_name, segment_score, 
                                                 attributes, last_updated)
                        VALUES ($1, $2, $3, $4, $5)
                        ON CONFLICT (user_id, segment_name) 
                        DO UPDATE SET 
                            segment_score = EXCLUDED.segment_score,
                            attributes = EXCLUDED.attributes,
                            last_updated = EXCLUDED.last_updated
                    """, user_id, segment, analysis.get('engagement_score', 0.0),
                    json.dumps(analysis), datetime.utcnow())
                
        except Exception as e:
            self.logger.error(f"Error saving user analysis for {user_id}: {str(e)}")
            raise
    
    async def queue_reward(self, user_id: str, reward_type: str, amount: float, 
                          reason: str, expires_at: Optional[datetime] = None,
                          metadata: Optional[Dict] = None) -> str:
        """Queue a reward for a user."""
        try:
            async with self.pool.acquire() as conn:
                reward_id = await conn.fetchval("""
                    INSERT INTO reward_queue (user_id, reward_type, amount, reason, 
                                            expires_at, metadata)
                    VALUES ($1, $2, $3, $4, $5, $6)
                    RETURNING id
                """, user_id, reward_type, amount, reason, expires_at, 
                json.dumps(metadata or {}))
                
                return str(reward_id)
                
        except Exception as e:
            self.logger.error(f"Error queueing reward for {user_id}: {str(e)}")
            raise
    
    async def get_pending_rewards(self, user_id: Optional[str] = None) -> List[Dict[str, Any]]:
        """Get pending rewards for processing."""
        try:
            async with self.pool.acquire() as conn:
                if user_id:
                    query = """
                        SELECT id, user_id, reward_type, amount, reason, expires_at, 
                               metadata, created_at
                        FROM reward_queue 
                        WHERE user_id = $1 AND status = 'pending'
                        AND (expires_at IS NULL OR expires_at > NOW())
                        ORDER BY created_at ASC
                    """
                    rows = await conn.fetch(query, user_id)
                else:
                    query = """
                        SELECT id, user_id, reward_type, amount, reason, expires_at, 
                               metadata, created_at
                        FROM reward_queue 
                        WHERE status = 'pending'
                        AND (expires_at IS NULL OR expires_at > NOW())
                        ORDER BY created_at ASC
                        LIMIT 100
                    """
                    rows = await conn.fetch(query)
                
                return [dict(row) for row in rows]
                
        except Exception as e:
            self.logger.error(f"Error getting pending rewards: {str(e)}")
            raise
    
    async def apply_reward(self, reward_id: str) -> bool:
        """Apply a queued reward to user balance."""
        try:
            async with self.pool.acquire() as conn:
                # Get reward details
                reward = await conn.fetchrow("""
                    SELECT user_id, amount, reason, reward_type
                    FROM reward_queue 
                    WHERE id = $1 AND status = 'pending'
                """, reward_id)
                
                if not reward:
                    return False
                
                # Apply reward using the database function
                success = await conn.fetchval("""
                    SELECT apply_reward($1, $2, $3, $4)
                """, reward['user_id'], reward['amount'], reward['reason'], reward_id)
                
                return success
                
        except Exception as e:
            self.logger.error(f"Error applying reward {reward_id}: {str(e)}")
            raise
    
    async def get_daily_rewards_count(self, user_id: str) -> int:
        """Get count of rewards given to user today."""
        try:
            async with self.pool.acquire() as conn:
                count = await conn.fetchval("""
                    SELECT COUNT(*)
                    FROM reward_queue 
                    WHERE user_id = $1 
                    AND status = 'applied'
                    AND applied_at >= CURRENT_DATE
                """, user_id)
                
                return count or 0
                
        except Exception as e:
            self.logger.error(f"Error getting daily rewards count for {user_id}: {str(e)}")
            return 0
    
    async def is_user_reward_eligible(self, user_id: str) -> bool:
        """Check if user is eligible for rewards."""
        try:
            async with self.pool.acquire() as conn:
                user = await conn.fetchrow("""
                    SELECT is_active
                    FROM users 
                    WHERE id = $1
                """, user_id)
                
                return user and user['is_active'] == True
                
        except Exception as e:
            self.logger.error(f"Error checking reward eligibility for {user_id}: {str(e)}")
            return False
    
    async def log_fraud_alert(self, user_id: str, fraud_data: Dict[str, Any]) -> None:
        """Log fraud detection alert."""
        try:
            async with self.pool.acquire() as conn:
                await conn.execute("""
                    INSERT INTO ai_logs (user_id, action_type, description, output_data, 
                                       confidence_score)
                    VALUES ($1, $2, $3, $4, $5)
                """, user_id, 'fraud_alert', 
                f"High fraud risk detected: {fraud_data.get('risk_score', 0):.2f}",
                json.dumps(fraud_data), fraud_data.get('risk_score', 0.0))
                
        except Exception as e:
            self.logger.error(f"Error logging fraud alert for {user_id}: {str(e)}")
    
    async def cleanup_old_data(self) -> int:
        """Clean up old AI data."""
        try:
            async with self.pool.acquire() as conn:
                deleted_count = await conn.fetchval("SELECT cleanup_old_ai_data()")
                return deleted_count or 0
                
        except Exception as e:
            self.logger.error(f"Error cleaning up old data: {str(e)}")
            return 0
    
    async def get_batch_users_for_analysis(self, limit: int = 100) -> List[str]:
        """Get batch of users that need analysis."""
        try:
            async with self.pool.acquire() as conn:
                # Get users who haven't been analyzed recently or are new
                query = """
                    SELECT DISTINCT u.id
                    FROM users u
                    LEFT JOIN behavior_analytics ba ON u.id = ba.user_id 
                        AND ba.analysis_type = 'comprehensive_analysis'
                        AND ba.created_at >= NOW() - INTERVAL '24 hours'
                    WHERE u.is_active = true
                    AND ba.id IS NULL
                    ORDER BY u.updated_at DESC
                    LIMIT $1
                """
                
                rows = await conn.fetch(query, limit)
                return [str(row['id']) for row in rows]
                
        except Exception as e:
            self.logger.error(f"Error getting batch users for analysis: {str(e)}")
            return []