/**
 * Authentication Service
 * 
 * Centralized authentication service that handles all authentication-related
 * operations including login, logout, token management, and user state.
 */

import { User, ApiResponse } from '../../../core/types';
import { logger } from '../../../infrastructure/monitoring/productionLogger';

interface AuthTokens {
  accessToken: string;
  refreshToken: string;
  expiresAt: number;
}

interface LoginCredentials {
  phoneNumber?: string;
  email?: string;
  pin?: string;
  verificationCode?: string;
}

interface AuthState {
  isAuthenticated: boolean;
  user: User | null;
  tokens: AuthTokens | null;
  lastActivity: number;
}

export class AuthenticationService {
  private apiService: any;
  private secureStorage: any;
  private inputValidation: any;
  private runtimeSecurity: any;
  
  private authState: AuthState = {
    isAuthenticated: false,
    user: null,
    tokens: null,
    lastActivity: Date.now()
  };

  private authStateListeners: ((state: AuthState) => void)[] = [];
  private tokenRefreshTimer: NodeJS.Timeout | null = null;
  private sessionTimeoutTimer: NodeJS.Timeout | null = null;
  private readonly SESSION_TIMEOUT = 30 * 60 * 1000; // 30 minutes
  private readonly TOKEN_REFRESH_BUFFER = 5 * 60 * 1000; // 5 minutes before expiry

  /**
   * Inject dependencies
   */
  async inject(apiService: any, secureStorage: any, inputValidation: any, runtimeSecurity: any): Promise<void> {
    this.apiService = apiService;
    this.secureStorage = secureStorage;
    this.inputValidation = inputValidation;
    this.runtimeSecurity = runtimeSecurity;

    // Initialize authentication state
    await this.initializeAuthState();
  }

  /**
   * Initialize authentication state from stored data
   */
  private async initializeAuthState(): Promise<void> {
    try {
      logger.info('Initializing authentication state', null, 'auth');

      // Load stored tokens
      const storedTokens = await this.secureStorage.getSecureItem('auth_tokens');
      const storedUser = await this.secureStorage.getSecureItem('user_data');

      if (storedTokens && storedUser) {
        const tokens: AuthTokens = JSON.parse(storedTokens);
        const user: User = JSON.parse(storedUser);

        // Check if tokens are still valid
        if (tokens.expiresAt > Date.now()) {
          this.authState = {
            isAuthenticated: true,
            user,
            tokens,
            lastActivity: Date.now()
          };

          // Set up token refresh
          this.scheduleTokenRefresh();
          this.startSessionTimeout();

          logger.info('Authentication state restored from storage', {
            userId: user.id,
            expiresAt: new Date(tokens.expiresAt).toISOString()
          }, 'auth');
        } else {
          logger.info('Stored tokens expired, clearing auth state', null, 'auth');
          await this.clearStoredAuthData();
        }
      }

      this.notifyAuthStateChange();
    } catch (error) {
      logger.error('Failed to initialize authentication state', error, 'auth');
      await this.clearStoredAuthData();
    }
  }

  /**
   * Authenticate with phone number and verification code
   */
  async authenticateWithPhone(phoneNumber: string, verificationCode: string): Promise<boolean> {
    try {
      // Validate inputs
      const phoneValidation = this.inputValidation.validatePhone(phoneNumber);
      if (!phoneValidation.isValid) {
        throw new Error(`Invalid phone number: ${phoneValidation.errors.join(', ')}`);
      }

      // Check security status
      const securityStatus = await this.runtimeSecurity.getSecurityStatus();
      if (!securityStatus.isSecure && securityStatus.riskLevel === 'critical') {
        throw new Error('Authentication blocked due to security concerns');
      }

      logger.info('Authenticating with phone number', {
        phoneNumber: '***REDACTED***',
        securityRisk: securityStatus.riskLevel
      }, 'auth');

      const response = await this.apiService.post('/auth/phone/verify', {
        phoneNumber: phoneValidation.sanitizedValue,
        verificationCode,
        deviceFingerprint: this.runtimeSecurity.getDeviceFingerprint()
      });

      if (response.data.success) {
        await this.handleSuccessfulAuthentication(response.data);
        return true;
      }

      return false;
    } catch (error) {
      logger.error('Phone authentication failed', error, 'auth');
      throw error;
    }
  }

  /**
   * Authenticate with email and verification code
   */
  async authenticateWithEmail(email: string, verificationCode: string): Promise<boolean> {
    try {
      // Validate inputs
      const emailValidation = this.inputValidation.validateEmail(email);
      if (!emailValidation.isValid) {
        throw new Error(`Invalid email: ${emailValidation.errors.join(', ')}`);
      }

      // Check security status
      const securityStatus = await this.runtimeSecurity.getSecurityStatus();
      if (!securityStatus.isSecure && securityStatus.riskLevel === 'critical') {
        throw new Error('Authentication blocked due to security concerns');
      }

      logger.info('Authenticating with email', {
        email: '***REDACTED***',
        securityRisk: securityStatus.riskLevel
      }, 'auth');

      const response = await this.apiService.post('/auth/email/verify', {
        email: emailValidation.sanitizedValue,
        verificationCode,
        deviceFingerprint: this.runtimeSecurity.getDeviceFingerprint()
      });

      if (response.data.success) {
        await this.handleSuccessfulAuthentication(response.data);
        return true;
      }

      return false;
    } catch (error) {
      logger.error('Email authentication failed', error, 'auth');
      throw error;
    }
  }

  /**
   * Authenticate with PIN
   */
  async authenticateWithPin(pin: string): Promise<boolean> {
    try {
      // Validate PIN
      const pinValidation = this.inputValidation.validatePin(pin);
      if (!pinValidation.isValid) {
        throw new Error(`Invalid PIN: ${pinValidation.errors.join(', ')}`);
      }

      if (!this.authState.user) {
        throw new Error('No user context for PIN authentication');
      }

      logger.info('Authenticating with PIN', {
        userId: this.authState.user.id,
        pinStrength: pinValidation.riskLevel
      }, 'auth');

      const response = await this.apiService.post('/auth/pin/verify', {
        userId: this.authState.user.id,
        pin: pinValidation.sanitizedValue,
        deviceFingerprint: this.runtimeSecurity.getDeviceFingerprint()
      });

      if (response.data.success) {
        await this.handleSuccessfulAuthentication(response.data);
        return true;
      }

      return false;
    } catch (error) {
      logger.error('PIN authentication failed', error, 'auth');
      throw error;
    }
  }

  /**
   * Handle successful authentication
   */
  private async handleSuccessfulAuthentication(authData: any): Promise<void> {
    try {
      const { user, tokens } = authData;

      // Update auth state
      this.authState = {
        isAuthenticated: true,
        user,
        tokens: {
          accessToken: tokens.accessToken,
          refreshToken: tokens.refreshToken,
          expiresAt: Date.now() + (tokens.expiresIn * 1000)
        },
        lastActivity: Date.now()
      };

      // Store auth data securely
      await this.storeAuthData();

      // Set up token refresh and session management
      this.scheduleTokenRefresh();
      this.startSessionTimeout();

      // Notify listeners
      this.notifyAuthStateChange();

      logger.info('Authentication successful', {
        userId: user.id,
        expiresAt: new Date(this.authState.tokens!.expiresAt).toISOString()
      }, 'auth');
    } catch (error) {
      logger.error('Failed to handle successful authentication', error, 'auth');
      throw error;
    }
  }

  /**
   * Refresh authentication token
   */
  async refreshToken(): Promise<boolean> {
    try {
      if (!this.authState.tokens?.refreshToken) {
        logger.warn('No refresh token available', null, 'auth');
        return false;
      }

      logger.info('Refreshing authentication token', null, 'auth');

      const response = await this.apiService.post('/auth/refresh', {
        refreshToken: this.authState.tokens.refreshToken,
        deviceFingerprint: this.runtimeSecurity.getDeviceFingerprint()
      });

      if (response.data.success) {
        const { tokens } = response.data;
        
        this.authState.tokens = {
          accessToken: tokens.accessToken,
          refreshToken: tokens.refreshToken,
          expiresAt: Date.now() + (tokens.expiresIn * 1000)
        };

        await this.storeAuthData();
        this.scheduleTokenRefresh();

        logger.info('Token refreshed successfully', {
          expiresAt: new Date(this.authState.tokens.expiresAt).toISOString()
        }, 'auth');

        return true;
      }

      return false;
    } catch (error) {
      logger.error('Token refresh failed', error, 'auth');
      await this.logout();
      return false;
    }
  }

  /**
   * Logout user
   */
  async logout(): Promise<void> {
    try {
      logger.info('Logging out user', {
        userId: this.authState.user?.id
      }, 'auth');

      // Notify server about logout
      if (this.authState.tokens?.accessToken) {
        try {
          await this.apiService.post('/auth/logout', {
            deviceFingerprint: this.runtimeSecurity.getDeviceFingerprint()
          });
        } catch (error) {
          logger.warn('Failed to notify server about logout', error, 'auth');
        }
      }

      // Clear auth state
      this.authState = {
        isAuthenticated: false,
        user: null,
        tokens: null,
        lastActivity: Date.now()
      };

      // Clear stored data
      await this.clearStoredAuthData();

      // Clear timers
      this.clearTimers();

      // Notify listeners
      this.notifyAuthStateChange();

      logger.info('Logout completed', null, 'auth');
    } catch (error) {
      logger.error('Logout failed', error, 'auth');
      throw error;
    }
  }

  /**
   * Store authentication data securely
   */
  private async storeAuthData(): Promise<void> {
    if (this.authState.tokens && this.authState.user) {
      await Promise.all([
        this.secureStorage.setSecureItem('auth_tokens', JSON.stringify(this.authState.tokens)),
        this.secureStorage.setSecureItem('user_data', JSON.stringify(this.authState.user))
      ]);
    }
  }

  /**
   * Clear stored authentication data
   */
  private async clearStoredAuthData(): Promise<void> {
    await Promise.all([
      this.secureStorage.removeSecureItem('auth_tokens'),
      this.secureStorage.removeSecureItem('user_data')
    ]);
  }

  /**
   * Schedule token refresh
   */
  private scheduleTokenRefresh(): void {
    this.clearTokenRefreshTimer();

    if (this.authState.tokens) {
      const refreshTime = this.authState.tokens.expiresAt - this.TOKEN_REFRESH_BUFFER;
      const delay = Math.max(0, refreshTime - Date.now());

      this.tokenRefreshTimer = setTimeout(async () => {
        await this.refreshToken();
      }, delay);

      logger.debug('Token refresh scheduled', {
        refreshAt: new Date(refreshTime).toISOString(),
        delayMs: delay
      }, 'auth');
    }
  }

  /**
   * Start session timeout
   */
  private startSessionTimeout(): void {
    this.clearSessionTimeout();

    this.sessionTimeoutTimer = setTimeout(async () => {
      logger.info('Session timeout reached', null, 'auth');
      await this.logout();
    }, this.SESSION_TIMEOUT);
  }

  /**
   * Clear timers
   */
  private clearTimers(): void {
    this.clearTokenRefreshTimer();
    this.clearSessionTimeout();
  }

  private clearTokenRefreshTimer(): void {
    if (this.tokenRefreshTimer) {
      clearTimeout(this.tokenRefreshTimer);
      this.tokenRefreshTimer = null;
    }
  }

  private clearSessionTimeout(): void {
    if (this.sessionTimeoutTimer) {
      clearTimeout(this.sessionTimeoutTimer);
      this.sessionTimeoutTimer = null;
    }
  }

  /**
   * Notify auth state change listeners
   */
  private notifyAuthStateChange(): void {
    this.authStateListeners.forEach(listener => {
      try {
        listener(this.authState);
      } catch (error) {
        logger.error('Auth state listener error', error, 'auth');
      }
    });
  }

  // Public API methods
  isAuthenticated(): boolean {
    return this.authState.isAuthenticated;
  }

  getCurrentUser(): User | null {
    return this.authState.user;
  }

  getAccessToken(): string | null {
    return this.authState.tokens?.accessToken || null;
  }

  onAuthStateChange(listener: (state: AuthState) => void): () => void {
    this.authStateListeners.push(listener);
    
    // Return unsubscribe function
    return () => {
      const index = this.authStateListeners.indexOf(listener);
      if (index > -1) {
        this.authStateListeners.splice(index, 1);
      }
    };
  }

  updateLastActivity(): void {
    this.authState.lastActivity = Date.now();
    this.startSessionTimeout(); // Reset session timeout
  }

  async cleanup(): Promise<void> {
    this.clearTimers();
    this.authStateListeners = [];
    logger.info('Authentication service cleaned up', null, 'auth');
  }
}
