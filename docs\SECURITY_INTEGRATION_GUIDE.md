# PayVendy Security Integration Guide

## 🔒 Overview

This guide covers the comprehensive security integration implemented in PayVendy, including Firebase Crashlytics and SSL Certificate Pinning.

## 🚀 What's Been Implemented

### 1. Firebase Crashlytics Integration ✅
- **Automatic crash reporting** for production builds
- **Non-fatal error tracking** with custom attributes
- **Breadcrumb logging** for debugging context
- **User identification** and custom attributes
- **Performance monitoring** integration

### 2. SSL Certificate Pinning ✅
- **Certificate validation** for API requests
- **Flexible pinning configuration** for different environments
- **Development mode bypass** for dev tunnels
- **Automatic fallback** if pinning fails
- **Certificate cache management**

### 3. Security Monitoring ✅
- **Global error handlers** for unhandled exceptions
- **Security health checks** every 5 minutes
- **Automatic cleanup** of old logs and caches
- **Comprehensive logging** with different levels

## 📁 Files Added/Modified

### New Files Created:
```
src/services/crashReportingService.ts     # Enhanced Crashlytics integration
src/services/sslPinningService.ts         # SSL certificate pinning
src/services/securityInitService.ts      # Security services initialization
src/utils/sslPinGenerator.ts             # SSL pin generation utility
SECURITY_INTEGRATION_GUIDE.md            # This guide
```

### Modified Files:
```
src/config/initFirebase.ts               # Added Crashlytics export
src/config/environment.ts                # Updated SSL pins configuration
src/services/apiService.ts               # Integrated SSL pinning
src/components/ErrorBoundary.tsx         # Enhanced with Crashlytics
App.tsx                                   # Added security initialization
```

## 🔧 How to Use

### 1. Crashlytics Usage

#### Basic Error Reporting:
```typescript
import crashReporting from './src/services/crashReportingService';

// Report a fatal error
try {
  // Some risky operation
} catch (error) {
  await crashReporting.recordError(error, 'ComponentName', true);
}

// Report a non-fatal error
await crashReporting.recordNonFatalError('Something went wrong', {
  userId: '12345',
  action: 'button_click',
});
```

#### Adding Breadcrumbs:
```typescript
// Navigation breadcrumb
crashReporting.recordNavigation('HomeScreen', { userId: '12345' });

// API call breadcrumb
crashReporting.recordApiCall('POST', '/api/users', 200, 1500);

// User action breadcrumb
crashReporting.recordUserAction('Button clicked', { buttonId: 'submit' });

// Performance issue breadcrumb
crashReporting.recordPerformanceIssue('api_response_time', 3000, 2000);
```

#### Setting User Information:
```typescript
// Set user ID
await crashReporting.setUserId('user123');

// Set custom attributes
await crashReporting.setUserAttributes({
  subscription_type: 'premium',
  app_version: '1.2.0',
  user_type: 'business',
});
```

### 2. SSL Pinning Usage

#### The SSL pinning is automatically integrated into your API service. No additional code needed!

#### Check SSL Status:
```typescript
import sslPinning from './src/services/sslPinningService';

// Get current status
const status = sslPinning.getStatus();
console.log('SSL Pinning Status:', status);

// Test pinning (development only)
if (__DEV__) {
  const result = await sslPinning.testPinning('https://your-api.com');
  console.log('Pin validation result:', result);
}
```

### 3. Security Service Status

#### Check if security services are ready:
```typescript
import securityInit from './src/services/securityInitService';

// Check if services are initialized
const isReady = securityInit.isSecurityReady();

// Get detailed status
const status = securityInit.getSecurityStatus();
console.log('Security Status:', status);

// Test services (development only)
if (__DEV__) {
  const testResults = await securityInit.testSecurityServices();
  console.log('Security Test Results:', testResults);
}
```

## 🔧 Configuration

### Environment Configuration
The security features are configured in `src/config/environment.ts`:

```typescript
// Enable/disable SSL pinning
ENABLE_SSL_PINNING: true, // Set to false to disable

// Enable/disable request signing
ENABLE_REQUEST_SIGNING: true,

// SSL certificate pins
SSL_PINS: {
  'your-production-api.com': [
    'sha256/YourActualCertificatePin=',
    'sha256/YourBackupCertificatePin=',
  ],
}
```

### Crashlytics Configuration
Crashlytics is configured automatically but you can customize:

```typescript
// Update crash reporting config
crashReporting.updateConfig({
  enableCrashlytics: true,
  enableLocalStorage: true,
  maxBreadcrumbs: 50,
  maxLocalErrors: 100,
  autoCleanupDays: 7,
});
```

## 🚨 Production Setup

### 1. Firebase Crashlytics Setup
1. Ensure your `google-services.json` (Android) and `GoogleService-Info.plist` (iOS) are properly configured
2. Crashlytics will automatically start collecting crashes in production builds
3. View crashes in the Firebase Console

### 2. SSL Certificate Pinning Setup

#### For Production APIs:
1. **Extract your production certificate pins:**
```bash
# Method 1: Using OpenSSL
openssl s_client -connect your-api.com:443 -servername your-api.com < /dev/null | openssl x509 -pubkey -noout | openssl rsa -pubin -outform der | openssl dgst -sha256 -binary | openssl enc -base64

# Method 2: Using the built-in utility (development)
import { sslPinGenerator } from './src/utils/sslPinGenerator';
const result = await sslPinGenerator.generatePinsForHostname('your-api.com');
```

2. **Update your environment configuration:**
```typescript
export const SSL_PINS = {
  'your-production-api.com': [
    'sha256/YourActualPrimaryPin=',
    'sha256/YourActualBackupPin=',
    'sha256/YourCABackupPin=',
  ],
};
```

3. **Test before deploying:**
```typescript
// Test in development
if (__DEV__) {
  const isValid = await sslPinGenerator.testPinValidation(
    'your-api.com', 
    ['sha256/YourPin=']
  );
  console.log('Pin validation test:', isValid);
}
```

## 📊 Monitoring & Debugging

### 1. View Crash Reports
- **Firebase Console**: https://console.firebase.google.com
- Navigate to your project → Crashlytics
- View crashes, non-fatal errors, and user sessions

### 2. Local Error Logs (Development)
```typescript
// Get stored errors for debugging
const errors = await crashReporting.getStoredErrors();
console.log('Local errors:', errors);

// Clear old errors
await crashReporting.clearOldErrors(7); // Clear errors older than 7 days
```

### 3. SSL Pinning Logs
SSL pinning events are logged with the 'ssl' category:
- Check your logs for SSL validation results
- Monitor certificate cache size
- Watch for pinning failures

## 🔄 Maintenance

### 1. Certificate Rotation
- **Monitor certificate expiration dates**
- **Update pins before certificates expire**
- **Test new pins in staging first**
- **Have a rollback plan**

### 2. Regular Monitoring
- **Check Firebase Crashlytics dashboard weekly**
- **Monitor SSL pinning logs for failures**
- **Review security health check results**
- **Update pins when certificates change**

## 🚨 Troubleshooting

### Common Issues:

#### 1. Crashlytics Not Working
```typescript
// Check if Crashlytics is ready
if (!crashReporting.isReady()) {
  console.log('Crashlytics not ready yet');
  // Wait and try again
}

// Check Firebase configuration
import { isFirebaseInitialized } from './src/config/initFirebase';
console.log('Firebase initialized:', isFirebaseInitialized());
```

#### 2. SSL Pinning Failures
```typescript
// Check SSL pinning status
const sslStatus = sslPinning.getStatus();
console.log('SSL Status:', sslStatus);

// Clear SSL cache if needed
sslPinning.clearAllCaches();

// Disable SSL pinning temporarily (development only)
if (__DEV__) {
  sslPinning.updateConfig({ enabled: false });
}
```

#### 3. Performance Issues
```typescript
// Check cache sizes
const status = securityInit.getSecurityStatus();
console.log('Cache sizes:', status.sslPinning.status.cacheSize);

// Clear caches if they're too large
if (status.sslPinning.status.cacheSize.certificates > 100) {
  sslPinning.clearAllCaches();
}
```

## 🔐 Security Best Practices

1. **Never disable SSL pinning in production**
2. **Always include backup certificate pins**
3. **Monitor certificate expiration dates**
4. **Test SSL pins before deploying**
5. **Review crash reports regularly**
6. **Keep Firebase SDK updated**
7. **Use non-fatal error reporting for debugging**
8. **Set up alerts for critical crashes**

## 📞 Support

If you encounter issues:
1. Check the logs for detailed error messages
2. Verify Firebase configuration
3. Test SSL pins with the provided utilities
4. Review this guide for troubleshooting steps

## 🎯 Next Steps

1. **Set up production SSL certificates** and extract real pins
2. **Configure Firebase project** for production
3. **Test the integration** thoroughly in staging
4. **Set up monitoring alerts** in Firebase Console
5. **Create a certificate rotation schedule**

---

**Note**: This integration provides enterprise-grade security while maintaining development flexibility. The services automatically adapt to development vs production environments.