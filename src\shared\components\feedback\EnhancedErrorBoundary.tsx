/**
 * Enhanced Error Boundary
 * 
 * Advanced error boundary with comprehensive error handling, recovery mechanisms,
 * performance monitoring, and detailed error reporting.
 */

import React, { Component, ErrorInfo, ReactNode } from 'react';
import { View, Text, StyleSheet, TouchableOpacity, ScrollView, Alert } from 'react-native';
import crashReporting from '../../../infrastructure/monitoring/crashReportingService';
import { logger } from '../../../infrastructure/monitoring/productionLogger';
// Note: advancedPerformanceService was removed as unused
// import { advancedPerformance } from '../services/advancedPerformanceService';

interface Props {
  children: ReactNode;
  fallback?: ReactNode;
  onError?: (error: Error, errorInfo: ErrorInfo) => void;
  level?: 'screen' | 'component' | 'critical';
  name?: string;
  enableRecovery?: boolean;
  maxRetries?: number;
  isolateFailures?: boolean;
}

interface State {
  hasError: boolean;
  error: Error | null;
  errorInfo: ErrorInfo | null;
  retryCount: number;
  errorId: string | null;
  isRecovering: boolean;
}

interface ErrorContext {
  timestamp: string;
  userAgent?: string;
  url?: string;
  userId?: string;
  sessionId?: string;
  performanceMetrics?: any;
  memoryUsage?: number;
  componentHierarchy?: string[];
}

class EnhancedErrorBoundary extends Component<Props, State> {
  private errorContext: ErrorContext = {
    timestamp: new Date().toISOString()
  };

  private recoveryTimer: NodeJS.Timeout | null = null;

  constructor(props: Props) {
    super(props);
    this.state = {
      hasError: false,
      error: null,
      errorInfo: null,
      retryCount: 0,
      errorId: null,
      isRecovering: false,
    };

    // Collect initial context
    this.collectErrorContext();
  }

  componentWillUnmount() {
    if (this.recoveryTimer) {
      clearTimeout(this.recoveryTimer);
    }
  }

  private async collectErrorContext(): Promise<void> {
    try {
      // Get performance metrics
      const performanceStatus = advancedPerformance.getPerformanceStatus();
      
      this.errorContext = {
        ...this.errorContext,
        performanceMetrics: performanceStatus.metrics,
        memoryUsage: performanceStatus.metrics.memoryUsage,
        componentHierarchy: this.buildComponentHierarchy(),
      };
    } catch (error) {
      logger.warn('Failed to collect error context', error, 'enhanced-error-boundary');
    }
  }

  private buildComponentHierarchy(): string[] {
    // Build component hierarchy for better debugging
    const hierarchy: string[] = [];
    
    if (this.props.name) {
      hierarchy.push(this.props.name);
    }
    
    // Add parent component names if available
    // This would require additional context from React DevTools or custom tracking
    
    return hierarchy;
  }

  static getDerivedStateFromError(error: Error): Partial<State> {
    return {
      hasError: true,
      error,
    };
  }

  async componentDidCatch(error: Error, errorInfo: ErrorInfo) {
    const errorId = `error-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;
    
    this.setState({
      error,
      errorInfo,
      errorId,
    });

    // Update error context
    await this.collectErrorContext();

    // Enhanced error logging
    const enhancedErrorInfo = {
      ...errorInfo,
      errorId,
      level: this.props.level || 'component',
      componentName: this.props.name || 'Unknown',
      retryCount: this.state.retryCount,
      context: this.errorContext,
      props: this.sanitizeProps(this.props),
      isolateFailures: this.props.isolateFailures,
    };

    // Log the error with full context
    logger.error('Enhanced error boundary caught an error', error, 'enhanced-error-boundary', enhancedErrorInfo);

    // Report to crash reporting service with enhanced data
    crashReporting.recordError(error, {
      ...enhancedErrorInfo,
      componentStack: errorInfo.componentStack,
      errorBoundary: true,
      enhanced: true,
      severity: this.getErrorSeverity(error, this.props.level),
    });

    // Handle critical errors differently
    if (this.props.level === 'critical') {
      this.handleCriticalError(error, enhancedErrorInfo);
    }

    // Attempt automatic recovery for certain error types
    if (this.shouldAttemptAutoRecovery(error)) {
      this.attemptAutoRecovery();
    }

    // Call custom error handler if provided
    if (this.props.onError) {
      this.props.onError(error, errorInfo);
    }

    // Record performance impact
    advancedPerformance.recordRender(
      `EnhancedErrorBoundary-${this.props.name || 'Unknown'}`,
      performance.now(),
      Object.keys(this.props).length,
      Object.keys(this.state).length
    );
  }

  private sanitizeProps(props: Props): any {
    // Remove sensitive data from props before logging
    const { children, onError, ...safeProps } = props;
    return {
      ...safeProps,
      hasChildren: !!children,
      hasOnError: !!onError,
    };
  }

  private getErrorSeverity(error: Error, level?: string): 'low' | 'medium' | 'high' | 'critical' {
    if (level === 'critical') return 'critical';
    if (level === 'screen') return 'high';
    if (error.name === 'ChunkLoadError' || error.message.includes('Loading chunk')) return 'medium';
    if (error.message.includes('Network Error') || error.message.includes('fetch')) return 'medium';
    if (error.name === 'TypeError' && error.message.includes('Cannot read property')) return 'high';
    return 'medium';
  }

  private shouldAttemptAutoRecovery(error: Error): boolean {
    // Determine if we should attempt automatic recovery
    const recoverableErrors = [
      'ChunkLoadError',
      'Loading chunk',
      'Network Error',
      'fetch',
    ];

    return recoverableErrors.some(pattern => 
      error.name.includes(pattern) || error.message.includes(pattern)
    );
  }

  private attemptAutoRecovery(): void {
    if (this.state.retryCount >= (this.props.maxRetries || 3)) {
      return;
    }

    this.setState({ isRecovering: true });

    logger.info('Attempting automatic error recovery', {
      errorId: this.state.errorId,
      retryCount: this.state.retryCount
    }, 'enhanced-error-boundary');

    // Delay recovery to allow for cleanup
    this.recoveryTimer = setTimeout(() => {
      this.handleRetry();
    }, 2000);
  }

  private handleCriticalError(error: Error, errorInfo: any): void {
    logger.error('Critical error detected', error, 'enhanced-error-boundary', {
      ...errorInfo,
      action: 'critical_error_handling'
    });

    // For critical errors, show immediate alert
    if (!__DEV__) {
      Alert.alert(
        'Critical Error',
        'A critical error has occurred. The app will attempt to recover.',
        [
          {
            text: 'Restart',
            onPress: () => this.handleForceRestart(),
          },
          {
            text: 'Try Again',
            onPress: () => this.handleRetry(),
          },
        ]
      );
    }
  }

  private handleForceRestart = (): void => {
    // Force restart the app or navigate to a safe state
    logger.info('Force restart requested', {
      errorId: this.state.errorId
    }, 'enhanced-error-boundary');

    // This would typically involve navigation to root or app restart
    // For now, we'll clear all state and retry
    this.setState({
      hasError: false,
      error: null,
      errorInfo: null,
      retryCount: 0,
      errorId: null,
      isRecovering: false,
    });
  };

  handleRetry = () => {
    const maxRetries = this.props.maxRetries || 3;
    
    if (this.state.retryCount >= maxRetries) {
      logger.warn('Maximum retry attempts reached', {
        retryCount: this.state.retryCount,
        maxRetries,
        errorId: this.state.errorId
      }, 'enhanced-error-boundary');
      
      Alert.alert(
        'Unable to Recover',
        'The error persists after multiple attempts. Please restart the app.',
        [
          { text: 'Force Restart', onPress: this.handleForceRestart },
          { text: 'Cancel' }
        ]
      );
      return;
    }

    logger.info('Attempting error recovery', {
      retryCount: this.state.retryCount + 1,
      errorId: this.state.errorId
    }, 'enhanced-error-boundary');

    this.setState({
      hasError: false,
      error: null,
      errorInfo: null,
      retryCount: this.state.retryCount + 1,
      errorId: null,
      isRecovering: false,
    });
  };

  handleReportError = () => {
    if (this.state.error && this.state.errorId) {
      logger.info('User reported error', {
        errorId: this.state.errorId,
        userAction: 'manual_report'
      }, 'enhanced-error-boundary');
      
      Alert.alert(
        'Error Reported',
        'Thank you for reporting this error. Our team will investigate.',
        [{ text: 'OK' }]
      );
    }
  };

  render() {
    if (this.state.hasError) {
      // Custom fallback UI
      if (this.props.fallback) {
        return this.props.fallback;
      }

      // Show recovery indicator
      if (this.state.isRecovering) {
        return (
          <View style={styles.container}>
            <View style={styles.errorContainer}>
              <Text style={styles.title}>🔄 Recovering...</Text>
              <Text style={styles.message}>
                Attempting to recover from the error. Please wait.
              </Text>
            </View>
          </View>
        );
      }

      // Enhanced default error UI
      return (
        <View style={styles.container}>
          <View style={styles.errorContainer}>
            <Text style={styles.title}>
              {this.props.level === 'critical' ? '⚠️ Critical Error' : '😔 Something went wrong'}
            </Text>
            <Text style={styles.message}>
              {this.props.level === 'critical' 
                ? 'A critical error has occurred. We\'re working to resolve this immediately.'
                : 'We\'re sorry for the inconvenience. The error has been reported and we\'re working to fix it.'
              }
            </Text>

            {this.state.errorId && (
              <Text style={styles.errorId}>
                Error ID: {this.state.errorId}
              </Text>
            )}

            {this.props.name && (
              <Text style={styles.componentName}>
                Component: {this.props.name}
              </Text>
            )}
            
            {__DEV__ && this.state.error && (
              <ScrollView style={styles.debugContainer}>
                <Text style={styles.debugTitle}>Debug Information:</Text>
                <Text style={styles.debugText}>
                  Level: {this.props.level || 'component'}
                </Text>
                <Text style={styles.debugText}>
                  Retry Count: {this.state.retryCount}/{this.props.maxRetries || 3}
                </Text>
                <Text style={styles.debugText}>
                  Auto Recovery: {this.shouldAttemptAutoRecovery(this.state.error) ? 'Yes' : 'No'}
                </Text>
                <Text style={styles.debugText}>
                  Error: {this.state.error.toString()}
                </Text>
                {this.state.errorInfo && (
                  <Text style={styles.debugText}>
                    Stack: {this.state.errorInfo.componentStack}
                  </Text>
                )}
              </ScrollView>
            )}
            
            <View style={styles.buttonContainer}>
              {this.props.enableRecovery !== false && (
                <TouchableOpacity 
                  style={[styles.button, styles.retryButton]} 
                  onPress={this.handleRetry}
                  disabled={this.state.retryCount >= (this.props.maxRetries || 3)}
                >
                  <Text style={styles.retryButtonText}>
                    Try Again ({this.state.retryCount}/{this.props.maxRetries || 3})
                  </Text>
                </TouchableOpacity>
              )}
              
              <TouchableOpacity 
                style={[styles.button, styles.reportButton]} 
                onPress={this.handleReportError}
              >
                <Text style={styles.reportButtonText}>Report Error</Text>
              </TouchableOpacity>

              {this.props.level === 'critical' && (
                <TouchableOpacity 
                  style={[styles.button, styles.restartButton]} 
                  onPress={this.handleForceRestart}
                >
                  <Text style={styles.restartButtonText}>Force Restart</Text>
                </TouchableOpacity>
              )}
            </View>
          </View>
        </View>
      );
    }

    return this.props.children;
  }
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f5f5f5',
    justifyContent: 'center',
    alignItems: 'center',
    padding: 20,
  },
  errorContainer: {
    backgroundColor: 'white',
    borderRadius: 12,
    padding: 24,
    maxWidth: '100%',
    width: '100%',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 8,
    elevation: 4,
  },
  title: {
    fontSize: 24,
    fontWeight: 'bold',
    color: '#333',
    marginBottom: 16,
    textAlign: 'center',
  },
  message: {
    fontSize: 16,
    color: '#666',
    textAlign: 'center',
    marginBottom: 20,
    lineHeight: 24,
  },
  errorId: {
    fontSize: 12,
    color: '#999',
    textAlign: 'center',
    marginBottom: 8,
    fontFamily: 'monospace',
  },
  componentName: {
    fontSize: 12,
    color: '#999',
    textAlign: 'center',
    marginBottom: 16,
    fontStyle: 'italic',
  },
  debugContainer: {
    backgroundColor: '#f8f8f8',
    borderRadius: 8,
    padding: 16,
    marginBottom: 20,
    maxHeight: 200,
  },
  debugTitle: {
    fontSize: 14,
    fontWeight: 'bold',
    color: '#333',
    marginBottom: 8,
  },
  debugText: {
    fontSize: 12,
    color: '#666',
    fontFamily: 'monospace',
    marginBottom: 4,
  },
  buttonContainer: {
    flexDirection: 'column',
    gap: 12,
  },
  button: {
    paddingVertical: 12,
    paddingHorizontal: 24,
    borderRadius: 8,
    alignItems: 'center',
  },
  retryButton: {
    backgroundColor: '#007AFF',
  },
  retryButtonText: {
    color: 'white',
    fontSize: 16,
    fontWeight: '600',
  },
  reportButton: {
    backgroundColor: '#FF9500',
  },
  reportButtonText: {
    color: 'white',
    fontSize: 16,
    fontWeight: '600',
  },
  restartButton: {
    backgroundColor: '#FF3B30',
  },
  restartButtonText: {
    color: 'white',
    fontSize: 16,
    fontWeight: '600',
  },
});

export default EnhancedErrorBoundary;
