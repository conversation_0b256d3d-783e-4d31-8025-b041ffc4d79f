import React from 'react';
import Svg, { Path } from 'react-native-svg';

interface HistoryIconProps {
  size?: number;
  color?: string;
}

const HistoryIcon: React.FC<HistoryIconProps> = ({ 
  size = 24, 
  color = '#000000' 
}) => {
  return (
    <Svg width={size} height={size} viewBox="0 0 24 24" fill="none">
      <Path
        d="M13 3C9.23 3 5.95 5.36 4.85 8.92L2.5 6.58L1.08 8L5.08 12L9.08 8L7.66 6.58L5.92 8.31C6.74 5.86 9.61 4 13 4C17.41 4 21 7.59 21 12S17.41 20 13 20C10.07 20 7.64 18.22 6.69 15.66L4.89 16.5C6.25 20.1 9.28 22 13 22C18.52 22 23 17.52 23 12S18.52 2 13 2V3ZM12 7V13L16.28 15.54L17 14.33L13.5 12.25V7H12Z"
        fill={color}
      />
    </Svg>
  );
};

export default HistoryIcon;