import { Platform } from 'react-native';
import DeviceInfo from 'react-native-device-info';
import { logger } from '../monitoring/productionLogger';
import crashReporting from '../monitoring/crashReportingService';

interface SecurityThreat {
  type: 'debugging' | 'rooting' | 'tampering' | 'hooking' | 'emulator' | 'suspicious_behavior';
  severity: 'low' | 'medium' | 'high' | 'critical';
  description: string;
  timestamp: string;
  metadata?: Record<string, any>;
}

interface SecurityStatus {
  isSecure: boolean;
  threats: SecurityThreat[];
  riskLevel: 'low' | 'medium' | 'high' | 'critical';
  recommendations: string[];
}

interface DeviceFingerprint {
  deviceId: string;
  brand: string;
  model: string;
  systemVersion: string;
  buildNumber: string;
  bundleId: string;
  isEmulator: boolean;
  isRooted: boolean;
  hasHooks: boolean;
  installerPackageName?: string;
}

class RuntimeSecurityService {
  private threats: SecurityThreat[] = [];
  private isMonitoring = false;
  private monitoringInterval: NodeJS.Timeout | null = null;
  private deviceFingerprint: DeviceFingerprint | null = null;
  private securityCheckInterval = 30000; // 30 seconds
  private maxThreatHistory = 100;

  /**
   * Initialize runtime security monitoring
   */
  async initialize(): Promise<void> {
    try {
      logger.info('Initializing runtime security monitoring', null, 'security');
      
      // Generate device fingerprint
      await this.generateDeviceFingerprint();
      
      // Perform initial security assessment
      await this.performSecurityAssessment();
      
      // Start continuous monitoring
      this.startMonitoring();
      
      logger.info('Runtime security monitoring initialized', {
        deviceFingerprint: this.deviceFingerprint?.deviceId,
        threatsDetected: this.threats.length
      }, 'security');
    } catch (error) {
      logger.error('Failed to initialize runtime security monitoring', error, 'security');
      throw error;
    }
  }

  /**
   * Generate comprehensive device fingerprint
   */
  private async generateDeviceFingerprint(): Promise<void> {
    try {
      const [
        deviceId,
        brand,
        model,
        systemVersion,
        buildNumber,
        bundleId,
        isEmulator,
        installerPackageName
      ] = await Promise.all([
        DeviceInfo.getUniqueId(),
        DeviceInfo.getBrand(),
        DeviceInfo.getModel(),
        DeviceInfo.getSystemVersion(),
        DeviceInfo.getBuildNumber(),
        DeviceInfo.getBundleId(),
        DeviceInfo.isEmulator(),
        Platform.OS === 'android' ? DeviceInfo.getInstallerPackageName() : Promise.resolve(undefined)
      ]);

      this.deviceFingerprint = {
        deviceId,
        brand,
        model,
        systemVersion,
        buildNumber,
        bundleId,
        isEmulator,
        isRooted: await this.checkRootStatus(),
        hasHooks: this.detectHooks(),
        installerPackageName
      };

      logger.info('Device fingerprint generated', {
        deviceId: deviceId.substring(0, 8) + '...',
        brand,
        model,
        isEmulator,
        isRooted: this.deviceFingerprint.isRooted
      }, 'security');
    } catch (error) {
      logger.error('Failed to generate device fingerprint', error, 'security');
      throw error;
    }
  }

  /**
   * Check if device is rooted/jailbroken
   */
  private async checkRootStatus(): Promise<boolean> {
    try {
      if (Platform.OS === 'android') {
        return await this.checkAndroidRoot();
      } else if (Platform.OS === 'ios') {
        return await this.checkiOSJailbreak();
      }
      return false;
    } catch (error) {
      logger.warn('Failed to check root status', error, 'security');
      return false;
    }
  }

  /**
   * Check for Android root indicators
   */
  private async checkAndroidRoot(): Promise<boolean> {
    try {
      // Check for common root indicators
      const rootIndicators = [
        'su',
        'busybox',
        'superuser.apk',
        'magisk',
        'xposed'
      ];

      // In a real implementation, you would check for:
      // 1. Root management apps
      // 2. Modified system files
      // 3. Writable system directories
      // 4. Root binaries in PATH
      
      // For React Native, we have limited access to system-level checks
      // This is a simplified detection
      const buildTags = await DeviceInfo.getBuildId();
      const isTestKeys = buildTags.includes('test-keys');
      
      if (isTestKeys) {
        this.addThreat({
          type: 'rooting',
          severity: 'high',
          description: 'Device appears to be rooted (test-keys detected)',
          timestamp: new Date().toISOString(),
          metadata: { buildTags }
        });
        return true;
      }

      return false;
    } catch (error) {
      logger.warn('Android root check failed', error, 'security');
      return false;
    }
  }

  /**
   * Check for iOS jailbreak indicators
   */
  private async checkiOSJailbreak(): Promise<boolean> {
    try {
      // In a real implementation, you would check for:
      // 1. Cydia and other jailbreak apps
      // 2. Modified system files
      // 3. Writable system directories
      // 4. SSH access
      
      // For React Native, detection is limited
      // This is a basic check
      const isSimulator = await DeviceInfo.isEmulator();
      
      // Jailbroken devices often have different characteristics
      // This is a simplified detection
      return false;
    } catch (error) {
      logger.warn('iOS jailbreak check failed', error, 'security');
      return false;
    }
  }

  /**
   * Detect runtime hooks and debugging
   */
  private detectHooks(): boolean {
    try {
      // Check for debugging
      if (__DEV__) {
        this.addThreat({
          type: 'debugging',
          severity: 'low',
          description: 'Development mode detected',
          timestamp: new Date().toISOString(),
          metadata: { isDev: true }
        });
        return true;
      }

      // Check for common hooking frameworks
      // In a real implementation, you would check for:
      // 1. Frida
      // 2. Xposed
      // 3. Substrate
      // 4. Other runtime manipulation tools

      // Check for suspicious global objects (simplified)
      const suspiciousGlobals = ['frida', 'xposed', 'substrate'];
      for (const global of suspiciousGlobals) {
        if (typeof (globalThis as any)[global] !== 'undefined') {
          this.addThreat({
            type: 'hooking',
            severity: 'critical',
            description: `Hooking framework detected: ${global}`,
            timestamp: new Date().toISOString(),
            metadata: { framework: global }
          });
          return true;
        }
      }

      return false;
    } catch (error) {
      logger.warn('Hook detection failed', error, 'security');
      return false;
    }
  }

  /**
   * Perform comprehensive security assessment
   */
  private async performSecurityAssessment(): Promise<SecurityStatus> {
    const threats: SecurityThreat[] = [];
    let riskLevel: 'low' | 'medium' | 'high' | 'critical' = 'low';
    const recommendations: string[] = [];

    try {
      // Check device integrity
      if (this.deviceFingerprint?.isEmulator) {
        threats.push({
          type: 'emulator',
          severity: 'medium',
          description: 'Running on emulator/simulator',
          timestamp: new Date().toISOString()
        });
        riskLevel = 'medium';
        recommendations.push('Consider additional verification for emulator usage');
      }

      if (this.deviceFingerprint?.isRooted) {
        threats.push({
          type: 'rooting',
          severity: 'high',
          description: 'Device is rooted/jailbroken',
          timestamp: new Date().toISOString()
        });
        riskLevel = 'high';
        recommendations.push('Implement additional security measures for rooted devices');
      }

      if (this.deviceFingerprint?.hasHooks) {
        threats.push({
          type: 'hooking',
          severity: 'critical',
          description: 'Runtime hooks detected',
          timestamp: new Date().toISOString()
        });
        riskLevel = 'critical';
        recommendations.push('Block access due to runtime manipulation');
      }

      // Check app integrity
      await this.checkAppIntegrity(threats);

      // Check for suspicious behavior
      this.checkSuspiciousBehavior(threats);

      // Update threat list
      this.threats = [...this.threats, ...threats].slice(-this.maxThreatHistory);

      const status: SecurityStatus = {
        isSecure: riskLevel === 'low',
        threats,
        riskLevel,
        recommendations
      };

      // Log security assessment
      logger.security('Security assessment completed', {
        threatsFound: threats.length,
        riskLevel,
        isSecure: status.isSecure
      });

      // Report critical threats
      if (riskLevel === 'critical') {
        crashReporting.recordNonFatalError('Critical security threat detected', {
          threats: threats.map(t => ({ type: t.type, severity: t.severity })),
          deviceFingerprint: this.deviceFingerprint
        });
      }

      return status;
    } catch (error) {
      logger.error('Security assessment failed', error, 'security');
      throw error;
    }
  }

  /**
   * Check application integrity
   */
  private async checkAppIntegrity(threats: SecurityThreat[]): Promise<void> {
    try {
      // Check installer package (Android)
      if (Platform.OS === 'android' && this.deviceFingerprint?.installerPackageName) {
        const validInstallers = ['com.android.vending', 'com.google.android.packageinstaller'];
        if (!validInstallers.includes(this.deviceFingerprint.installerPackageName)) {
          threats.push({
            type: 'tampering',
            severity: 'medium',
            description: 'App installed from unknown source',
            timestamp: new Date().toISOString(),
            metadata: { installer: this.deviceFingerprint.installerPackageName }
          });
        }
      }

      // Check for debugging flags
      if (__DEV__) {
        threats.push({
          type: 'debugging',
          severity: 'low',
          description: 'Debug build detected',
          timestamp: new Date().toISOString()
        });
      }
    } catch (error) {
      logger.warn('App integrity check failed', error, 'security');
    }
  }

  /**
   * Check for suspicious behavior patterns
   */
  private checkSuspiciousBehavior(threats: SecurityThreat[]): void {
    try {
      // Check for rapid API calls (potential automation)
      // Check for unusual navigation patterns
      // Check for excessive error rates
      // This would be implemented based on app-specific behavior patterns
      
      // Placeholder for behavior analysis
      logger.debug('Suspicious behavior check completed', null, 'security');
    } catch (error) {
      logger.warn('Suspicious behavior check failed', error, 'security');
    }
  }

  /**
   * Add a new security threat
   */
  private addThreat(threat: SecurityThreat): void {
    this.threats.push(threat);
    
    // Keep only recent threats
    if (this.threats.length > this.maxThreatHistory) {
      this.threats = this.threats.slice(-this.maxThreatHistory);
    }

    logger.security('Security threat detected', {
      type: threat.type,
      severity: threat.severity,
      description: threat.description
    });
  }

  /**
   * Start continuous security monitoring
   */
  private startMonitoring(): void {
    if (this.isMonitoring) {
      return;
    }

    this.isMonitoring = true;
    this.monitoringInterval = setInterval(async () => {
      try {
        await this.performSecurityAssessment();
      } catch (error) {
        logger.error('Security monitoring check failed', error, 'security');
      }
    }, this.securityCheckInterval);

    logger.info('Security monitoring started', {
      interval: this.securityCheckInterval
    }, 'security');
  }

  /**
   * Stop security monitoring
   */
  stopMonitoring(): void {
    if (this.monitoringInterval) {
      clearInterval(this.monitoringInterval);
      this.monitoringInterval = null;
    }
    this.isMonitoring = false;
    logger.info('Security monitoring stopped', null, 'security');
  }

  /**
   * Get current security status
   */
  async getSecurityStatus(): Promise<SecurityStatus> {
    return this.performSecurityAssessment();
  }

  /**
   * Get device fingerprint
   */
  getDeviceFingerprint(): DeviceFingerprint | null {
    return this.deviceFingerprint;
  }

  /**
   * Get threat history
   */
  getThreatHistory(): SecurityThreat[] {
    return [...this.threats];
  }

  /**
   * Clear threat history
   */
  clearThreatHistory(): void {
    this.threats = [];
    logger.info('Threat history cleared', null, 'security');
  }
}

// Export singleton instance
export const runtimeSecurity = new RuntimeSecurityService();
export default runtimeSecurity;
