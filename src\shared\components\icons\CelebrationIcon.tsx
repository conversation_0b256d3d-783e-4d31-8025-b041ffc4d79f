import React from 'react';
import Svg, { Path, Circle } from 'react-native-svg';

interface CelebrationIconProps {
  size?: number;
  color?: string;
}

const CelebrationIcon: React.FC<CelebrationIconProps> = ({ 
  size = 24, 
  color = '#007AFF' 
}) => {
  return (
    <Svg width={size} height={size} viewBox="0 0 24 24" fill="none">
      {/* Sparkles/Stars */}
      <Path
        d="M12 2L13.09 8.26L19 7L13.09 8.26L12 2Z"
        fill={color}
      />
      <Path
        d="M12 22L13.09 15.74L19 17L13.09 15.74L12 22Z"
        fill={color}
      />
      <Path
        d="M2 12L8.26 10.91L7 5L8.26 10.91L2 12Z"
        fill={color}
      />
      <Path
        d="M22 12L15.74 10.91L17 5L15.74 10.91L22 12Z"
        fill={color}
      />
      
      {/* Center circle */}
      <Circle cx="12" cy="12" r="3" fill={color} />
      
      {/* Additional sparkles */}
      <Path
        d="M6 6L6.5 7.5L8 7L6.5 7.5L6 6Z"
        fill={color}
        opacity="0.7"
      />
      <Path
        d="M18 6L18.5 7.5L20 7L18.5 7.5L18 6Z"
        fill={color}
        opacity="0.7"
      />
      <Path
        d="M6 18L6.5 16.5L8 17L6.5 16.5L6 18Z"
        fill={color}
        opacity="0.7"
      />
      <Path
        d="M18 18L18.5 16.5L20 17L18.5 16.5L18 18Z"
        fill={color}
        opacity="0.7"
      />
    </Svg>
  );
};

export default CelebrationIcon;