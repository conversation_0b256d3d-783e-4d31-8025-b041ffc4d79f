-- Migration to add setup-related fields to users table
-- Run this SQL script on your database

-- Add PIN setup fields
ALTER TABLE users ADD COLUMN IF NOT EXISTS pin_setup_at TIMESTAMP;

-- Add biometric setup fields
ALTER TABLE users ADD COLUMN IF NOT EXISTS biometric_enabled BOOLEAN DEFAULT FALSE;
ALTER TABLE users ADD COLUMN IF NOT EXISTS biometric_type VARCHAR(20);
ALTER TABLE users ADD COLUMN IF NOT EXISTS biometric_device_info JSONB;
ALTER TABLE users ADD COLUMN IF NOT EXISTS biometric_setup_at TIMESTAMP;

-- Add profile setup fields
ALTER TABLE users ADD COLUMN IF NOT EXISTS date_of_birth DATE;
ALTER TABLE users ADD COLUMN IF NOT EXISTS profile_setup_at TIMESTAMP;

-- Add overall setup completion fields
ALTER TABLE users ADD COLUMN IF NOT EXISTS setup_completed BOOLEAN DEFAULT FALSE;
ALTER TABLE users ADD COLUMN IF NOT EXISTS setup_completed_at TIMESTAMP;

-- Create indexes for better performance
CREATE INDEX IF NOT EXISTS idx_users_setup_completed ON users(setup_completed);
CREATE INDEX IF NOT EXISTS idx_users_biometric_enabled ON users(biometric_enabled);
CREATE INDEX IF NOT EXISTS idx_users_pin_setup_at ON users(pin_setup_at);

-- Update existing users to have setup_completed = true if they have basic info
UPDATE users 
SET setup_completed = true, 
    setup_completed_at = CURRENT_TIMESTAMP 
WHERE first_name IS NOT NULL 
  AND pin IS NOT NULL 
  AND pin != '0000'
  AND (is_email_verified = true OR is_phone_verified = true)
  AND setup_completed IS NULL;