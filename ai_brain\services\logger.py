"""
PayVendy AI Brain Logging Service

Advanced logging system for AI Brain operations with structured logging,
performance monitoring, and security audit trails.
"""

import logging
import logging.handlers
import json
import os
import sys
from datetime import datetime
from pathlib import Path
from typing import Dict, Any, Optional
from dataclasses import dataclass, asdict


@dataclass
class LogContext:
    """Context information for structured logging."""
    user_id: Optional[str] = None
    session_id: Optional[str] = None
    operation: Optional[str] = None
    model_version: Optional[str] = None
    processing_time_ms: Optional[int] = None
    confidence_score: Optional[float] = None
    additional_data: Optional[Dict[str, Any]] = None


class AIBrainFormatter(logging.Formatter):
    """Custom formatter for AI Brain logs with structured JSON output."""
    
    def format(self, record):
        # Create base log entry
        log_entry = {
            'timestamp': datetime.utcnow().isoformat() + 'Z',
            'level': record.levelname,
            'logger': record.name,
            'message': record.getMessage(),
            'module': record.module,
            'function': record.funcName,
            'line': record.lineno
        }
        
        # Add context if available
        if hasattr(record, 'context') and record.context:
            context_dict = asdict(record.context) if isinstance(record.context, LogContext) else record.context
            log_entry['context'] = context_dict
        
        # Add exception info if present
        if record.exc_info:
            log_entry['exception'] = self.formatException(record.exc_info)
        
        # Add extra fields
        for key, value in record.__dict__.items():
            if key not in ['name', 'msg', 'args', 'levelname', 'levelno', 'pathname', 
                          'filename', 'module', 'lineno', 'funcName', 'created', 
                          'msecs', 'relativeCreated', 'thread', 'threadName', 
                          'processName', 'process', 'getMessage', 'exc_info', 
                          'exc_text', 'stack_info', 'context']:
                log_entry[key] = value
        
        return json.dumps(log_entry, default=str)


class AILogger:
    """Advanced logging service for AI Brain operations."""
    
    def __init__(self, config=None):
        self.config = config
        self.logger = logging.getLogger('ai_brain')
        self._setup_logging()
    
    def _setup_logging(self):
        """Setup logging configuration."""
        # Clear existing handlers
        self.logger.handlers.clear()
        
        # Set log level
        log_level = getattr(logging, (self.config.log_level if self.config else 'INFO').upper())
        self.logger.setLevel(log_level)
        
        # Console handler with structured format
        console_handler = logging.StreamHandler(sys.stdout)
        console_handler.setLevel(log_level)
        
        if self.config and self.config.is_production:
            # Production: JSON structured logging
            console_handler.setFormatter(AIBrainFormatter())
        else:
            # Development: Human-readable format
            formatter = logging.Formatter(
                '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
            )
            console_handler.setFormatter(formatter)
        
        self.logger.addHandler(console_handler)
        
        # File handler if enabled
        if self.config and self.config.enable_file_logging:
            self._setup_file_logging()
        
        # Prevent propagation to root logger
        self.logger.propagate = False
    
    def _setup_file_logging(self):
        """Setup file logging with rotation."""
        try:
            log_file_path = Path(self.config.log_file_path)
            log_file_path.parent.mkdir(parents=True, exist_ok=True)
            
            # Rotating file handler (10MB max, keep 5 files)
            file_handler = logging.handlers.RotatingFileHandler(
                log_file_path,
                maxBytes=10 * 1024 * 1024,  # 10MB
                backupCount=5
            )
            
            file_handler.setLevel(logging.DEBUG)
            file_handler.setFormatter(AIBrainFormatter())
            
            self.logger.addHandler(file_handler)
            
        except Exception as e:
            self.logger.error(f"Failed to setup file logging: {str(e)}")
    
    def _log_with_context(self, level: int, message: str, context: Optional[LogContext] = None, **kwargs):
        """Log message with context information."""
        extra = {'context': context} if context else {}
        extra.update(kwargs)
        self.logger.log(level, message, extra=extra)
    
    def debug(self, message: str, context: Optional[LogContext] = None, **kwargs):
        """Log debug message."""
        self._log_with_context(logging.DEBUG, message, context, **kwargs)
    
    def info(self, message: str, context: Optional[LogContext] = None, **kwargs):
        """Log info message."""
        self._log_with_context(logging.INFO, message, context, **kwargs)
    
    def warning(self, message: str, context: Optional[LogContext] = None, **kwargs):
        """Log warning message."""
        self._log_with_context(logging.WARNING, message, context, **kwargs)
    
    def error(self, message: str, context: Optional[LogContext] = None, **kwargs):
        """Log error message."""
        self._log_with_context(logging.ERROR, message, context, **kwargs)
    
    def critical(self, message: str, context: Optional[LogContext] = None, **kwargs):
        """Log critical message."""
        self._log_with_context(logging.CRITICAL, message, context, **kwargs)
    
    def log_user_analysis(self, user_id: str, analysis_result: Dict[str, Any], 
                         processing_time_ms: int, confidence_score: float):
        """Log user behavior analysis."""
        context = LogContext(
            user_id=user_id,
            operation='user_analysis',
            processing_time_ms=processing_time_ms,
            confidence_score=confidence_score,
            additional_data={
                'segment': analysis_result.get('segment'),
                'risk_level': analysis_result.get('risk_level'),
                'engagement_score': analysis_result.get('engagement_score')
            }
        )
        
        self.info(
            f"User analysis completed for {user_id}: "
            f"segment={analysis_result.get('segment')}, "
            f"risk={analysis_result.get('risk_level')}, "
            f"engagement={analysis_result.get('engagement_score', 0):.2f}",
            context=context
        )
    
    def log_reward_decision(self, user_id: str, reward_type: str, amount: float, 
                           reason: str, confidence: float):
        """Log reward allocation decision."""
        context = LogContext(
            user_id=user_id,
            operation='reward_decision',
            confidence_score=confidence,
            additional_data={
                'reward_type': reward_type,
                'amount': amount,
                'reason': reason
            }
        )
        
        self.info(
            f"Reward decision for {user_id}: {reward_type} ${amount:.2f} - {reason}",
            context=context
        )
    
    def log_fraud_detection(self, user_id: str, risk_score: float, 
                           patterns_detected: list, requires_investigation: bool):
        """Log fraud detection results."""
        context = LogContext(
            user_id=user_id,
            operation='fraud_detection',
            confidence_score=risk_score,
            additional_data={
                'patterns_detected': patterns_detected,
                'requires_investigation': requires_investigation,
                'pattern_count': len(patterns_detected)
            }
        )
        
        level = logging.WARNING if requires_investigation else logging.INFO
        message = (f"Fraud detection for {user_id}: risk_score={risk_score:.2f}, "
                  f"patterns={len(patterns_detected)}, investigation_required={requires_investigation}")
        
        self._log_with_context(level, message, context)
    
    def log_batch_operation(self, operation: str, batch_size: int, 
                           success_count: int, error_count: int, 
                           processing_time_ms: int):
        """Log batch operation results."""
        context = LogContext(
            operation=f'batch_{operation}',
            processing_time_ms=processing_time_ms,
            additional_data={
                'batch_size': batch_size,
                'success_count': success_count,
                'error_count': error_count,
                'success_rate': success_count / batch_size if batch_size > 0 else 0
            }
        )
        
        self.info(
            f"Batch {operation} completed: {success_count}/{batch_size} successful, "
            f"{error_count} errors, {processing_time_ms}ms",
            context=context
        )
    
    def log_performance_metric(self, metric_name: str, value: float, 
                              unit: str = '', context_data: Optional[Dict] = None):
        """Log performance metrics."""
        context = LogContext(
            operation='performance_metric',
            additional_data={
                'metric_name': metric_name,
                'value': value,
                'unit': unit,
                **(context_data or {})
            }
        )
        
        self.info(f"Performance metric: {metric_name}={value}{unit}", context=context)
    
    def log_system_event(self, event_type: str, description: str, 
                        severity: str = 'info', metadata: Optional[Dict] = None):
        """Log system-level events."""
        context = LogContext(
            operation='system_event',
            additional_data={
                'event_type': event_type,
                'severity': severity,
                **(metadata or {})
            }
        )
        
        level_map = {
            'debug': logging.DEBUG,
            'info': logging.INFO,
            'warning': logging.WARNING,
            'error': logging.ERROR,
            'critical': logging.CRITICAL
        }
        
        level = level_map.get(severity.lower(), logging.INFO)
        self._log_with_context(level, f"System event [{event_type}]: {description}", context)
    
    def log_security_event(self, event_type: str, user_id: Optional[str], 
                          description: str, risk_level: str = 'medium',
                          additional_data: Optional[Dict] = None):
        """Log security-related events."""
        context = LogContext(
            user_id=user_id,
            operation='security_event',
            additional_data={
                'event_type': event_type,
                'risk_level': risk_level,
                **(additional_data or {})
            }
        )
        
        # Security events are always at least WARNING level
        level = logging.CRITICAL if risk_level == 'critical' else logging.WARNING
        
        self._log_with_context(
            level, 
            f"Security event [{event_type}]: {description}" + 
            (f" (User: {user_id})" if user_id else ""),
            context
        )
    
    def create_context(self, user_id: Optional[str] = None, 
                      operation: Optional[str] = None,
                      **kwargs) -> LogContext:
        """Create a log context for structured logging."""
        return LogContext(
            user_id=user_id,
            operation=operation,
            additional_data=kwargs if kwargs else None
        )
    
    def get_logger(self, name: str = None) -> logging.Logger:
        """Get a child logger with the specified name."""
        if name:
            return self.logger.getChild(name)
        return self.logger