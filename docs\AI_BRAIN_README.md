# PayVendy AI Brain 🧠

The PayVendy AI Brain is an autonomous AI system that provides intelligent user behavior analysis, automated reward allocation, and real-time fraud detection for the PayVendy platform.

## Features

### 🔍 User Behavior Analysis
- **Comprehensive User Profiling**: Analyzes transaction patterns, engagement metrics, and activity history
- **Dynamic User Segmentation**: Automatically categorizes users (New, Active, High-Value, VIP, Dormant, At-Risk)
- **Predictive Analytics**: Forecasts user behavior and lifetime value
- **Confidence Scoring**: All analyses include confidence levels for decision making

### 🎁 Intelligent Reward System
- **Automated Reward Allocation**: AI-driven reward decisions based on user behavior
- **Multiple Reward Types**: Welcome bonuses, engagement rewards, loyalty bonuses, recovery incentives
- **Smart Timing**: Optimal reward timing based on user activity patterns
- **Budget Management**: Automatic daily limits and cooldown periods

### 🛡️ Fraud Detection
- **Real-time Monitoring**: Continuous analysis of user activities for suspicious patterns
- **Velocity Checks**: Detection of unusual transaction frequencies and volumes
- **Pattern Recognition**: Identification of fraudulent behavior patterns
- **Risk Scoring**: Comprehensive risk assessment with actionable recommendations

### ⚡ Real-time Processing
- **Event-Driven Architecture**: Processes user events in real-time
- **Scalable Processing**: Handles high-volume user activities efficiently
- **Batch Operations**: Optimized batch processing for comprehensive analysis

## Architecture

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   Backend API   │───▶│   AI Brain      │───▶│   PostgreSQL    │
│                 │    │   Orchestrator  │    │   Database      │
└─────────────────┘    └─────────────────┘    └─────────────────┘
         │                       │                       │
         │              ┌─────────────────┐              │
         └─────────────▶│  Real-time      │◀─────────────┘
                        │  Events Queue   │
                        └─────────────────┘
```

## Installation

### Prerequisites
- Python 3.11+
- PostgreSQL 13+
- Node.js 18+ (for backend integration)

### 1. Install Dependencies
```bash
cd ai_brain
pip install -r requirements.txt
```

### 2. Database Setup
```bash
# Run the AI Brain database migration
cd ../backend
node scripts/run-ai-migration.js
```

### 3. Environment Configuration
Create a `.env` file in the `ai_brain` directory:

```env
# Database Configuration
DATABASE_URL=postgresql://username:password@localhost:5432/payvendy

# AI Model Parameters
AI_BASE_REWARD_AMOUNT=10.0
AI_MAX_DAILY_REWARDS=5
AI_ENGAGEMENT_THRESHOLD=0.6
AI_SUSPICIOUS_THRESHOLD=0.8
AI_HIGH_VALUE_THRESHOLD=1000.0
AI_RISK_VELOCITY_THRESHOLD=500.0

# Operational Settings
AI_ANALYSIS_INTERVAL_SECONDS=300
AI_REWARD_INTERVAL_SECONDS=600
AI_BATCH_SIZE=100
AI_MAX_CONCURRENT_OPS=10

# Logging
AI_LOG_LEVEL=INFO
AI_ENABLE_FILE_LOGGING=true
AI_LOG_FILE_PATH=logs/ai_brain.log

# Environment
NODE_ENV=development
```

### 4. Start the AI Brain
```bash
# Development
python start.py

# Production (with Docker)
docker-compose -f ../docker-compose.ai.yml up -d
```

## Usage

### Backend Integration

The AI Brain automatically integrates with the backend through event triggers:

```javascript
const aiService = require('./services/aiService');

// Trigger user analysis on login
await aiService.triggerLoginAnalysis(userId, {
  method: 'otp_verification',
  ip: clientIP,
  userAgent: req.headers['user-agent']
});

// Trigger analysis on profile update
await aiService.triggerProfileUpdate(userId, {
  updatedFields: ['firstName', 'lastName'],
  sensitiveFieldsChanged: false
});

// Trigger transaction analysis
await aiService.triggerTransactionAnalysis(userId, {
  id: transactionId,
  amount: 500.00,
  type: 'transfer'
});
```

### API Endpoints

#### Trigger Manual Analysis
```http
POST /api/v1/ai/analyze/:userId
Authorization: Bearer <token>
Content-Type: application/json

{
  "priority": "high"
}
```

#### Get User Analysis
```http
GET /api/v1/ai/analysis/:userId?limit=10
Authorization: Bearer <token>
```

#### Get User Rewards
```http
GET /api/v1/ai/rewards/:userId
Authorization: Bearer <token>
```

#### Apply Reward
```http
POST /api/v1/ai/rewards/:rewardId/apply
Authorization: Bearer <token>
```

#### System Statistics
```http
GET /api/v1/ai/stats
Authorization: Bearer <token>
```

## Configuration

### AI Model Parameters

| Parameter | Default | Description |
|-----------|---------|-------------|
| `AI_BASE_REWARD_AMOUNT` | 10.0 | Base reward amount in currency |
| `AI_MAX_DAILY_REWARDS` | 5 | Maximum rewards per user per day |
| `AI_ENGAGEMENT_THRESHOLD` | 0.6 | Minimum engagement score for rewards |
| `AI_SUSPICIOUS_THRESHOLD` | 0.8 | Risk score threshold for fraud alerts |
| `AI_HIGH_VALUE_THRESHOLD` | 1000.0 | Threshold for high-value user classification |

### Operational Settings

| Parameter | Default | Description |
|-----------|---------|-------------|
| `AI_ANALYSIS_INTERVAL_SECONDS` | 300 | User analysis interval (5 minutes) |
| `AI_REWARD_INTERVAL_SECONDS` | 600 | Reward processing interval (10 minutes) |
| `AI_BATCH_SIZE` | 100 | Batch size for user processing |
| `AI_MAX_CONCURRENT_OPS` | 10 | Maximum concurrent operations |

## Monitoring

### Health Check
```bash
# Check AI Brain health
curl http://localhost:8080/health

# Check database connectivity
python -c "
import asyncio
from services.database import DatabaseService
from config.settings import AIBrainConfig
from services.logger import AILogger

async def test():
    db = DatabaseService(AIBrainConfig(), AILogger())
    await db.initialize()
    print('✅ Database connection successful')

asyncio.run(test())
"
```

### Logs
- **File Logging**: `logs/ai_brain.log`
- **Console Logging**: Real-time output
- **Database Logging**: All actions logged to `ai_logs` table

### Metrics
- Analysis completion rate
- Reward processing rate
- Fraud detection accuracy
- System performance metrics

## Database Schema

### Core Tables
- `ai_logs`: All AI actions and decisions
- `behavior_analytics`: User behavior analysis results
- `user_segments`: AI-driven user categorization
- `reward_queue`: Pending and applied rewards
- `realtime_events`: Event processing queue
- `fraud_alerts`: Fraud detection results
- `ai_configuration`: Dynamic AI parameters

### Key Functions
- `apply_reward()`: Safely applies rewards to user accounts
- `cleanup_old_events()`: Maintains database performance

## Development

### Running Tests
```bash
# Install test dependencies
pip install pytest pytest-asyncio

# Run tests
pytest tests/
```

### Code Quality
```bash
# Format code
black ai_brain/

# Lint code
flake8 ai_brain/

# Type checking
mypy ai_brain/
```

## Production Deployment

### Docker Deployment
```bash
# Build and deploy with Docker Compose
docker-compose -f docker-compose.ai.yml up -d

# View logs
docker-compose -f docker-compose.ai.yml logs -f ai-brain

# Scale AI Brain instances
docker-compose -f docker-compose.ai.yml up -d --scale ai-brain=3
```

### Environment Variables for Production
```env
NODE_ENV=production
AI_LOG_LEVEL=WARNING
AI_ENABLE_PERFORMANCE_MONITORING=true
AI_ENABLE_DETAILED_LOGGING=false
DATABASE_URL=********************************************/payvendy_prod
```

## Security Considerations

1. **Database Access**: Use dedicated database user with minimal permissions
2. **API Authentication**: All API endpoints require proper authentication
3. **Data Encryption**: Sensitive data encrypted at rest and in transit
4. **Audit Logging**: All AI decisions logged for compliance
5. **Rate Limiting**: API endpoints protected against abuse

## Troubleshooting

### Common Issues

#### AI Brain Won't Start
```bash
# Check database connectivity
python -c "import asyncpg; print('asyncpg available')"

# Verify environment variables
python -c "
from config.settings import AIBrainConfig
config = AIBrainConfig()
config.validate_config()
print('✅ Configuration valid')
"
```

#### No Events Being Processed
```sql
-- Check event queue
SELECT COUNT(*) as pending_events 
FROM realtime_events 
WHERE processed = false;

-- Check for errors
SELECT error_message, COUNT(*) 
FROM realtime_events 
WHERE error_message IS NOT NULL 
GROUP BY error_message;
```

#### Rewards Not Being Applied
```sql
-- Check reward queue
SELECT status, COUNT(*) 
FROM reward_queue 
GROUP BY status;

-- Check recent AI logs
SELECT action_type, description, created_at 
FROM ai_logs 
WHERE action_type LIKE '%reward%' 
ORDER BY created_at DESC 
LIMIT 10;
```

## Support

For issues and questions:
1. Check the logs: `logs/ai_brain.log`
2. Review database logs: `SELECT * FROM ai_logs ORDER BY created_at DESC LIMIT 50`
3. Monitor system health: `GET /api/v1/ai/stats`

## License

This AI Brain system is part of the PayVendy platform and is proprietary software.