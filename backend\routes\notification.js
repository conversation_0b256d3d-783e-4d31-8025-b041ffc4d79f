const express = require('express');
const router = express.Router();
const { sendPushNotification, saveFcmToken, getUserFcmTokens } = require('../services/notificationService');
const { body, validationResult } = require('express-validator');

// Store FCM token for a user (should be authenticated in production)
router.post('/register-fcm-token', [
  body('userId').isUUID(),
  body('fcmToken').isString().notEmpty(),
], async (req, res) => {
  const errors = validationResult(req);
  if (!errors.isEmpty()) {
    return res.status(400).json({ errors: errors.array() });
  }
  const { userId, fcmToken } = req.body;
  try {
    await saveFcmToken(userId, fcmToken);
    res.status(200).json({ message: 'FCM token registered' });
  } catch (err) {
    res.status(500).json({ error: 'Failed to register FCM token' });
  }
});

// Send push notification to a user
router.post('/send-push', [
  body('userId').isUUID(),
  body('title').isString().notEmpty(),
  body('body').isString().notEmpty(),
], async (req, res) => {
  const errors = validationResult(req);
  if (!errors.isEmpty()) {
    return res.status(400).json({ errors: errors.array() });
  }
  const { userId, title, body: messageBody } = req.body;
  try {
    await sendPushNotification(userId, title, messageBody);
    res.status(200).json({ message: 'Push notification sent' });
  } catch (err) {
    res.status(500).json({ error: 'Failed to send push notification' });
  }
});

// Admin: Get all FCM tokens for a user
router.get('/user-tokens/:userId', async (req, res) => {
  // TODO: Add admin authentication/authorization here
  const { userId } = req.params;
  try {
    const tokens = await getUserFcmTokens(userId);
    res.status(200).json({ tokens });
  } catch (err) {
    res.status(500).json({ error: 'Failed to fetch FCM tokens' });
  }
});

module.exports = router;
