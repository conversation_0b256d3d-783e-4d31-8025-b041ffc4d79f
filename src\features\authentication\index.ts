/**
 * Authentication Feature Module
 * 
 * This module encapsulates all authentication-related functionality including
 * screens, services, components, and state management.
 */

import { FeatureModule } from '../../core/types';
import { container } from '../../core/di/container';
import { logger } from '../../services/productionLogger';

// Import authentication services
import { AuthenticationService } from './services/authenticationService';
import { BiometricService } from './services/biometricService';
import { PinService } from './services/pinService';

// Import authentication store
import { createAuthStore } from './store/authStore';

// Import authentication screens (lazy loaded)
const getAuthScreens = () => ({
  SplashScreen: () => import('./screens/SplashScreen'),
  StartupScreen: () => import('./screens/StartupScreen'),
  PhoneInputScreen: () => import('./screens/PhoneInputScreen'),
  EmailInputScreen: () => import('./screens/EmailInputScreen'),
  EmailVerificationScreen: () => import('./screens/EmailVerificationScreen'),
  VerificationScreen: () => import('./screens/VerificationScreen'),
  PinSetupScreen: () => import('./screens/PinSetupScreen'),
  PinVerificationScreen: () => import('./screens/PinVerificationScreen'),

});

// Import authentication components
const getAuthComponents = () => ({
  PinInputModal: () => import('./components/PinInputModal'),
  BiometricSetupModal: () => import('./components/BiometricSetupModal'),
  AuthErrorBoundary: () => import('./components/AuthErrorBoundary'),
});

class AuthenticationModule implements FeatureModule {
  name = 'authentication';
  version = '1.0.0';
  dependencies = ['core', 'navigation', 'security'];
  
  private authStore: any = null;
  private isInitialized = false;

  async initialize(): Promise<void> {
    if (this.isInitialized) {
      logger.warn('Authentication module already initialized', null, 'auth-module');
      return;
    }

    try {
      logger.info('Initializing authentication module', null, 'auth-module');

      // Register authentication services
      await this.registerServices();

      // Initialize authentication store
      this.authStore = createAuthStore();

      // Register authentication screens and components
      this.registerScreens();
      this.registerComponents();

      this.isInitialized = true;
      logger.info('Authentication module initialized successfully', null, 'auth-module');
    } catch (error) {
      logger.error('Failed to initialize authentication module', error, 'auth-module');
      throw error;
    }
  }

  async cleanup(): Promise<void> {
    if (!this.isInitialized) {
      return;
    }

    try {
      logger.info('Cleaning up authentication module', null, 'auth-module');

      // Cleanup services
      const authService = await container.resolve<AuthenticationService>('authenticationService');
      await authService.cleanup();

      // Cleanup store
      if (this.authStore && typeof this.authStore.cleanup === 'function') {
        this.authStore.cleanup();
      }

      this.isInitialized = false;
      logger.info('Authentication module cleaned up successfully', null, 'auth-module');
    } catch (error) {
      logger.error('Failed to cleanup authentication module', error, 'auth-module');
    }
  }

  getRoutes(): any[] {
    const screens = getAuthScreens();
    
    return [
      {
        name: 'Splash',
        component: screens.SplashScreen,
        options: { headerShown: false }
      },
      {
        name: 'Startup',
        component: screens.StartupScreen,
        options: { headerShown: false }
      },
      {
        name: 'PhoneInput',
        component: screens.PhoneInputScreen,
        options: { headerShown: false }
      },
      {
        name: 'EmailInput',
        component: screens.EmailInputScreen,
        options: { headerShown: false }
      },
      {
        name: 'EmailVerification',
        component: screens.EmailVerificationScreen,
        options: { headerShown: false }
      },
      {
        name: 'Verification',
        component: screens.VerificationScreen,
        options: { headerShown: false }
      },
      {
        name: 'PinSetup',
        component: screens.PinSetupScreen,
        options: { headerShown: false }
      },
      {
        name: 'PinVerification',
        component: screens.PinVerificationScreen,
        options: { headerShown: false }
      },
      {
        name: 'BiometricSetup',
        component: screens.BiometricSetupScreen,
        options: { headerShown: false }
      },
    ];
  }

  getServices(): any[] {
    return [
      'authenticationService',
      'biometricService',
      'pinService'
    ];
  }

  private async registerServices(): Promise<void> {
    // Register authentication service
    container.registerSingleton(
      'authenticationService',
      () => new AuthenticationService(),
      ['apiService', 'secureStorage', 'inputValidation', 'runtimeSecurity']
    );

    // Register biometric service
    container.registerSingleton(
      'biometricService',
      () => new BiometricService(),
      ['secureStorage', 'logger']
    );

    // Register PIN service
    container.registerSingleton(
      'pinService',
      () => new PinService(),
      ['secureStorage', 'keyManagement', 'logger']
    );

    logger.debug('Authentication services registered', null, 'auth-module');
  }

  private registerScreens(): void {
    // Screen registration would be handled by the navigation system
    // This is a placeholder for screen registration logic
    logger.debug('Authentication screens registered', null, 'auth-module');
  }

  private registerComponents(): void {
    // Component registration for shared authentication components
    // This is a placeholder for component registration logic
    logger.debug('Authentication components registered', null, 'auth-module');
  }

  // Public API methods for other modules
  async isAuthenticated(): Promise<boolean> {
    const authService = await container.resolve<AuthenticationService>('authenticationService');
    return authService.isAuthenticated();
  }

  async getCurrentUser(): Promise<any> {
    const authService = await container.resolve<AuthenticationService>('authenticationService');
    return authService.getCurrentUser();
  }

  async logout(): Promise<void> {
    const authService = await container.resolve<AuthenticationService>('authenticationService');
    await authService.logout();
  }

  async refreshToken(): Promise<boolean> {
    const authService = await container.resolve<AuthenticationService>('authenticationService');
    return authService.refreshToken();
  }

  // Biometric methods
  async isBiometricAvailable(): Promise<boolean> {
    const biometricService = await container.resolve<BiometricService>('biometricService');
    return biometricService.isAvailable();
  }

  async authenticateWithBiometric(): Promise<boolean> {
    const biometricService = await container.resolve<BiometricService>('biometricService');
    return biometricService.authenticate();
  }

  // PIN methods
  async validatePin(pin: string): Promise<boolean> {
    const pinService = await container.resolve<PinService>('pinService');
    return pinService.validatePin(pin);
  }

  async setupPin(pin: string): Promise<boolean> {
    const pinService = await container.resolve<PinService>('pinService');
    return pinService.setupPin(pin);
  }

  // Event handlers
  onAuthStateChanged(callback: (isAuthenticated: boolean) => void): () => void {
    // Implementation would depend on the store/event system
    return () => {}; // Unsubscribe function
  }

  onUserChanged(callback: (user: any) => void): () => void {
    // Implementation would depend on the store/event system
    return () => {}; // Unsubscribe function
  }
}

// Create and export the authentication module instance
export const authenticationModule = new AuthenticationModule();

// Export types and interfaces
export * from './types';
export * from './services/authenticationService';
export * from './services/biometricService';
export * from './services/pinService';

// Export store
export { createAuthStore } from './store/authStore';

// Export components for external use
export const AuthComponents = {
  PinInputModal: () => import('./components/PinInputModal'),
  BiometricSetupModal: () => import('./components/BiometricSetupModal'),
  AuthErrorBoundary: () => import('./components/AuthErrorBoundary'),
};

// Export screens for external use
export const AuthScreens = getAuthScreens();

export default authenticationModule;
