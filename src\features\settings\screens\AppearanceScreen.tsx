import React from 'react';
import { View, Text, StyleSheet, TouchableOpacity, ScrollView } from 'react-native';
import { useTheme } from '../../../shared/components/layout/ThemeContext';
import ThemeIcon from '../../../shared/components/icons/ThemeIcon';
import DefaultIcon from '../../../shared/components/icons/DefaultIcon';

const AppearanceScreen = () => {
  const { theme } = useTheme();
  return (
    <ScrollView style={{ flex: 1, backgroundColor: theme.colors.background }} contentContainerStyle={{ alignItems: 'center', padding: 0 }}>
      {/* Header */}
      <View style={styles.topRow}>
        <View style={[styles.iconCircle, { backgroundColor: theme.colors.card }]}> 
          <ThemeIcon size={24} color={theme.colors.primary} />
        </View>
        <Text style={[styles.title, { color: theme.colors.text }]}>Appearance</Text>
      </View>
      {/* Card List */}
      <View style={[styles.card, { backgroundColor: theme.colors.card }]}> 
        <TouchableOpacity style={styles.optionRow} activeOpacity={0.7}>
          <View style={[styles.optionIcon, { backgroundColor: theme.colors.primary + '22' }]}> 
            <ThemeIcon size={20} color={theme.colors.primary} />
          </View>
          <View style={styles.optionTextWrap}>
            <Text style={[styles.optionTitle, { color: theme.colors.text }]}>Theme</Text>
            <Text style={[styles.optionSubtitle, { color: theme.colors.muted }]}>Light or dark mode</Text>
          </View>
          <Text style={{ color: theme.colors.primary, fontWeight: '600' }}>Dark</Text>
        </TouchableOpacity>
        <TouchableOpacity style={styles.optionRow} activeOpacity={0.7}>
          <View style={[styles.optionIcon, { backgroundColor: theme.colors.primary + '22' }]}> 
            <DefaultIcon size={20} color={theme.colors.primary} />
          </View>
          <View style={styles.optionTextWrap}>
            <Text style={[styles.optionTitle, { color: theme.colors.text }]}>Accent Color</Text>
            <Text style={[styles.optionSubtitle, { color: theme.colors.muted }]}>Highlight color</Text>
          </View>
          <Text style={{ color: theme.colors.primary, fontWeight: '600' }}>Blue</Text>
        </TouchableOpacity>
        <TouchableOpacity style={styles.optionRow} activeOpacity={0.7}>
          <View style={[styles.optionIcon, { backgroundColor: theme.colors.primary + '22' }]}> 
            <DefaultIcon size={20} color={theme.colors.primary} />
          </View>
          <View style={styles.optionTextWrap}>
            <Text style={[styles.optionTitle, { color: theme.colors.text }]}>Font Size</Text>
            <Text style={[styles.optionSubtitle, { color: theme.colors.muted }]}>Text size</Text>
          </View>
          <Text style={{ color: theme.colors.primary, fontWeight: '600' }}>Medium</Text>
        </TouchableOpacity>
        <TouchableOpacity style={styles.optionRow} activeOpacity={0.7}>
          <View style={[styles.optionIcon, { backgroundColor: theme.colors.primary + '22' }]}> 
            <DefaultIcon size={20} color={theme.colors.primary} />
          </View>
          <View style={styles.optionTextWrap}>
            <Text style={[styles.optionTitle, { color: theme.colors.text }]}>App Icon</Text>
            <Text style={[styles.optionSubtitle, { color: theme.colors.muted }]}>Change app icon</Text>
          </View>
          <Text style={{ color: theme.colors.primary, fontWeight: '600' }}>Default</Text>
        </TouchableOpacity>
        <TouchableOpacity style={styles.optionRow} activeOpacity={0.7}>
          <View style={[styles.optionIcon, { backgroundColor: theme.colors.primary + '22' }]}> 
            <DefaultIcon size={20} color={theme.colors.primary} />
          </View>
          <View style={styles.optionTextWrap}>
            <Text style={[styles.optionTitle, { color: theme.colors.text }]}>Animations</Text>
            <Text style={[styles.optionSubtitle, { color: theme.colors.muted }]}>Enable/disable UI animations</Text>
          </View>
          <Text style={{ color: theme.colors.primary, fontWeight: '600' }}>On</Text>
        </TouchableOpacity>
      </View>
    </ScrollView>
  );
};

const styles = StyleSheet.create({
  topRow: {
    flexDirection: 'row',
    alignItems: 'center',
    width: '100%',
    paddingHorizontal: 20,
    marginTop: 32,
    marginBottom: 16,
    gap: 12,
  },
  iconCircle: {
    width: 44,
    height: 44,
    borderRadius: 22,
    alignItems: 'center',
    justifyContent: 'center',
    marginRight: 8,
  },
  title: {
    fontSize: 22,
    fontWeight: '700',
    flex: 1,
  },
  card: {
    width: '92%',
    borderRadius: 18,
    paddingVertical: 8,
    marginBottom: 24,
    shadowColor: '#000',
    shadowOpacity: 0.06,
    shadowRadius: 8,
    shadowOffset: { width: 0, height: 2 },
    elevation: 2,
  },
  optionRow: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 18,
    paddingVertical: 18,
    borderBottomWidth: 1,
    borderBottomColor: '#eee',
    gap: 16,
  },
  optionIcon: {
    width: 36,
    height: 36,
    borderRadius: 18,
    alignItems: 'center',
    justifyContent: 'center',
    marginRight: 8,
  },
  optionTextWrap: {
    flex: 1,
  },
  optionTitle: {
    fontSize: 16,
    fontWeight: '600',
  },
  optionSubtitle: {
    fontSize: 13,
    marginTop: 2,
    color: '#888',
  },
});

export default AppearanceScreen;
