import React from 'react';
import { View, Text, StyleSheet, TouchableOpacity, Switch, ScrollView } from 'react-native';
import { useTheme } from '../../../shared/components/layout/ThemeContext';
import SecurityIcon from '../../../shared/components/icons/SecurityIcon';
import LockIcon from '../../../shared/components/icons/LockIcon';
import FingerprintIcon from '../../../shared/components/icons/FingerprintIcon';
import DefaultIcon from '../../../shared/components/icons/DefaultIcon';

const SecurityScreen = () => {
  const { theme } = useTheme();
  return (
    <ScrollView style={{ flex: 1, backgroundColor: theme.colors.background }} contentContainerStyle={{ alignItems: 'center', padding: 0 }}>
      {/* Header */}
      <View style={styles.topRow}>
        <View style={[styles.iconCircle, { backgroundColor: theme.colors.card }]}> 
          <SecurityIcon size={24} color={theme.colors.primary} />
        </View>
        <Text style={[styles.title, { color: theme.colors.text }]}>Security & Privacy</Text>
      </View>
      {/* Card List */}
      <View style={[styles.card, { backgroundColor: theme.colors.card }]}> 
        <TouchableOpacity style={styles.optionRow} activeOpacity={0.7}>
          <View style={[styles.optionIcon, { backgroundColor: theme.colors.primary + '22' }]}> 
            <LockIcon size={20} color={theme.colors.primary} />
          </View>
          <View style={styles.optionTextWrap}>
            <Text style={[styles.optionTitle, { color: theme.colors.text }]}>Change PIN</Text>
            <Text style={[styles.optionSubtitle, { color: theme.colors.muted }]}>Edit your passcode</Text>
          </View>
          <Text style={{ color: theme.colors.primary, fontWeight: '600' }}>Edit</Text>
        </TouchableOpacity>
        <View style={styles.optionRow}>
          <View style={[styles.optionIcon, { backgroundColor: theme.colors.primary + '22' }]}> 
            <FingerprintIcon size={20} color={theme.colors.primary} />
          </View>
          <View style={styles.optionTextWrap}>
            <Text style={[styles.optionTitle, { color: theme.colors.text }]}>Biometric Login</Text>
            <Text style={[styles.optionSubtitle, { color: theme.colors.muted }]}>Use Face/Touch ID</Text>
          </View>
          <Switch value={true} />
        </View>
        <TouchableOpacity style={styles.optionRow} activeOpacity={0.7}>
          <View style={[styles.optionIcon, { backgroundColor: theme.colors.primary + '22' }]}> 
            <DefaultIcon size={20} color={theme.colors.primary} />
          </View>
          <View style={styles.optionTextWrap}>
            <Text style={[styles.optionTitle, { color: theme.colors.text }]}>Active Sessions</Text>
            <Text style={[styles.optionSubtitle, { color: theme.colors.muted }]}>Devices logged in</Text>
          </View>
          <Text style={{ color: theme.colors.primary, fontWeight: '600' }}>View</Text>
        </TouchableOpacity>
        <View style={styles.optionRow}>
          <View style={[styles.optionIcon, { backgroundColor: theme.colors.primary + '22' }]}> 
            <DefaultIcon size={20} color={theme.colors.primary} />
          </View>
          <View style={styles.optionTextWrap}>
            <Text style={[styles.optionTitle, { color: theme.colors.text }]}>Incognito Mode</Text>
            <Text style={[styles.optionSubtitle, { color: theme.colors.muted }]}>Hide activity</Text>
          </View>
          <Switch value={false} />
        </View>
        <View style={styles.optionRow}>
          <View style={[styles.optionIcon, { backgroundColor: theme.colors.primary + '22' }]}> 
            <DefaultIcon size={20} color={theme.colors.primary} />
          </View>
          <View style={styles.optionTextWrap}>
            <Text style={[styles.optionTitle, { color: theme.colors.text }]}>Two-Factor Auth</Text>
            <Text style={[styles.optionSubtitle, { color: theme.colors.muted }]}>Extra account security</Text>
          </View>
          <Switch value={false} />
        </View>
      </View>
    </ScrollView>
  );
};

const styles = StyleSheet.create({
  topRow: {
    flexDirection: 'row',
    alignItems: 'center',
    width: '100%',
    paddingHorizontal: 20,
    marginTop: 32,
    marginBottom: 16,
    gap: 12,
  },
  iconCircle: {
    width: 44,
    height: 44,
    borderRadius: 22,
    alignItems: 'center',
    justifyContent: 'center',
    marginRight: 8,
  },
  title: {
    fontSize: 22,
    fontWeight: '700',
    flex: 1,
  },
  card: {
    width: '92%',
    borderRadius: 18,
    paddingVertical: 8,
    marginBottom: 24,
    shadowColor: '#000',
    shadowOpacity: 0.06,
    shadowRadius: 8,
    shadowOffset: { width: 0, height: 2 },
    elevation: 2,
  },
  optionRow: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 18,
    paddingVertical: 18,
    borderBottomWidth: 1,
    borderBottomColor: '#eee',
    gap: 16,
  },
  optionIcon: {
    width: 36,
    height: 36,
    borderRadius: 18,
    alignItems: 'center',
    justifyContent: 'center',
    marginRight: 8,
  },
  optionTextWrap: {
    flex: 1,
  },
  optionTitle: {
    fontSize: 16,
    fontWeight: '600',
  },
  optionSubtitle: {
    fontSize: 13,
    marginTop: 2,
    color: '#888',
  },
});

export default SecurityScreen;
