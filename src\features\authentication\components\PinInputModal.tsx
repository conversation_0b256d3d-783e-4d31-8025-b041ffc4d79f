import React, { useState } from 'react';
import { View, Text, TouchableOpacity, StyleSheet, Alert } from 'react-native';
import { useTheme } from '../../../shared/components/layout/ThemeContext';
import ModalBase from '../../../shared/components/ui/ModalBase';

interface PinInputModalProps {
  visible: boolean;
  onClose: () => void;
  onConfirm: (pin: string) => void;
  amount?: string; // Optional amount to display
}

const PinInputModal: React.FC<PinInputModalProps> = ({ visible, onClose, onConfirm, amount = "0.00" }) => {
  const { theme } = useTheme();
  const [pin, setPin] = useState('');

  const handleKeyPress = (value: string) => {
    if (value === 'backspace') {
      setPin(pin.slice(0, -1));
    } else if (pin.length < 4) {
      setPin(pin + value);
    }
  };

  const handleConfirm = () => {
    if (pin.length === 4) {
      onConfirm(pin);
      setPin('');
    } else {
      Alert.alert('Invalid PIN', 'Please enter a 4-digit PIN.');
    }
  };

  const renderPinBoxes = () => {
    return [0, 1, 2, 3].map((_, index) => (
      <View
        key={index}
        style={[
          styles.pinBox,
          { borderColor: pin.length === index ? '#00cc66' : theme.colors.border },
          { backgroundColor: theme.dark ? '#1f1f1f' : '#f0f0f0' }
        ]}
      >
        <Text style={[styles.pinText, { color: theme.colors.text }]}>
          {pin.length > index ? '●' : ''}
        </Text>
      </View>
    ));
  };

  const keypadButtons = ['1', '2', '3', '4', '5', '6', '7', '8', '9', '0'];
  const lastRowButtons = ['0', '', 'backspace'];

  return (
    <ModalBase visible={visible} onClose={onClose} variant="center">
      <View style={[
        styles.modalContent,
        {
          backgroundColor: theme.dark ? '#121212' : theme.colors.card,
        }
      ]}>
          <Text style={{ color: theme.colors.muted, fontSize: 22, marginBottom: 10, textAlign: 'center' }}>₦{amount}</Text>
          <Text style={{ color: theme.colors.text, fontSize: 20, fontWeight: 'bold', marginBottom: 20, textAlign: 'center' }}>Enter Payment PIN</Text>

          <View style={styles.pinRow}>
            {renderPinBoxes()}
          </View>

          <Text style={[styles.forgotText, { color: theme.colors.primary }]}>Forgot Payment PIN?</Text>

          <View style={styles.secureInfo}>
            <View style={styles.shieldIcon}>
              <Text style={{ color: 'green' }}>✔</Text>
            </View>
            <Text style={[styles.secureText, { color: theme.colors.muted }]}>OPay Secure Numeric Keypad</Text>
          </View>

          <View style={styles.keypad}>
            {keypadButtons.map((number, index) => (
              <TouchableOpacity
                key={index}
                style={[styles.key, { backgroundColor: theme.dark ? '#333' : '#e0e0e0' }]}
                onPress={() => handleKeyPress(number)}
              >
                <Text style={[styles.keyText, { color: theme.colors.text }]}>{number}</Text>
              </TouchableOpacity>
            ))}
            {lastRowButtons.map((btn, index) => (
              <TouchableOpacity
                key={index}
                style={[styles.key, { backgroundColor: btn ? (theme.dark ? '#333' : '#e0e0e0') : 'transparent' }]}
                onPress={() => btn === 'backspace' && handleKeyPress('backspace')}
                disabled={!btn}
              >
                {btn === 'backspace' && <Text style={[styles.keyText, { color: theme.colors.text }]}>⌫</Text>}
                {btn === '0' && <Text style={[styles.keyText, { color: theme.colors.text }]}>0</Text>}
              </TouchableOpacity>
            ))}
          </View>
        </View>
    </ModalBase>
  );
};

export default PinInputModal;

const styles = StyleSheet.create({
  modalContent: {
    borderRadius: 16,
    padding: 20,
    width: '100%',
    alignItems: 'center',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.25,
    shadowRadius: 4,
    elevation: 5,
    maxHeight: '60%',
  },
  pinRow: {
    flexDirection: 'row',
    justifyContent: 'center',
    marginBottom: 15,
  },
  pinBox: {
    width: 50,
    height: 50,
    borderRadius: 10,
    borderWidth: 1,
    marginHorizontal: 5,
    alignItems: 'center',
    justifyContent: 'center',
  },
  activePinBox: {
    borderColor: '#00cc66',
  },
  pinText: {
    fontSize: 24,
  },
  forgotText: {
    marginBottom: 20,
    fontSize: 14,
    textAlign: 'center',
  },
  secureInfo: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 10,
  },
  shieldIcon: {
    width: 16,
    height: 16,
    backgroundColor: '#d0ffd0',
    borderRadius: 8,
    alignItems: 'center',
    justifyContent: 'center',
  },
  secureText: {
    marginLeft: 6,
    fontSize: 12,
  },
  keypad: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    width: '80%',
    justifyContent: 'center',
    marginTop: 20,
  },
  key: {
    width: '30%',
    aspectRatio: 1,
    margin: '1.5%',
    borderRadius: 12,
    justifyContent: 'center',
    alignItems: 'center',
  },
  keyText: {
    fontSize: 24,
  },
});
