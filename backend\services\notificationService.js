const admin = require('firebase-admin');
const db = require('../config/database');
const logger = require('../utils/logger');

/**
 * Save or update FCM token for a user (production scale: upsert, indexed)
 */
async function saveFcmToken(userId, fcmToken) {
  // Use a dedicated table for device tokens (user_id, fcm_token, device_info, last_seen, etc.)
  const query = `
    INSERT INTO user_fcm_tokens (user_id, fcm_token, last_seen)
    VALUES ($1, $2, NOW())
    ON CONFLICT (user_id, fcm_token)
    DO UPDATE SET last_seen = NOW()
  `;
  await db.query(query, [userId, fcmToken]);
}

/**
 * Send push notification to all tokens for a user (production: handle errors, clean up invalid tokens)
 */
async function sendPushNotification(userId, title, body) {
  // Get all tokens for the user
  const { rows } = await db.query('SELECT fcm_token FROM user_fcm_tokens WHERE user_id = $1', [userId]);
  if (!rows.length) throw new Error('No FCM tokens for user');
  const tokens = rows.map(r => r.fcm_token);

  const message = {
    notification: { title, body },
    tokens,
  };
  const response = await admin.messaging().sendMulticast(message);
  logger.info(`Push sent to user ${userId}: ${response.successCount} success, ${response.failureCount} failed`);
  // Clean up invalid tokens
  if (response.failureCount > 0) {
    const invalidTokens = response.responses
      .map((r, i) => (!r.success ? tokens[i] : null))
      .filter(Boolean);
    if (invalidTokens.length) {
      await db.query('DELETE FROM user_fcm_tokens WHERE user_id = $1 AND fcm_token = ANY($2)', [userId, invalidTokens]);
    }
  }
}

/**
 * Get all FCM tokens for a user (for admin use)
 */
async function getUserFcmTokens(userId) {
  const { rows } = await db.query('SELECT fcm_token FROM user_fcm_tokens WHERE user_id = $1', [userId]);
  return rows.map(r => r.fcm_token);
}

module.exports = { saveFcmToken, sendPushNotification, getUserFcmTokens };
